from PyQt5.QtCore import Qt, pyqtSignal, QDate import datetime from PyQt5.QtGui import QIcon, QFont from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, from utils import (show_error_message, show_info_message, show_confirmation_message, from database import Revenue, Invoice from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox, QLabel, QLineEdit, QTableWidget, QTableWidgetItem, QFormLayout, QTextEdit, QHeaderView, QMessageBox, QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox, QSizePolicy, QFrame) qdate_to_datetime, datetime_to_qdate, format_currency) StyledTable, StyledLabel) class RevenueDialog(QDialog): def __init__(self, parent=None, revenue=None, session=None): super().__init__(parent) self.revenue = revenue self.session = session self.init_ui() def init_ui(self): # إعداد نافذة الحوار if self.revenue: self.setWindowTitle("تعديل إيراد") else: self.setWindowTitle("إضافة إيراد جديد") self.setMinimumWidth(400) # إنشاء النموذج form_layout = QFormLayout() # حقل العنوان self.title_edit = QLineEdit() self.title_edit.setStyleSheet(UnifiedStyles.get_input_style()) if self.revenue: self.title_edit.setText(self.revenue.title) form_layout.addRow("العنوان:", self.title_edit) # حقل المبلغ self.amount_edit = QDoubleSpinBox() self.amount_edit.setRange(0, 1000000) self.amount_edit.setDecimals(0) # بدون كسور عشرية self.amount_edit.setSingleStep(100) if self.revenue: self.amount_edit.setValue(self.revenue.amount) form_layout.addRow("المبلغ:", self.amount_edit) # حقل التاريخ self.date_edit = QDateEdit() self.date_edit.setCalendarPopup(True) self.date_edit.setDate(QDate.currentDate()) if self.revenue and self.revenue.date: self.date_edit.setDate(datetime_to_qdate(self.revenue.date)) form_layout.addRow("التاريخ:", self.date_edit) # حقل الفئة self.category_edit = QComboBox() categories = ["مبيعات", "خدمات", "استشارات", "أخرى"] self.category_edit.addItems(categories) if self.revenue and self.revenue.category: index = self.category_edit.findText(self.revenue.category) if index >= 0: self.category_edit.setCurrentIndex(index) form_layout.addRow("الفئة:", self.category_edit) # حقل الفاتورة self.invoice_combo = QComboBox() self.invoice_combo.addItem("-- بدون فاتورة --", None) # إضافة الفواتير من قاعدة البيانات if self.session: invoices = self.session.query(Invoice).all() for invoice in invoices: client_name = invoice.client.name if invoice.client else "بدون عميل" self.invoice_combo.addItem(f"{invoice.invoice_number} - {client_name}", invoice.id) # تحديد الفاتورة الحالية إذا كانت موجودة if self.revenue and self.revenue.invoice_id: index = self.invoice_combo.findData(self.revenue.invoice_id) if index >= 0: self.invoice_combo.setCurrentIndex(index) form_layout.addRow("الفاتورة:", self.invoice_combo) # حقل الملاحظات self.notes_edit = QTextEdit() if self.revenue and self.revenue.notes: self.notes_edit.setText(self.revenue.notes) form_layout.addRow("ملاحظات:", self.notes_edit) # أزرار الحفظ والإلغاء button_layout = QHBoxLayout() self.save_button = StyledButton(" حفظ", "success", "normal") self.save_button.clicked.connect(self.accept) self.cancel_button = StyledButton(" إلغاء", "secondary", "normal") self.cancel_button.clicked.connect(self.reject) button_layout.addWidget(self.save_button.button) button_layout.addWidget(self.cancel_button.button) # تجميع التخطيط النهائي main_layout = QVBoxLayout() main_layout.addLayout(form_layout) main_layout.addLayout(button_layout) self.setLayout(main_layout) def get_data(self): """الحصول على بيانات الإيراد من النموذج""" title = self.title_edit.text().strip() amount = self.amount_edit.value() date = qdate_to_datetime(self.date_edit.date()) category = self.category_edit.currentText() invoice_id = self.invoice_combo.currentData() notes = self.notes_edit.toPlainText().strip() # التحقق من صحة البيانات if not title: show_error_message("خطأ", "يجب إدخال عنوان الإيراد") return None if amount <= 0: show_error_message("خطأ", "يجب أن يكون المبلغ أكبر من صفر") return None return { 'title': title, 'amount': amount, 'date': date, 'category': category, 'invoice_id': invoice_id, 'notes': notes } class RevenuesWidget(QWidget): def __init__(self, session): super().__init__() self.session = session self.init_ui() self.refresh_data() def init_ui(self): # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات main_layout = QVBoxLayout() main_layout.setContentsMargins(10, 10, 10, 10) main_layout.setSpacing(8) # إضافة العنوان الرئيسي المطور والمحسن مطابق للموردين title_label = QLabel(" إدارة الإيرادات المتطورة - نظام شامل ومتقدم لإدارة الإيرادات مع أدوات احترافية للبحث والتحليل والتقارير") title_label.setFont(QFont("Segoe UI", 18, QFont.Bold)) # خط أكبر وأوضح title_label.setAlignment(Qt.AlignCenter) # توسيط النص في المنتصف title_label.setStyleSheet(""" QLabel { color: #ffffff; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e40af, stop:0.2 #3b82f6, stop:0.4 #6366f1, stop:0.6 #8b5cf6, stop:0.8 #a855f7, stop:1 #c084fc); border: 3px solid #000000; border-radius: 10px; padding: 4px 10px; margin: 2px; font-weight: bold; max-height: 40px; min-height: 40px; } main_layout.addWidget(title_label) # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين top_frame = QFrame() top_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } # تخطيط أفقي واحد محسن (الطريقة القديمة) search_layout = QHBoxLayout() search_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق search_layout.setSpacing(4) # مسافات متوازنة # إنشاء حاوي عمودي للتوسيط الحقيقي top_container = QVBoxLayout() top_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط top_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط top_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف top_container.addLayout(search_layout) # إضافة مساحة فارغة أسفل للتوسيط top_container.addStretch(1) # تسمية البحث محسنة مع ألوان موحدة مطابقة للموردين search_label = QLabel(" بحث:") search_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b); border: 3px solid #7f1d1d; border-radius: 12px; min-width: 70px; max-width: 70px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c); border: 3px solid #ef4444; } search_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية self.search_edit = QLineEdit() self.search_edit.setPlaceholderText(" ابحث بالعنوان، الفئة، الفاتورة أو الملاحظات...") self.search_edit.textChanged.connect(self.filter_revenues) self.search_edit.setStyleSheet(""" QLineEdit { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; selection-background-color: #4f46e5; } QLineEdit:focus { border: 3px solid #3730a3; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f0f9ff, stop:1 #e0f2fe); } QLineEdit:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } search_button = QPushButton("") search_button.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0891b2, stop:0.5 #0e7490, stop:1 #155e75); color: #ffffff; border: 3px solid #164e63; border-radius: 12px; padding: 8px; font-size: 20px; font-weight: bold; min-width: 50px; max-width: 50px; max-height: 38px; min-height: 34px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #22d3ee, stop:0.5 #0891b2, stop:1 #0e7490); border: 3px solid #22d3ee; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75); border: 3px solid #0e7490; } search_button.clicked.connect(self.filter_revenues) search_button.setToolTip("بحث متقدم") search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed) search_button.setContentsMargins(0, 0, 0, 0) # تسمية التصفية مطورة بألوان احترافية filter_label = QLabel(" تصفية:") filter_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309); border: 3px solid #92400e; border-radius: 12px; min-width: 65px; max-width: 65px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706); border: 3px solid #fbbf24; } filter_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية # إضافة حقل تصفية حسب الفئة محسن self.category_filter = QComboBox() self.category_filter.addItem("جميع الفئات") categories = ["مبيعات", "خدمات", "استشارات", "أخرى"] self.category_filter.addItems(categories) self.category_filter.currentIndexChanged.connect(self.filter_revenues) self.category_filter.setStyleSheet(""" QComboBox { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; min-width: 120px; } QComboBox:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } QComboBox::drop-down { border: none; width: 30px; } QComboBox::down-arrow { image: none; border: 2px solid #4f46e5; width: 8px; height: 8px; border-radius: 4px; background: #4f46e5; } # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار search_layout.addWidget(search_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter) # يأخذ مساحة أكبر search_layout.addWidget(search_button, 0, Qt.AlignVCenter) search_layout.addWidget(filter_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.category_filter, 1, Qt.AlignVCenter) # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط top_frame.setLayout(top_container) # إنشاء جدول الإيرادات المتطور والمحسن self.create_advanced_revenues_table() main_layout.addWidget(top_frame) main_layout.addWidget(self.revenues_table, 1) # إعطاء الجدول أولوية في التمدد # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين bottom_frame = QFrame() bottom_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } actions_layout = QHBoxLayout() actions_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق actions_layout.setSpacing(4) # مسافة أكبر بين الأزرار لتوزيع أفضل # إنشاء حاوي عمودي للتوسيط الحقيقي bottom_container = QVBoxLayout() bottom_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط bottom_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط bottom_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف bottom_container.addLayout(actions_layout) # إضافة مساحة فارغة أسفل للتوسيط bottom_container.addStretch(1) # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة self.add_button = QPushButton(" إضافة إيراد ") self.style_advanced_button(self.add_button, 'emerald', has_menu=True) # أخضر زمردي مميز مع قائمة self.add_button.clicked.connect(self.add_revenue) self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.edit_button = QPushButton(" تعديل") self.style_advanced_button(self.edit_button, 'primary') # أزرق كلاسيكي self.edit_button.clicked.connect(self.edit_revenue) self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.delete_button = QPushButton(" حذف") self.style_advanced_button(self.delete_button, 'danger') # أحمر تحذيري self.delete_button.clicked.connect(self.delete_revenue) self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.refresh_button = QPushButton(" تحديث") self.style_advanced_button(self.refresh_button, 'modern_teal') # تصميم حديث ومتطور self.refresh_button.clicked.connect(self.refresh_data) self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # المجموعة الثانية - العمليات المتقدمة self.report_button = QPushButton(" التقارير") self.style_advanced_button(self.report_button, 'cyan') # سيان مميز للتقارير self.report_button.clicked.connect(self.generate_revenues_report) self.report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.export_button = QPushButton(" تصدير ") self.style_advanced_button(self.export_button, 'info', has_menu=True) # لون متسق مع نظام الألوان self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.statistics_button = QPushButton(" الإحصائيات") self.style_advanced_button(self.statistics_button, 'rose') # وردي للإحصائيات self.statistics_button.clicked.connect(self.show_statistics) self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إضافة ملخص الإيرادات محسن self.total_label = QLabel("إجمالي الإيرادات: 0.00") self.total_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 16px; font-weight: bold; padding: 8px 16px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #059669, stop:0.5 #047857, stop:1 #065f46); border: 3px solid #064e3b; border-radius: 12px; min-height: 34px; max-height: 38px; } self.total_label.setAlignment(Qt.AlignCenter) # إضافة الأزرار للتخطيط actions_layout.addWidget(self.add_button) actions_layout.addWidget(self.edit_button) actions_layout.addWidget(self.delete_button) actions_layout.addWidget(self.refresh_button) actions_layout.addWidget(self.report_button) actions_layout.addWidget(self.export_button) actions_layout.addWidget(self.statistics_button) actions_layout.addWidget(self.total_label) # تعيين التخطيط للإطار السفلي bottom_frame.setLayout(bottom_container) # تجميع التخطيط النهائي main_layout.addWidget(bottom_frame) self.setLayout(main_layout) def create_advanced_revenues_table(self): """إنشاء جدول الإيرادات المتطور والمحسن مطابق للموردين""" styled_table = StyledTable() self.revenues_table = styled_table.table self.revenues_table.setColumnCount(7) self.revenues_table.setHorizontalHeaderLabels(["الرقم", "العنوان", "المبلغ", "التاريخ", "الفئة", "الفاتورة", "ملاحظات"]) # تحسين عرض الأعمدة header = self.revenues_table.horizontalHeader() header.setSectionResizeMode(0, QHeaderView.Fixed) # الرقم header.setSectionResizeMode(1, QHeaderView.Stretch) # العنوان header.setSectionResizeMode(2, QHeaderView.Fixed) # المبلغ header.setSectionResizeMode(3, QHeaderView.Fixed) # التاريخ header.setSectionResizeMode(4, QHeaderView.Fixed) # الفئة header.setSectionResizeMode(5, QHeaderView.Stretch) # الفاتورة header.setSectionResizeMode(6, QHeaderView.Stretch) # الملاحظات # تحديد عرض الأعمدة الثابتة self.revenues_table.setColumnWidth(0, 80) # الرقم self.revenues_table.setColumnWidth(2, 120) # المبلغ self.revenues_table.setColumnWidth(3, 120) # التاريخ self.revenues_table.setColumnWidth(4, 100) # الفئة self.revenues_table.setSelectionBehavior(QTableWidget.SelectRows) self.revenues_table.setSelectionMode(QTableWidget.SingleSelection) self.revenues_table.setEditTriggers(QTableWidget.NoEditTriggers) self.revenues_table.setAlternatingRowColors(True) # تحسين تصميم الجدول QTableWidget { background-color: #ffffff; border: 3px solid #000000; border-radius: 8px; gridline-color: #e5e7eb; font-size: 13px; font-weight: bold; } QTableWidget::item { padding: 8px; border-bottom: 1px solid #e5e7eb; color: #000000; font-weight: bold; } QTableWidget::item:selected { background-color: #3b82f6; color: white; } QTableWidget::item:hover { background-color: #f3f4f6; } QHeaderView::section { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4f46e5, stop:1 #3730a3); color: white; padding: 8px; border: 1px solid #3730a3; font-weight: bold; font-size: 14px; } """) def refresh_data(self): # الحصول على جميع الإيرادات من قاعدة البيانات revenues = self.session.query(Revenue).all() self.populate_table(revenues) self.update_total(revenues) def populate_table(self, revenues): """ملء جدول الإيرادات بالبيانات""" self.revenues_table.setRowCount(0) for row, revenue in enumerate(revenues): self.revenues_table.insertRow(row) self.revenues_table.setItem(row, 0, QTableWidgetItem(str(revenue.id))) self.revenues_table.setItem(row, 1, QTableWidgetItem(revenue.title)) self.revenues_table.setItem(row, 2, QTableWidgetItem(format_currency(revenue.amount))) date = revenue.date.strftime("%Y-%m-%d") if revenue.date else "" self.revenues_table.setItem(row, 3, QTableWidgetItem(date)) self.revenues_table.setItem(row, 4, QTableWidgetItem(revenue.category or "")) invoice_text = "" if revenue.invoice: client_name = revenue.invoice.client.name if revenue.invoice.client else "" invoice_text = f"{revenue.invoice.invoice_number} - {client_name}" self.revenues_table.setItem(row, 5, QTableWidgetItem(invoice_text)) self.revenues_table.setItem(row, 6, QTableWidgetItem(revenue.notes or "")) def update_total(self, revenues): total = sum(revenue.amount for revenue in revenues) self.total_label.setText(f"إجمالي الإيرادات: {format_currency(total)}") def filter_revenues(self): """تصفية الإيرادات بناءً على نص البحث والفئة""" search_text = self.search_edit.text().strip().lower() category = self.category_filter.currentText() # بناء الاستعلام query = self.session.query(Revenue) # تطبيق تصفية النص if search_text: query = query.filter( Revenue.title.like(f"%{search_text}%") | Revenue.notes.like(f"%{search_text}%") ) # تطبيق تصفية الفئة if category != "جميع الفئات": query = query.filter(Revenue.category == category) # تنفيذ الاستعلام revenues = query.all() # تحديث الجدول والإجمالي self.populate_table(revenues) self.update_total(revenues) def add_revenue(self): dialog = RevenueDialog(self, session=self.session) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # إنشاء إيراد جديد في قاعدة البيانات revenue = Revenue(**data) self.session.add(revenue) self.session.commit() # إذا كان الإيراد مرتبط بفاتورة، تحديث المبلغ المدفوع للفاتورة if revenue.invoice_id: invoice = self.session.query(Invoice).get(revenue.invoice_id) if invoice: invoice.paid_amount += revenue.amount if invoice.paid_amount >= invoice.total_amount: invoice.status = 'paid' elif invoice.paid_amount > 0: invoice.status = 'partially_paid' self.session.commit() show_info_message("تم", "تمت إضافة الإيراد بنجاح") self.refresh_data() def edit_revenue(self): """تعديل بيانات إيراد""" selected_row = self.revenues_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار إيراد من القائمة") return revenue_id = int(self.revenues_table.item(selected_row, 0).text()) revenue = self.session.query(Revenue).get(revenue_id) if not revenue: show_error_message("خطأ", "لم يتم العثور على الإيراد") return # حفظ البيانات القديمة للإيراد old_amount = revenue.amount old_invoice_id = revenue.invoice_id dialog = RevenueDialog(self, revenue, self.session) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # تحديث بيانات الإيراد for key, value in data.items(): setattr(revenue, key, value) # إذا تغير المبلغ أو الفاتورة، تحديث المبلغ المدفوع للفواتير المتأثرة if old_invoice_id != revenue.invoice_id or old_amount != revenue.amount: # إذا كان هناك فاتورة قديمة، تحديث المبلغ المدفوع لها if old_invoice_id: old_invoice = self.session.query(Invoice).get(old_invoice_id) if old_invoice: old_invoice.paid_amount -= old_amount if old_invoice.paid_amount <= 0: old_invoice.paid_amount = 0 old_invoice.status = 'pending' elif old_invoice.paid_amount < old_invoice.total_amount: old_invoice.status = 'partially_paid' # إذا كان هناك فاتورة جديدة، تحديث المبلغ المدفوع لها if revenue.invoice_id: new_invoice = self.session.query(Invoice).get(revenue.invoice_id) if new_invoice: new_invoice.paid_amount += revenue.amount if new_invoice.paid_amount >= new_invoice.total_amount: new_invoice.status = 'paid' elif new_invoice.paid_amount > 0: new_invoice.status = 'partially_paid' self.session.commit() show_info_message("تم", "تم تحديث بيانات الإيراد بنجاح") self.refresh_data() def delete_revenue(self): selected_row = self.revenues_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار إيراد من القائمة") return revenue_id = int(self.revenues_table.item(selected_row, 0).text()) revenue = self.session.query(Revenue).get(revenue_id) if not revenue: show_error_message("خطأ", "لم يتم العثور على الإيراد") return # طلب تأكيد الحذف if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف الإيراد '{revenue.title}'؟"): # إذا كان الإيراد مرتبط بفاتورة، تحديث المبلغ المدفوع للفاتورة if revenue.invoice_id: invoice = self.session.query(Invoice).get(revenue.invoice_id) if invoice: invoice.paid_amount -= revenue.amount if invoice.paid_amount <= 0: invoice.paid_amount = 0 invoice.status = 'pending' elif invoice.paid_amount < invoice.total_amount: invoice.status = 'partially_paid' self.session.delete(revenue) self.session.commit() show_info_message("تم", "تم حذف الإيراد بنجاح") self.refresh_data() def generate_revenues_report(self): """إنشاء تقرير الإيرادات""" show_info_message("تقرير الإيرادات", "سيتم إضافة ميزة التقارير قريباً") def show_statistics(self): show_info_message("إحصائيات الإيرادات", "سيتم إضافة ميزة الإحصائيات قريباً") def style_advanced_button(self, button, button_type, has_menu=False): """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة""" try: # تحديد الألوان المتنوعة والمميزة حسب نوع الزر colors = { 'primary': { 'bg_start': '#1e3a8a', 'bg_mid': '#3b82f6', 'bg_end': '#1d4ed8', 'bg_bottom': '#1e40af', 'hover_start': '#3b82f6', 'hover_mid': '#60a5fa', 'hover_end': '#2563eb', 'hover_bottom': '#1d4ed8', 'hover_border': '#2563eb', 'pressed_start': '#1e40af', 'pressed_mid': '#1d4ed8', 'pressed_end': '#1e3a8a', 'pressed_bottom': '#172554', 'pressed_border': '#1e3a8a', 'border': '#1d4ed8', 'text': '#ffffff' }, 'emerald': { 'bg_start': '#064e3b', 'bg_mid': '#10b981', 'bg_end': '#059669', 'bg_bottom': '#022c22', 'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#059669', 'hover_bottom': '#064e3b', 'hover_border': '#059669', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b', 'pressed_end': '#014737', 'pressed_bottom': '#014737', 'pressed_border': '#064e3b', 'border': '#059669', 'text': '#ffffff' }, 'danger': { 'bg_start': '#991b1b', 'bg_mid': '#ef4444', 'bg_end': '#dc2626', 'bg_bottom': '#7f1d1d', 'hover_start': '#ef4444', 'hover_mid': '#f87171', 'hover_end': '#dc2626', 'hover_bottom': '#991b1b', 'hover_border': '#dc2626', 'pressed_start': '#7f1d1d', 'pressed_mid': '#991b1b', 'pressed_end': '#450a0a', 'pressed_bottom': '#450a0a', 'pressed_border': '#991b1b', 'border': '#dc2626', 'text': '#ffffff' }, 'info': { 'bg_start': '#0891b2', 'bg_mid': '#0ea5e9', 'bg_end': '#0284c7', 'bg_bottom': '#075985', 'hover_start': '#0ea5e9', 'hover_mid': '#38bdf8', 'hover_end': '#0891b2', 'hover_bottom': '#0284c7', 'hover_border': '#0891b2', 'pressed_start': '#0284c7', 'pressed_mid': '#0891b2', 'pressed_end': '#075985', 'pressed_bottom': '#0c4a6e', 'pressed_border': '#075985', 'border': '#0891b2', 'text': '#ffffff' }, 'modern_teal': { 'bg_start': '#0d9488', 'bg_mid': '#14b8a6', 'bg_end': '#0f766e', 'bg_bottom': '#134e4a', 'hover_start': '#14b8a6', 'hover_mid': '#2dd4bf', 'hover_end': '#0d9488', 'hover_bottom': '#0f766e', 'hover_border': '#14b8a6', 'pressed_start': '#0f766e', 'pressed_mid': '#0d9488', 'pressed_end': '#134e4a', 'pressed_bottom': '#042f2e', 'pressed_border': '#0f766e', 'border': '#0d9488', 'text': '#ffffff' }, 'cyan': { 'bg_start': '#0891b2', 'bg_mid': '#06b6d4', 'bg_end': '#0e7490', 'bg_bottom': '#164e63', 'hover_start': '#06b6d4', 'hover_mid': '#22d3ee', 'hover_end': '#0891b2', 'hover_bottom': '#0e7490', 'hover_border': '#06b6d4', 'pressed_start': '#0e7490', 'pressed_mid': '#0891b2', 'pressed_end': '#164e63', 'pressed_bottom': '#083344', 'pressed_border': '#0e7490', 'border': '#0891b2', 'text': '#ffffff' }, 'rose': { 'bg_start': '#be185d', 'bg_mid': '#ec4899', 'bg_end': '#be185d', 'bg_bottom': '#9d174d', 'hover_start': '#ec4899', 'hover_mid': '#f472b6', 'hover_end': '#be185d', 'hover_bottom': '#be185d', 'hover_border': '#ec4899', 'pressed_start': '#9d174d', 'pressed_mid': '#be185d', 'pressed_end': '#831843', 'pressed_bottom': '#500724', 'pressed_border': '#9d174d', 'border': '#be185d', 'text': '#ffffff' } } # الحصول على ألوان الزر المحدد color_scheme = colors.get(button_type, colors['primary']) # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else "" # تطبيق التصميم المتطور QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['bg_start']}, stop:0.3 {color_scheme['bg_mid']}, stop:0.7 {color_scheme['bg_end']}, stop:1 {color_scheme['bg_bottom']}); color: {color_scheme['text']}; border: 2px solid {color_scheme['border']}; border-radius: 8px; padding: 8px 16px; font-weight: bold; font-size: 13px; min-height: 38px; max-height: 38px; min-width: 100px; text-align: center; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['hover_start']}, stop:0.3 {color_scheme['hover_mid']}, stop:0.7 {color_scheme['hover_end']}, stop:1 {color_scheme['hover_bottom']}); border: 2px solid {color_scheme['hover_border']}; }} QPushButton:pressed {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['pressed_start']}, stop:0.3 {color_scheme['pressed_mid']}, stop:0.7 {color_scheme['pressed_end']}, stop:1 {color_scheme['pressed_bottom']}); border: 2px solid {color_scheme['pressed_border']}; }} {menu_indicator} """ button.setStyleSheet(style) print(f" تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}") except Exception as e: print(f" خطأ في تطبيق التصميم على الزر: {str(e)}")