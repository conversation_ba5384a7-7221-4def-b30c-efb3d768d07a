# Note: The first part of this file can be modified in place, but the latter # part is autogenerated by the boilerplate.py script. """ `matplotlib.pyplot` is a state-based interface to matplotlib. It provides an implicit, MATLAB-like, way of plotting. It also opens figures on your screen, and acts as the figure GUI manager. pyplot is mainly intended for interactive plots and simple cases of programmatic plot generation:: import numpy as np import matplotlib.pyplot as plt x = np.arange(0, 5, 0.1) y = np.sin(x) plt.plot(x, y) plt.show() The explicit object-oriented API is recommended for complex plots, though pyplot is still usually used to create the figure and often the Axes in the figure. See `.pyplot.figure`, `.pyplot.subplots`, and `.pyplot.subplot_mosaic` to create figures, and :doc:`Axes API </api/axes_api>` for the plotting methods on an Axes:: import numpy as np import matplotlib.pyplot as plt x = np.arange(0, 5, 0.1) y = np.sin(x) fig, ax = plt.subplots() ax.plot(x, y) plt.show() See :ref:`api_interfaces` for an explanation of the tradeoffs between the implicit and explicit interfaces. """ # fmt: off from __future__ import annotations from contextlib import AbstractContextManager, ExitStack from enum import Enum import functools import importlib import inspect import logging import sys import threading import time from typing import TYPE_CHECKING, cast, overload from cycler import cycler # noqa: F401 import matplotlib import matplotlib.colorbar import matplotlib.image from matplotlib import _api # Re-exported (import x as x) for typing. from matplotlib import get_backend as get_backend, rcParams as rcParams from matplotlib import cm as cm # noqa: F401 from matplotlib import style as style # noqa: F401 from matplotlib import _pylab_helpers from matplotlib import interactive # noqa: F401 from matplotlib import cbook from matplotlib import _docstring from matplotlib.backend_bases import ( FigureCanvasBase, FigureManagerBase, MouseButton) from matplotlib.figure import Figure, FigureBase, figaspect from matplotlib.gridspec import GridSpec, SubplotSpec from matplotlib import rcsetup, rcParamsDefault, rcParamsOrig from matplotlib.artist import Artist from matplotlib.axes import Axes from matplotlib.axes import Subplot # noqa: F401 from matplotlib.backends import BackendFilter, backend_registry from matplotlib.projections import PolarAxes from matplotlib.colorizer import _ColorizerInterface, ColorizingArtist, Colorizer from matplotlib import mlab # for detrend_none, window_hanning from matplotlib.scale import get_scale_names # noqa: F401 from matplotlib.cm import _colormaps from matplotlib.colors import _color_sequences, Colormap import numpy as np if TYPE_CHECKING: from collections.abc import Callable, Hashable, Iterable, Sequence import datetime import pathlib import os from typing import Any, BinaryIO, Literal, TypeVar from typing_extensions import ParamSpec import PIL.Image from numpy.typing import ArrayLike import matplotlib.axes import matplotlib.artist import matplotlib.backend_bases from matplotlib.axis import Tick from matplotlib.axes._base import _AxesBase from matplotlib.backend_bases import Event from matplotlib.cm import ScalarMappable from matplotlib.contour import ContourSet, QuadContourSet from matplotlib.collections import ( Collection, FillBetweenPolyCollection, LineCollection, PolyCollection, PathCollection, EventCollection, QuadMesh, ) from matplotlib.colorbar import Colorbar from matplotlib.container import ( BarContainer, ErrorbarContainer, StemContainer, ) from matplotlib.figure import SubFigure from matplotlib.legend import Legend from matplotlib.mlab import GaussianKDE from matplotlib.image import AxesImage, FigureImage from matplotlib.patches import FancyArrow, StepPatch, Wedge from matplotlib.quiver import Barbs, Quiver, QuiverKey from matplotlib.scale import ScaleBase from matplotlib.typing import ( ColorType, CoordsType, HashableList, LineStyleType, MarkerType, ) from matplotlib.widgets import SubplotTool _P = ParamSpec('_P') _R = TypeVar('_R') _T = TypeVar('_T') # We may not need the following imports here: from matplotlib.colors import Normalize from matplotlib.lines import Line2D, AxLine from matplotlib.text import Text, Annotation from matplotlib.patches import Arrow, Circle, Rectangle # noqa: F401 from matplotlib.patches import Polygon from matplotlib.widgets import Button, Slider, Widget # noqa: F401 from .ticker import ( # noqa: F401 TickHelper, Formatter, FixedFormatter, NullFormatter, FuncFormatter, FormatStrFormatter, ScalarFormatter, LogFormatter, LogFormatterExponent, LogFormatterMathtext, Locator, IndexLocator, FixedLocator, NullLocator, LinearLocator, LogLocator, AutoLocator, MultipleLocator, MaxNLocator) _log = logging.getLogger(__name__) # Explicit rename instead of import-as for typing's sake. colormaps = _colormaps color_sequences = _color_sequences @overload def _copy_docstring_and_deprecators( method: Any, func: Literal[None] = None ) -> Callable[[Callable[_P, _R]], Callable[_P, _R]]: ... @overload def _copy_docstring_and_deprecators( method: Any, func: Callable[_P, _R]) -> Callable[_P, _R]: ... def _copy_docstring_and_deprecators( method: Any, func: Callable[_P, _R] | None = None ) -> Callable[[Callable[_P, _R]], Callable[_P, _R]] | Callable[_P, _R]: if func is None: return cast('Callable[[Callable[_P, _R]], Callable[_P, _R]]', functools.partial(_copy_docstring_and_deprecators, method)) decorators: list[Callable[[Callable[_P, _R]], Callable[_P, _R]]] = [ _docstring.copy(method) ] # Check whether the definition of *method* includes @_api.rename_parameter # or @_api.make_keyword_only decorators; if so, propagate them to the # pyplot wrapper as well. while hasattr(method, "__wrapped__"): potential_decorator = _api.deprecation.DECORATORS.get(method) if potential_decorator: decorators.append(potential_decorator) method = method.__wrapped__ for decorator in decorators[::-1]: func = decorator(func) _add_pyplot_note(func, method) return func _NO_PYPLOT_NOTE = [ 'FigureBase._gci', # wrapped_func is private '_AxesBase._sci', # wrapped_func is private 'Artist.findobj', # not a standard pyplot wrapper because it does not operate # on the current Figure / Axes. Explanation of relation would # be more complex and is not too important. ] def _add_pyplot_note(func, wrapped_func): """ Add a note to the docstring of *func* that it is a pyplot wrapper. The note is added to the "Notes" section of the docstring. If that does not exist, a "Notes" section is created. In numpydoc, the "Notes" section is the third last possible section, only potentially followed by "References" and "Examples". """ if not func.__doc__: return # nothing to do qualname = wrapped_func.__qualname__ if qualname in _NO_PYPLOT_NOTE: return wrapped_func_is_method = True if "." not in qualname: # method qualnames are prefixed by the class and ".", e.g. "Axes.plot" wrapped_func_is_method = False link = f"{wrapped_func.__module__}.{qualname}" elif qualname.startswith("Axes."): # e.g. "Axes.plot" link = ".axes." + qualname elif qualname.startswith("_AxesBase."): # e.g. "_AxesBase.set_xlabel" link = ".axes.Axes" + qualname[9:] elif qualname.startswith("Figure."): # e.g. "Figure.figimage" link = "." + qualname elif qualname.startswith("FigureBase."): # e.g. "FigureBase.gca" link = ".Figure" + qualname[10:] elif qualname.startswith("FigureCanvasBase."): # "FigureBaseCanvas.mpl_connect" link = "." + qualname else: raise RuntimeError(f"Wrapped method from unexpected class: {qualname}") if wrapped_func_is_method: message = f"This is the :ref:`pyplot wrapper <pyplot_interface>` for `{link}`." else: message = f"This is equivalent to `{link}`." # Find the correct insert position: # - either we already have a "Notes" section into which we can insert # - or we create one before the next present section. Note that in numpydoc, the # "Notes" section is the third last possible section, only potentially followed # by "References" and "Examples". # - or we append a new "Notes" section at the end. doc = inspect.cleandoc(func.__doc__) if "\nNotes\n-----" in doc: before, after = doc.split("\nNotes\n-----", 1) elif (index := doc.find("\nReferences\n----------")) != -1: before, after = doc[:index], doc[index:] elif (index := doc.find("\nExamples\n--------")) != -1: before, after = doc[:index], doc[index:] else: # No "Notes", "References", or "Examples" --> append to the end. before = doc + "\n" after = "" func.__doc__ = f"{before}\nNotes\n-----\n\n.. note::\n\n {message}\n{after}" ## Global ## # The state controlled by {,un}install_repl_displayhook(). _ReplDisplayHook = Enum("_ReplDisplayHook", ["NONE", "PLAIN", "IPYTHON"]) _REPL_DISPLAYHOOK = _ReplDisplayHook.NONE def _draw_all_if_interactive() -> None: if matplotlib.is_interactive(): draw_all() def install_repl_displayhook() -> None: """ Connect to the display hook of the current shell. The display hook gets called when the read-evaluate-print-loop (REPL) of the shell has finished the execution of a command. We use this callback to be able to automatically update a figure in interactive mode. This works both with IPython and with vanilla python shells. """ global _REPL_DISPLAYHOOK if _REPL_DISPLAYHOOK is _ReplDisplayHook.IPYTHON: return # See if we have IPython hooks around, if so use them. # Use ``sys.modules.get(name)`` rather than ``name in sys.modules`` as # entries can also have been explicitly set to None. mod_ipython = sys.modules.get("IPython") if not mod_ipython: _REPL_DISPLAYHOOK = _ReplDisplayHook.PLAIN return ip = mod_ipython.get_ipython() if not ip: _REPL_DISPLAYHOOK = _ReplDisplayHook.PLAIN return ip.events.register("post_execute", _draw_all_if_interactive) _REPL_DISPLAYHOOK = _ReplDisplayHook.IPYTHON if mod_ipython.version_info[:2] < (8, 24): # Use of backend2gui is not needed for IPython >= 8.24 as that functionality # has been moved to Matplotlib. # This code can be removed when Python 3.12, the latest version supported by # IPython < 8.24, reaches end-of-life in late 2028. from IPython.core.pylabtools import backend2gui ipython_gui_name = backend2gui.get(get_backend()) else: _, ipython_gui_name = backend_registry.resolve_backend(get_backend()) # trigger IPython's eventloop integration, if available if ipython_gui_name: ip.enable_gui(ipython_gui_name) def uninstall_repl_displayhook() -> None: """Disconnect from the display hook of the current shell.""" global _REPL_DISPLAYHOOK if _REPL_DISPLAYHOOK is _ReplDisplayHook.IPYTHON: from IPython import get_ipython ip = get_ipython() ip.events.unregister("post_execute", _draw_all_if_interactive) _REPL_DISPLAYHOOK = _ReplDisplayHook.NONE draw_all = _pylab_helpers.Gcf.draw_all # Ensure this appears in the pyplot docs. @_copy_docstring_and_deprecators(matplotlib.set_loglevel) def set_loglevel(*args, **kwargs) -> None: return matplotlib.set_loglevel(*args, **kwargs) @_copy_docstring_and_deprecators(Artist.findobj) def findobj( o: Artist | None = None, match: Callable[[Artist], bool] | type[Artist] | None = None, include_self: bool = True ) -> list[Artist]: if o is None: o = gcf() return o.findobj(match, include_self=include_self) _backend_mod: type[matplotlib.backend_bases._Backend] | None = None def _get_backend_mod() -> type[matplotlib.backend_bases._Backend]: """ Ensure that a backend is selected and return it. This is currently private, but may be made public in the future. """ if _backend_mod is None: # Use rcParams._get("backend") to avoid going through the fallback # logic (which will (re)import pyplot and then call switch_backend if # we need to resolve the auto sentinel) switch_backend(rcParams._get("backend")) return cast(type[matplotlib.backend_bases._Backend], _backend_mod) def switch_backend(newbackend: str) -> None: """ Set the pyplot backend. Switching to an interactive backend is possible only if no event loop for another interactive backend has started. Switching to and from non-interactive backends is always possible. If the new backend is different than the current backend then all open Figures will be closed via ``plt.close('all')``. Parameters ---------- newbackend : str The case-insensitive name of the backend to use. """ global _backend_mod # make sure the init is pulled up so we can assign to it later import matplotlib.backends if newbackend is rcsetup._auto_backend_sentinel: current_framework = cbook._get_running_interactive_framework() if (current_framework and (backend := backend_registry.backend_for_gui_framework( current_framework))): candidates = [backend] else: candidates = [] candidates += [ "macosx", "qtagg", "gtk4agg", "gtk3agg", "tkagg", "wxagg"] # Don't try to fallback on the cairo-based backends as they each have # an additional dependency (pycairo) over the agg-based backend, and # are of worse quality. for candidate in candidates: try: switch_backend(candidate) except ImportError: continue else: rcParamsOrig['backend'] = candidate return else: # Switching to Agg should always succeed; if it doesn't, let the # exception propagate out. switch_backend("agg") rcParamsOrig["backend"] = "agg" return old_backend = rcParams._get('backend') # get without triggering backend resolution module = backend_registry.load_backend_module(newbackend) canvas_class = module.FigureCanvas required_framework = canvas_class.required_interactive_framework if required_framework is not None: current_framework = cbook._get_running_interactive_framework() if (current_framework and required_framework and current_framework != required_framework): raise ImportError( "Cannot load backend {!r} which requires the {!r} interactive " "framework, as {!r} is currently running".format( newbackend, required_framework, current_framework)) # Load the new_figure_manager() and show() functions from the backend. # Classically, backends can directly export these functions. This should # keep working for backcompat. new_figure_manager = getattr(module, "new_figure_manager", None) show = getattr(module, "show", None) # In that classical approach, backends are implemented as modules, but # "inherit" default method implementations from backend_bases._Backend. # This is achieved by creating a "class" that inherits from # backend_bases._Backend and whose body is filled with the module globals. class backend_mod(matplotlib.backend_bases._Backend): locals().update(vars(module)) # However, the newer approach for defining new_figure_manager and # show is to derive them from canvas methods. In that case, also # update backend_mod accordingly; also, per-backend customization of # draw_if_interactive is disabled. if new_figure_manager is None: def new_figure_manager_given_figure(num, figure): return canvas_class.new_manager(figure, num) def new_figure_manager(num, *args, FigureClass=Figure, **kwargs): fig = FigureClass(*args, **kwargs) return new_figure_manager_given_figure(num, fig) def draw_if_interactive() -> None: if matplotlib.is_interactive(): manager = _pylab_helpers.Gcf.get_active() if manager: manager.canvas.draw_idle() backend_mod.new_figure_manager_given_figure = ( # type: ignore[method-assign] new_figure_manager_given_figure) backend_mod.new_figure_manager = ( # type: ignore[method-assign] new_figure_manager) backend_mod.draw_if_interactive = ( # type: ignore[method-assign] draw_if_interactive) # If the manager explicitly overrides pyplot_show, use it even if a global # show is already present, as the latter may be here for backcompat. manager_class = getattr(canvas_class, "manager_class", None) # We can't compare directly manager_class.pyplot_show and FMB.pyplot_show because # pyplot_show is a classmethod so the above constructs are bound classmethods, and # thus always different (being bound to different classes). We also have to use # getattr_static instead of vars as manager_class could have no __dict__. manager_pyplot_show = inspect.getattr_static(manager_class, "pyplot_show", None) base_pyplot_show = inspect.getattr_static(FigureManagerBase, "pyplot_show", None) if (show is None or (manager_pyplot_show is not None and manager_pyplot_show != base_pyplot_show)): if not manager_pyplot_show: raise ValueError( f"Backend {newbackend} defines neither FigureCanvas.manager_class nor " f"a toplevel show function") _pyplot_show = cast('Any', manager_class).pyplot_show backend_mod.show = _pyplot_show # type: ignore[method-assign] _log.debug("Loaded backend %s version %s.", newbackend, backend_mod.backend_version) if newbackend in ("ipympl", "widget"): # ipympl < 0.9.4 expects rcParams["backend"] to be the fully-qualified backend # name "module://ipympl.backend_nbagg" not short names "ipympl" or "widget". import importlib.metadata as im from matplotlib import _parse_to_version_info # type: ignore[attr-defined] try: module_version = im.version("ipympl") if _parse_to_version_info(module_version) < (0, 9, 4): newbackend = "module://ipympl.backend_nbagg" except im.PackageNotFoundError: pass rcParams['backend'] = rcParamsDefault['backend'] = newbackend _backend_mod = backend_mod for func_name in ["new_figure_manager", "draw_if_interactive", "show"]: globals()[func_name].__signature__ = inspect.signature( getattr(backend_mod, func_name)) # Need to keep a global reference to the backend for compatibility reasons. # See https://github.com/matplotlib/matplotlib/issues/6092 matplotlib.backends.backend = newbackend # type: ignore[attr-defined] # Make sure the repl display hook is installed in case we become interactive. install_repl_displayhook() def _warn_if_gui_out_of_main_thread() -> None: warn = False canvas_class = cast(type[FigureCanvasBase], _get_backend_mod().FigureCanvas) if canvas_class.required_interactive_framework: if hasattr(threading, 'get_native_id'): # This compares native thread ids because even if Python-level # Thread objects match, the underlying OS thread (which is what # really matters) may be different on Python implementations with # green threads. if threading.get_native_id() != threading.main_thread().native_id: warn = True else: # Fall back to Python-level Thread if native IDs are unavailable, # mainly for PyPy. if threading.current_thread() is not threading.main_thread(): warn = True if warn: _api.warn_external( "Starting a Matplotlib GUI outside of the main thread will likely " "fail.") # This function's signature is rewritten upon backend-load by switch_backend. def new_figure_manager(*args, **kwargs): """Create a new figure manager instance.""" _warn_if_gui_out_of_main_thread() return _get_backend_mod().new_figure_manager(*args, **kwargs) # This function's signature is rewritten upon backend-load by switch_backend. def draw_if_interactive(*args, **kwargs): """ Redraw the current figure if in interactive mode. .. warning:: End users will typically not have to call this function because the the interactive mode takes care of this. """ return _get_backend_mod().draw_if_interactive(*args, **kwargs) # This function's signature is rewritten upon backend-load by switch_backend. def show(*args, **kwargs) -> None: """ Display all open figures. Parameters ---------- block : bool, optional Whether to wait for all figures to be closed before returning. If `True` block and run the GUI main loop until all figure windows are closed. If `False` ensure that all figure windows are displayed and return immediately. In this case, you are responsible for ensuring that the event loop is running to have responsive figures. Defaults to True in non-interactive mode and to False in interactive mode (see `.pyplot.isinteractive`). See Also -------- ion : Enable interactive mode, which shows / updates the figure after every plotting command, so that calling ``show()`` is not necessary. ioff : Disable interactive mode. savefig : Save the figure to an image file instead of showing it on screen. Notes ----- **Saving figures to file and showing a window at the same time** If you want an image file as well as a user interface window, use `.pyplot.savefig` before `.pyplot.show`. At the end of (a blocking) ``show()`` the figure is closed and thus unregistered from pyplot. Calling `.pyplot.savefig` afterwards would save a new and thus empty figure. This limitation of command order does not apply if the show is non-blocking or if you keep a reference to the figure and use `.Figure.savefig`. **Auto-show in jupyter notebooks** The jupyter backends (activated via ``%matplotlib inline``, ``%matplotlib notebook``, or ``%matplotlib widget``), call ``show()`` at the end of every cell by default. Thus, you usually don't have to call it explicitly there. """ _warn_if_gui_out_of_main_thread() return _get_backend_mod().show(*args, **kwargs) def isinteractive() -> bool: """ Return whether plots are updated after every plotting command. The interactive mode is mainly useful if you build plots from the command line and want to see the effect of each command while you are building the figure. In interactive mode: - newly created figures will be shown immediately; - figures will automatically redraw on change; - `.pyplot.show` will not block by default. In non-interactive mode: - newly created figures and changes to figures will not be reflected until explicitly asked to be; - `.pyplot.show` will block by default. See Also -------- ion : Enable interactive mode. ioff : Disable interactive mode. show : Show all figures (and maybe block). pause : Show all figures, and block for a time. """ return matplotlib.is_interactive() # Note: The return type of ioff being AbstractContextManager # instead of ExitStack is deliberate. # See https://github.com/matplotlib/matplotlib/issues/27659 # and https://github.com/matplotlib/matplotlib/pull/27667 for more info. def ioff() -> AbstractContextManager: """ Disable interactive mode. See `.pyplot.isinteractive` for more details. See Also -------- ion : Enable interactive mode. isinteractive : Whether interactive mode is enabled. show : Show all figures (and maybe block). pause : Show all figures, and block for a time. Notes ----- For a temporary change, this can be used as a context manager:: # if interactive mode is on # then figures will be shown on creation plt.ion() # This figure will be shown immediately fig = plt.figure() with plt.ioff(): # interactive mode will be off # figures will not automatically be shown fig2 = plt.figure() # ... To enable optional usage as a context manager, this function returns a context manager object, which is not intended to be stored or accessed by the user. """ stack = ExitStack() stack.callback(ion if isinteractive() else ioff) matplotlib.interactive(False) uninstall_repl_displayhook() return stack # Note: The return type of ion being AbstractContextManager # instead of ExitStack is deliberate. # See https://github.com/matplotlib/matplotlib/issues/27659 # and https://github.com/matplotlib/matplotlib/pull/27667 for more info. def ion() -> AbstractContextManager: """ Enable interactive mode. See `.pyplot.isinteractive` for more details. See Also -------- ioff : Disable interactive mode. isinteractive : Whether interactive mode is enabled. show : Show all figures (and maybe block). pause : Show all figures, and block for a time. Notes ----- For a temporary change, this can be used as a context manager:: # if interactive mode is off # then figures will not be shown on creation plt.ioff() # This figure will not be shown immediately fig = plt.figure() with plt.ion(): # interactive mode will be on # figures will automatically be shown fig2 = plt.figure() # ... To enable optional usage as a context manager, this function returns a context manager object, which is not intended to be stored or accessed by the user. """ stack = ExitStack() stack.callback(ion if isinteractive() else ioff) matplotlib.interactive(True) install_repl_displayhook() return stack def pause(interval: float) -> None: """ Run the GUI event loop for *interval* seconds. If there is an active figure, it will be updated and displayed before the pause, and the GUI event loop (if any) will run during the pause. This can be used for crude animation. For more complex animation use :mod:`matplotlib.animation`. If there is no active figure, sleep for *interval* seconds instead. See Also -------- matplotlib.animation : Proper animations show : Show all figures and optional block until all figures are closed. """ manager = _pylab_helpers.Gcf.get_active() if manager is not None: canvas = manager.canvas if canvas.figure.stale: canvas.draw_idle() show(block=False) canvas.start_event_loop(interval) else: time.sleep(interval) @_copy_docstring_and_deprecators(matplotlib.rc) def rc(group: str, **kwargs) -> None: matplotlib.rc(group, **kwargs) @_copy_docstring_and_deprecators(matplotlib.rc_context) def rc_context( rc: dict[str, Any] | None = None, fname: str | pathlib.Path | os.PathLike | None = None, ) -> AbstractContextManager[None]: return matplotlib.rc_context(rc, fname) @_copy_docstring_and_deprecators(matplotlib.rcdefaults) def rcdefaults() -> None: matplotlib.rcdefaults() if matplotlib.is_interactive(): draw_all() # getp/get/setp are explicitly reexported so that they show up in pyplot docs. @_copy_docstring_and_deprecators(matplotlib.artist.getp) def getp(obj, *args, **kwargs): return matplotlib.artist.getp(obj, *args, **kwargs) @_copy_docstring_and_deprecators(matplotlib.artist.get) def get(obj, *args, **kwargs): return matplotlib.artist.get(obj, *args, **kwargs) @_copy_docstring_and_deprecators(matplotlib.artist.setp) def setp(obj, *args, **kwargs): return matplotlib.artist.setp(obj, *args, **kwargs) def xkcd( scale: float = 1, length: float = 100, randomness: float = 2 ) -> ExitStack: """ Turn on `xkcd <https://xkcd.com/>`_ sketch-style drawing mode. This will only have an effect on things drawn after this function is called. For best results, install the `xkcd script <https://github.com/ipython/xkcd-font/>`_ font; xkcd fonts are not packaged with Matplotlib. Parameters ---------- scale : float, optional The amplitude of the wiggle perpendicular to the source line. length : float, optional The length of the wiggle along the line. randomness : float, optional The scale factor by which the length is shrunken or expanded. Notes ----- This function works by a number of rcParams, so it will probably override others you have set before. If you want the effects of this function to be temporary, it can be used as a context manager, for example:: with plt.xkcd(): # This figure will be in XKCD-style fig1 = plt.figure() # ... # This figure will be in regular style fig2 = plt.figure() """ # This cannot be implemented in terms of contextmanager() or rc_context() # because this needs to work as a non-contextmanager too. if rcParams['text.usetex']: raise RuntimeError( "xkcd mode is not compatible with text.usetex = True") stack = ExitStack() stack.callback(rcParams._update_raw, rcParams.copy()) # type: ignore[arg-type] from matplotlib import patheffects rcParams.update({ 'font.family': ['xkcd', 'xkcd Script', 'Comic Neue', 'Comic Sans MS'], 'font.size': 14.0, 'path.sketch': (scale, length, randomness), 'path.effects': [ patheffects.withStroke(linewidth=4, foreground="w")], 'axes.linewidth': 1.5, 'lines.linewidth': 2.0, 'figure.facecolor': 'white', 'grid.linewidth': 0.0, 'axes.grid': False, 'axes.unicode_minus': False, 'axes.edgecolor': 'black', 'xtick.major.size': 8, 'xtick.major.width': 3, 'ytick.major.size': 8, 'ytick.major.width': 3, }) return stack ## Figures ## def figure( # autoincrement if None, else integer from 1-N num: int | str | Figure | SubFigure | None = None, # defaults to rc figure.figsize figsize: ArrayLike | None = None, # defaults to rc figure.dpi dpi: float | None = None, *, # defaults to rc figure.facecolor facecolor: ColorType | None = None, # defaults to rc figure.edgecolor edgecolor: ColorType | None = None, frameon: bool = True, FigureClass: type[Figure] = Figure, clear: bool = False, **kwargs ) -> Figure: """ Create a new figure, or activate an existing figure. Parameters ---------- num : int or str or `.Figure` or `.SubFigure`, optional A unique identifier for the figure. If a figure with that identifier already exists, this figure is made active and returned. An integer refers to the ``Figure.number`` attribute, a string refers to the figure label. If there is no figure with the identifier or *num* is not given, a new figure is created, made active and returned. If *num* is an int, it will be used for the ``Figure.number`` attribute, otherwise, an auto-generated integer value is used (starting at 1 and incremented for each new figure). If *num* is a string, the figure label and the window title is set to this value. If num is a ``SubFigure``, its parent ``Figure`` is activated. figsize : (float, float), default: :rc:`figure.figsize` Width, height in inches. dpi : float, default: :rc:`figure.dpi` The resolution of the figure in dots-per-inch. facecolor : :mpltype:`color`, default: :rc:`figure.facecolor` The background color. edgecolor : :mpltype:`color`, default: :rc:`figure.edgecolor` The border color. frameon : bool, default: True If False, suppress drawing the figure frame. FigureClass : subclass of `~matplotlib.figure.Figure` If set, an instance of this subclass will be created, rather than a plain `.Figure`. clear : bool, default: False If True and the figure already exists, then it is cleared. layout : {'constrained', 'compressed', 'tight', 'none', `.LayoutEngine`, None}, \ default: None The layout mechanism for positioning of plot elements to avoid overlapping Axes decorations (labels, ticks, etc). Note that layout managers can measurably slow down figure display. - 'constrained': The constrained layout solver adjusts Axes sizes to avoid overlapping Axes decorations. Can handle complex plot layouts and colorbars, and is thus recommended. See :ref:`constrainedlayout_guide` for examples. - 'compressed': uses the same algorithm as 'constrained', but removes extra space between fixed-aspect-ratio Axes. Best for simple grids of Axes. - 'tight': Use the tight layout mechanism. This is a relatively simple algorithm that adjusts the subplot parameters so that decorations do not overlap. See `.Figure.set_tight_layout` for further details. - 'none': Do not use a layout engine. - A `.LayoutEngine` instance. Builtin layout classes are `.ConstrainedLayoutEngine` and `.TightLayoutEngine`, more easily accessible by 'constrained' and 'tight'. Passing an instance allows third parties to provide their own layout engine. If not given, fall back to using the parameters *tight_layout* and *constrained_layout*, including their config defaults :rc:`figure.autolayout` and :rc:`figure.constrained_layout.use`. **kwargs Additional keyword arguments are passed to the `.Figure` constructor. Returns ------- `~matplotlib.figure.Figure` Notes ----- A newly created figure is passed to the `~.FigureCanvasBase.new_manager` method or the `new_figure_manager` function provided by the current backend, which install a canvas and a manager on the figure. Once this is done, :rc:`figure.hooks` are called, one at a time, on the figure; these hooks allow arbitrary customization of the figure (e.g., attaching callbacks) or of associated elements (e.g., modifying the toolbar). See :doc:`/gallery/user_interfaces/mplcvd` for an example of toolbar customization. If you are creating many figures, make sure you explicitly call `.pyplot.close` on the figures you are not using, because this will enable pyplot to properly clean up the memory. `~matplotlib.rcParams` defines the default values, which can be modified in the matplotlibrc file. """ allnums = get_fignums() if isinstance(num, FigureBase): # type narrowed to `Figure | SubFigure` by combination of input and isinstance root_fig = num.get_figure(root=True) if root_fig.canvas.manager is None: raise ValueError("The passed figure is not managed by pyplot") elif (any(param is not None for param in [figsize, dpi, facecolor, edgecolor]) or not frameon or kwargs) and root_fig.canvas.manager.num in allnums: _api.warn_external( "Ignoring specified arguments in this call because figure " f"with num: {root_fig.canvas.manager.num} already exists") _pylab_helpers.Gcf.set_active(root_fig.canvas.manager) return root_fig next_num = max(allnums) + 1 if allnums else 1 fig_label = '' if num is None: num = next_num else: if (any(param is not None for param in [figsize, dpi, facecolor, edgecolor]) or not frameon or kwargs) and num in allnums: _api.warn_external( "Ignoring specified arguments in this call " f"because figure with num: {num} already exists") if isinstance(num, str): fig_label = num all_labels = get_figlabels() if fig_label not in all_labels: if fig_label == 'all': _api.warn_external("close('all') closes all existing figures.") num = next_num else: inum = all_labels.index(fig_label) num = allnums[inum] else: num = int(num) # crude validation of num argument # Type of "num" has narrowed to int, but mypy can't quite see it manager = _pylab_helpers.Gcf.get_fig_manager(num) # type: ignore[arg-type] if manager is None: max_open_warning = rcParams['figure.max_open_warning'] if len(allnums) == max_open_warning >= 1: _api.warn_external( f"More than {max_open_warning} figures have been opened. " f"Figures created through the pyplot interface " f"(`matplotlib.pyplot.figure`) are retained until explicitly " f"closed and may consume too much memory. (To control this " f"warning, see the rcParam `figure.max_open_warning`). " f"Consider using `matplotlib.pyplot.close()`.", RuntimeWarning) manager = new_figure_manager( num, figsize=figsize, dpi=dpi, facecolor=facecolor, edgecolor=edgecolor, frameon=frameon, FigureClass=FigureClass, **kwargs) fig = manager.canvas.figure if fig_label: fig.set_label(fig_label) for hookspecs in rcParams["figure.hooks"]: module_name, dotted_name = hookspecs.split(":") obj: Any = importlib.import_module(module_name) for part in dotted_name.split("."): obj = getattr(obj, part) obj(fig) _pylab_helpers.Gcf._set_new_active_manager(manager) # make sure backends (inline) that we don't ship that expect this # to be called in plotting commands to make the figure call show # still work. There is probably a better way to do this in the # FigureManager base class. draw_if_interactive() if _REPL_DISPLAYHOOK is _ReplDisplayHook.PLAIN: fig.stale_callback = _auto_draw_if_interactive if clear: manager.canvas.figure.clear() return manager.canvas.figure def _auto_draw_if_interactive(fig, val): """ An internal helper function for making sure that auto-redrawing works as intended in the plain python repl. Parameters ---------- fig : Figure A figure object which is assumed to be associated with a canvas """ if (val and matplotlib.is_interactive() and not fig.canvas.is_saving() and not fig.canvas._is_idle_drawing): # Some artists can mark themselves as stale in the middle of drawing # (e.g. axes position & tick labels being computed at draw time), but # this shouldn't trigger a redraw because the current redraw will # already take them into account. with fig.canvas._idle_draw_cntx(): fig.canvas.draw_idle() def gcf() -> Figure: """ Get the current figure. If there is currently no figure on the pyplot figure stack, a new one is created using `~.pyplot.figure()`. (To test whether there is currently a figure on the pyplot figure stack, check whether `~.pyplot.get_fignums()` is empty.) """ manager = _pylab_helpers.Gcf.get_active() if manager is not None: return manager.canvas.figure else: return figure() def fignum_exists(num: int | str) -> bool: """ Return whether the figure with the given id exists. Parameters ---------- num : int or str A figure identifier. Returns ------- bool Whether or not a figure with id *num* exists. """ return ( _pylab_helpers.Gcf.has_fignum(num) if isinstance(num, int) else num in get_figlabels() ) def get_fignums() -> list[int]: """Return a list of existing figure numbers.""" return sorted(_pylab_helpers.Gcf.figs) def get_figlabels() -> list[Any]: """Return a list of existing figure labels.""" managers = _pylab_helpers.Gcf.get_all_fig_managers() managers.sort(key=lambda m: m.num) return [m.canvas.figure.get_label() for m in managers] def get_current_fig_manager() -> FigureManagerBase | None: """ Return the figure manager of the current figure. The figure manager is a container for the actual backend-depended window that displays the figure on screen. If no current figure exists, a new one is created, and its figure manager is returned. Returns ------- `.FigureManagerBase` or backend-dependent subclass thereof """ return gcf().canvas.manager @_copy_docstring_and_deprecators(FigureCanvasBase.mpl_connect) def connect(s: str, func: Callable[[Event], Any]) -> int: return gcf().canvas.mpl_connect(s, func) @_copy_docstring_and_deprecators(FigureCanvasBase.mpl_disconnect) def disconnect(cid: int) -> None: gcf().canvas.mpl_disconnect(cid) def close(fig: None | int | str | Figure | Literal["all"] = None) -> None: """ Close a figure window, and unregister it from pyplot. Parameters ---------- fig : None or int or str or `.Figure` The figure to close. There are a number of ways to specify this: - *None*: the current figure - `.Figure`: the given `.Figure` instance - ``int``: a figure number - ``str``: a figure name - 'all': all figures Notes ----- pyplot maintains a reference to figures created with `figure()`. When work on the figure is completed, it should be closed, i.e. deregistered from pyplot, to free its memory (see also :rc:figure.max_open_warning). Closing a figure window created by `show()` automatically deregisters the figure. For all other use cases, most prominently `savefig()` without `show()`, the figure must be deregistered explicitly using `close()`. """ if fig is None: manager = _pylab_helpers.Gcf.get_active() if manager is None: return else: _pylab_helpers.Gcf.destroy(manager) elif fig == 'all': _pylab_helpers.Gcf.destroy_all() elif isinstance(fig, int): _pylab_helpers.Gcf.destroy(fig) elif hasattr(fig, 'int'): # if we are dealing with a type UUID, we # can use its integer representation _pylab_helpers.Gcf.destroy(fig.int) elif isinstance(fig, str): all_labels = get_figlabels() if fig in all_labels: num = get_fignums()[all_labels.index(fig)] _pylab_helpers.Gcf.destroy(num) elif isinstance(fig, Figure): _pylab_helpers.Gcf.destroy_fig(fig) else: raise TypeError("close() argument must be a Figure, an int, a string, " "or None, not %s" % type(fig)) def clf() -> None: """Clear the current figure.""" gcf().clear() def draw() -> None: """ Redraw the current figure. This is used to update a figure that has been altered, but not automatically re-drawn. If interactive mode is on (via `.ion()`), this should be only rarely needed, but there may be ways to modify the state of a figure without marking it as "stale". Please report these cases as bugs. This is equivalent to calling ``fig.canvas.draw_idle()``, where ``fig`` is the current figure. See Also -------- .FigureCanvasBase.draw_idle .FigureCanvasBase.draw """ gcf().canvas.draw_idle() @_copy_docstring_and_deprecators(Figure.savefig) def savefig(*args, **kwargs) -> None: fig = gcf() # savefig default implementation has no return, so mypy is unhappy # presumably this is here because subclasses can return? res = fig.savefig(*args, **kwargs) # type: ignore[func-returns-value] fig.canvas.draw_idle() # Need this if 'transparent=True', to reset colors. return res ## Putting things in figures ## def figlegend(*args, **kwargs) -> Legend: return gcf().legend(*args, **kwargs) if Figure.legend.__doc__: figlegend.__doc__ = Figure.legend.__doc__ \ .replace(" legend(", " figlegend(") \ .replace("fig.legend(", "plt.figlegend(") \ .replace("ax.plot(", "plt.plot(") ## Axes ## @_docstring.interpd def axes( arg: None | tuple[float, float, float, float] = None, **kwargs ) -> matplotlib.axes.Axes: """ Add an Axes to the current figure and make it the current Axes. Call signatures:: plt.axes() plt.axes(rect, projection=None, polar=False, **kwargs) plt.axes(ax) Parameters ---------- arg : None or 4-tuple The exact behavior of this function depends on the type: - *None*: A new full window Axes is added using ``subplot(**kwargs)``. - 4-tuple of floats *rect* = ``(left, bottom, width, height)``. A new Axes is added with dimensions *rect* in normalized (0, 1) units using `~.Figure.add_axes` on the current figure. projection : {None, 'aitoff', 'hammer', 'lambert', 'mollweide', \ 'polar', 'rectilinear', str}, optional The projection type of the `~.axes.Axes`. *str* is the name of a custom projection, see `~matplotlib.projections`. The default None results in a 'rectilinear' projection. polar : bool, default: False If True, equivalent to projection='polar'. sharex, sharey : `~matplotlib.axes.Axes`, optional Share the x or y `~matplotlib.axis` with sharex and/or sharey. The axis will have the same limits, ticks, and scale as the axis of the shared Axes. label : str A label for the returned Axes. Returns ------- `~.axes.Axes`, or a subclass of `~.axes.Axes` The returned Axes class depends on the projection used. It is `~.axes.Axes` if rectilinear projection is used and `.projections.polar.PolarAxes` if polar projection is used. Other Parameters ---------------- **kwargs This method also takes the keyword arguments for the returned Axes class. The keyword arguments for the rectilinear Axes class `~.axes.Axes` can be found in the following table but there might also be other keyword arguments if another projection is used, see the actual Axes class. %(Axes:kwdoc)s See Also -------- .Figure.add_axes .pyplot.subplot .Figure.add_subplot .Figure.subplots .pyplot.subplots Examples -------- :: # Creating a new full window Axes plt.axes() # Creating a new Axes with specified dimensions and a grey background plt.axes((left, bottom, width, height), facecolor='grey') """ fig = gcf() pos = kwargs.pop('position', None) if arg is None: if pos is None: return fig.add_subplot(**kwargs) else: return fig.add_axes(pos, **kwargs) else: return fig.add_axes(arg, **kwargs) def delaxes(ax: matplotlib.axes.Axes | None = None) -> None: """ Remove an `~.axes.Axes` (defaulting to the current Axes) from its figure. """ if ax is None: ax = gca() ax.remove() def sca(ax: Axes) -> None: """ Set the current Axes to *ax* and the current Figure to the parent of *ax*. """ # Mypy sees ax.figure as potentially None, # but if you are calling this, it won't be None # Additionally the slight difference between `Figure` and `FigureBase` mypy catches fig = ax.get_figure(root=False) figure(fig) # type: ignore[arg-type] fig.sca(ax) # type: ignore[union-attr] def cla() -> None: """Clear the current Axes.""" # Not generated via boilerplate.py to allow a different docstring. return gca().cla() ## More ways of creating Axes ## @_docstring.interpd def subplot(*args, **kwargs) -> Axes: """ Add an Axes to the current figure or retrieve an existing Axes. This is a wrapper of `.Figure.add_subplot` which provides additional behavior when working with the implicit API (see the notes section). Call signatures:: subplot(nrows, ncols, index, **kwargs) subplot(pos, **kwargs) subplot(**kwargs) subplot(ax) Parameters ---------- *args : int, (int, int, *index*), or `.SubplotSpec`, default: (1, 1, 1) The position of the subplot described by one of - Three integers (*nrows*, *ncols*, *index*). The subplot will take the *index* position on a grid with *nrows* rows and *ncols* columns. *index* starts at 1 in the upper left corner and increases to the right. *index* can also be a two-tuple specifying the (*first*, *last*) indices (1-based, and including *last*) of the subplot, e.g., ``fig.add_subplot(3, 1, (1, 2))`` makes a subplot that spans the upper 2/3 of the figure. - A 3-digit integer. The digits are interpreted as if given separately as three single-digit integers, i.e. ``fig.add_subplot(235)`` is the same as ``fig.add_subplot(2, 3, 5)``. Note that this can only be used if there are no more than 9 subplots. - A `.SubplotSpec`. projection : {None, 'aitoff', 'hammer', 'lambert', 'mollweide', \ 'polar', 'rectilinear', str}, optional The projection type of the subplot (`~.axes.Axes`). *str* is the name of a custom projection, see `~matplotlib.projections`. The default None results in a 'rectilinear' projection. polar : bool, default: False If True, equivalent to projection='polar'. sharex, sharey : `~matplotlib.axes.Axes`, optional Share the x or y `~matplotlib.axis` with sharex and/or sharey. The axis will have the same limits, ticks, and scale as the axis of the shared Axes. label : str A label for the returned Axes. Returns ------- `~.axes.Axes` The Axes of the subplot. The returned Axes can actually be an instance of a subclass, such as `.projections.polar.PolarAxes` for polar projections. Other Parameters ---------------- **kwargs This method also takes the keyword arguments for the returned Axes base class; except for the *figure* argument. The keyword arguments for the rectilinear base class `~.axes.Axes` can be found in the following table but there might also be other keyword arguments if another projection is used. %(Axes:kwdoc)s Notes ----- .. versionchanged:: 3.8 In versions prior to 3.8, any preexisting Axes that overlap with the new Axes beyond sharing a boundary was deleted. Deletion does not happen in more recent versions anymore. Use `.Axes.remove` explicitly if needed. If you do not want this behavior, use the `.Figure.add_subplot` method or the `.pyplot.axes` function instead. If no *kwargs* are passed and there exists an Axes in the location specified by *args* then that Axes will be returned rather than a new Axes being created. If *kwargs* are passed and there exists an Axes in the location specified by *args*, the projection type is the same, and the *kwargs* match with the existing Axes, then the existing Axes is returned. Otherwise a new Axes is created with the specified parameters. We save a reference to the *kwargs* which we use for this comparison. If any of the values in *kwargs* are mutable we will not detect the case where they are mutated. In these cases we suggest using `.Figure.add_subplot` and the explicit Axes API rather than the implicit pyplot API. See Also -------- .Figure.add_subplot .pyplot.subplots .pyplot.axes .Figure.subplots Examples -------- :: plt.subplot(221) # equivalent but more general ax1 = plt.subplot(2, 2, 1) # add a subplot with no frame ax2 = plt.subplot(222, frameon=False) # add a polar subplot plt.subplot(223, projection='polar') # add a red subplot that shares the x-axis with ax1 plt.subplot(224, sharex=ax1, facecolor='red') # delete ax2 from the figure plt.delaxes(ax2) # add ax2 to the figure again plt.subplot(ax2) # make the first Axes "current" again plt.subplot(221) """ # Here we will only normalize `polar=True` vs `projection='polar'` and let # downstream code deal with the rest. unset = object() projection = kwargs.get('projection', unset) polar = kwargs.pop('polar', unset) if polar is not unset and polar: # if we got mixed messages from the user, raise if projection is not unset and projection != 'polar': raise ValueError( f"polar={polar}, yet projection={projection!r}. " "Only one of these arguments should be supplied." ) kwargs['projection'] = projection = 'polar' # if subplot called without arguments, create subplot(1, 1, 1) if len(args) == 0: args = (1, 1, 1) # This check was added because it is very easy to type subplot(1, 2, False) # when subplots(1, 2, False) was intended (sharex=False, that is). In most # cases, no error will ever occur, but mysterious behavior can result # because what was intended to be the sharex argument is instead treated as # a subplot index for subplot() if len(args) >= 3 and isinstance(args[2], bool): _api.warn_external("The subplot index argument to subplot() appears " "to be a boolean. Did you intend to use " "subplots()?") # Check for nrows and ncols, which are not valid subplot args: if 'nrows' in kwargs or 'ncols' in kwargs: raise TypeError("subplot() got an unexpected keyword argument 'ncols' " "and/or 'nrows'. Did you intend to call subplots()?") fig = gcf() # First, search for an existing subplot with a matching spec. key = SubplotSpec._from_subplot_args(fig, args) for ax in fig.axes: # If we found an Axes at the position, we can reuse it if the user passed no # kwargs or if the Axes class and kwargs are identical. if (ax.get_subplotspec() == key and (kwargs == {} or (ax._projection_init == fig._process_projection_requirements(**kwargs)))): break else: # we have exhausted the known Axes and none match, make a new one! ax = fig.add_subplot(*args, **kwargs) fig.sca(ax) return ax @overload def subplots( nrows: Literal[1] = ..., ncols: Literal[1] = ..., *, sharex: bool | Literal["none", "all", "row", "col"] = ..., sharey: bool | Literal["none", "all", "row", "col"] = ..., squeeze: Literal[True] = ..., width_ratios: Sequence[float] | None = ..., height_ratios: Sequence[float] | None = ..., subplot_kw: dict[str, Any] | None = ..., gridspec_kw: dict[str, Any] | None = ..., **fig_kw ) -> tuple[Figure, Axes]: ... @overload def subplots( nrows: int = ..., ncols: int = ..., *, sharex: bool | Literal["none", "all", "row", "col"] = ..., sharey: bool | Literal["none", "all", "row", "col"] = ..., squeeze: Literal[False], width_ratios: Sequence[float] | None = ..., height_ratios: Sequence[float] | None = ..., subplot_kw: dict[str, Any] | None = ..., gridspec_kw: dict[str, Any] | None = ..., **fig_kw ) -> tuple[Figure, np.ndarray]: # TODO numpy/numpy#24738 ... @overload def subplots( nrows: int = ..., ncols: int = ..., *, sharex: bool | Literal["none", "all", "row", "col"] = ..., sharey: bool | Literal["none", "all", "row", "col"] = ..., squeeze: bool = ..., width_ratios: Sequence[float] | None = ..., height_ratios: Sequence[float] | None = ..., subplot_kw: dict[str, Any] | None = ..., gridspec_kw: dict[str, Any] | None = ..., **fig_kw ) -> tuple[Figure, Any]: ... def subplots( nrows: int = 1, ncols: int = 1, *, sharex: bool | Literal["none", "all", "row", "col"] = False, sharey: bool | Literal["none", "all", "row", "col"] = False, squeeze: bool = True, width_ratios: Sequence[float] | None = None, height_ratios: Sequence[float] | None = None, subplot_kw: dict[str, Any] | None = None, gridspec_kw: dict[str, Any] | None = None, **fig_kw ) -> tuple[Figure, Any]: """ Create a figure and a set of subplots. This utility wrapper makes it convenient to create common layouts of subplots, including the enclosing figure object, in a single call. Parameters ---------- nrows, ncols : int, default: 1 Number of rows/columns of the subplot grid. sharex, sharey : bool or {'none', 'all', 'row', 'col'}, default: False Controls sharing of properties among x (*sharex*) or y (*sharey*) axes: - True or 'all': x- or y-axis will be shared among all subplots. - False or 'none': each subplot x- or y-axis will be independent. - 'row': each subplot row will share an x- or y-axis. - 'col': each subplot column will share an x- or y-axis. When subplots have a shared x-axis along a column, only the x tick labels of the bottom subplot are created. Similarly, when subplots have a shared y-axis along a row, only the y tick labels of the first column subplot are created. To later turn other subplots' ticklabels on, use `~matplotlib.axes.Axes.tick_params`. When subplots have a shared axis that has units, calling `.Axis.set_units` will update each axis with the new units. Note that it is not possible to unshare axes. squeeze : bool, default: True - If True, extra dimensions are squeezed out from the returned array of `~matplotlib.axes.Axes`: - if only one subplot is constructed (nrows=ncols=1), the resulting single Axes object is returned as a scalar. - for Nx1 or 1xM subplots, the returned object is a 1D numpy object array of Axes objects. - for NxM, subplots with N>1 and M>1 are returned as a 2D array. - If False, no squeezing at all is done: the returned Axes object is always a 2D array containing Axes instances, even if it ends up being 1x1. width_ratios : array-like of length *ncols*, optional Defines the relative widths of the columns. Each column gets a relative width of ``width_ratios[i] / sum(width_ratios)``. If not given, all columns will have the same width. Equivalent to ``gridspec_kw={'width_ratios': [...]}``. height_ratios : array-like of length *nrows*, optional Defines the relative heights of the rows. Each row gets a relative height of ``height_ratios[i] / sum(height_ratios)``. If not given, all rows will have the same height. Convenience for ``gridspec_kw={'height_ratios': [...]}``. subplot_kw : dict, optional Dict with keywords passed to the `~matplotlib.figure.Figure.add_subplot` call used to create each subplot. gridspec_kw : dict, optional Dict with keywords passed to the `~matplotlib.gridspec.GridSpec` constructor used to create the grid the subplots are placed on. **fig_kw All additional keyword arguments are passed to the `.pyplot.figure` call. Returns ------- fig : `.Figure` ax : `~matplotlib.axes.Axes` or array of Axes *ax* can be either a single `~.axes.Axes` object, or an array of Axes objects if more than one subplot was created. The dimensions of the resulting array can be controlled with the squeeze keyword, see above. Typical idioms for handling the return value are:: # using the variable ax for single a Axes fig, ax = plt.subplots() # using the variable axs for multiple Axes fig, axs = plt.subplots(2, 2) # using tuple unpacking for multiple Axes fig, (ax1, ax2) = plt.subplots(1, 2) fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2) The names ``ax`` and pluralized ``axs`` are preferred over ``axes`` because for the latter it's not clear if it refers to a single `~.axes.Axes` instance or a collection of these. See Also -------- .pyplot.figure .pyplot.subplot .pyplot.axes .Figure.subplots .Figure.add_subplot Examples -------- :: # First create some toy data: x = np.linspace(0, 2*np.pi, 400) y = np.sin(x**2) # Create just a figure and only one subplot fig, ax = plt.subplots() ax.plot(x, y) ax.set_title('Simple plot') # Create two subplots and unpack the output array immediately f, (ax1, ax2) = plt.subplots(1, 2, sharey=True) ax1.plot(x, y) ax1.set_title('Sharing Y axis') ax2.scatter(x, y) # Create four polar Axes and access them through the returned array fig, axs = plt.subplots(2, 2, subplot_kw=dict(projection="polar")) axs[0, 0].plot(x, y) axs[1, 1].scatter(x, y) # Share a X axis with each column of subplots plt.subplots(2, 2, sharex='col') # Share a Y axis with each row of subplots plt.subplots(2, 2, sharey='row') # Share both X and Y axes with all subplots plt.subplots(2, 2, sharex='all', sharey='all') # Note that this is the same as plt.subplots(2, 2, sharex=True, sharey=True) # Create figure number 10 with a single subplot # and clears it if it already exists. fig, ax = plt.subplots(num=10, clear=True) """ fig = figure(**fig_kw) axs = fig.subplots(nrows=nrows, ncols=ncols, sharex=sharex, sharey=sharey, squeeze=squeeze, subplot_kw=subplot_kw, gridspec_kw=gridspec_kw, height_ratios=height_ratios, width_ratios=width_ratios) return fig, axs @overload def subplot_mosaic( mosaic: str, *, sharex: bool = ..., sharey: bool = ..., width_ratios: ArrayLike | None = ..., height_ratios: ArrayLike | None = ..., empty_sentinel: str = ..., subplot_kw: dict[str, Any] | None = ..., gridspec_kw: dict[str, Any] | None = ..., per_subplot_kw: dict[str | tuple[str, ...], dict[str, Any]] | None = ..., **fig_kw: Any ) -> tuple[Figure, dict[str, matplotlib.axes.Axes]]: ... @overload def subplot_mosaic( mosaic: list[HashableList[_T]], *, sharex: bool = ..., sharey: bool = ..., width_ratios: ArrayLike | None = ..., height_ratios: ArrayLike | None = ..., empty_sentinel: _T = ..., subplot_kw: dict[str, Any] | None = ..., gridspec_kw: dict[str, Any] | None = ..., per_subplot_kw: dict[_T | tuple[_T, ...], dict[str, Any]] | None = ..., **fig_kw: Any ) -> tuple[Figure, dict[_T, matplotlib.axes.Axes]]: ... @overload def subplot_mosaic( mosaic: list[HashableList[Hashable]], *, sharex: bool = ..., sharey: bool = ..., width_ratios: ArrayLike | None = ..., height_ratios: ArrayLike | None = ..., empty_sentinel: Any = ..., subplot_kw: dict[str, Any] | None = ..., gridspec_kw: dict[str, Any] | None = ..., per_subplot_kw: dict[Hashable | tuple[Hashable, ...], dict[str, Any]] | None = ..., **fig_kw: Any ) -> tuple[Figure, dict[Hashable, matplotlib.axes.Axes]]: ... def subplot_mosaic( mosaic: str | list[HashableList[_T]] | list[HashableList[Hashable]], *, sharex: bool = False, sharey: bool = False, width_ratios: ArrayLike | None = None, height_ratios: ArrayLike | None = None, empty_sentinel: Any = '.', subplot_kw: dict[str, Any] | None = None, gridspec_kw: dict[str, Any] | None = None, per_subplot_kw: dict[str | tuple[str, ...], dict[str, Any]] | dict[_T | tuple[_T, ...], dict[str, Any]] | dict[Hashable | tuple[Hashable, ...], dict[str, Any]] | None = None, **fig_kw: Any ) -> tuple[Figure, dict[str, matplotlib.axes.Axes]] | \ tuple[Figure, dict[_T, matplotlib.axes.Axes]] | \ tuple[Figure, dict[Hashable, matplotlib.axes.Axes]]: """ Build a layout of Axes based on ASCII art or nested lists. This is a helper function to build complex GridSpec layouts visually. See :ref:`mosaic` for an example and full API documentation Parameters ---------- mosaic : list of list of {hashable or nested} or str A visual layout of how you want your Axes to be arranged labeled as strings. For example :: x = [['A panel', 'A panel', 'edge'], ['C panel', '.', 'edge']] produces 4 Axes: - 'A panel' which is 1 row high and spans the first two columns - 'edge' which is 2 rows high and is on the right edge - 'C panel' which in 1 row and 1 column wide in the bottom left - a blank space 1 row and 1 column wide in the bottom center Any of the entries in the layout can be a list of lists of the same form to create nested layouts. If input is a str, then it must be of the form :: ''' AAE C.E ''' where each character is a column and each line is a row. This only allows only single character Axes labels and does not allow nesting but is very terse. sharex, sharey : bool, default: False If True, the x-axis (*sharex*) or y-axis (*sharey*) will be shared among all subplots. In that case, tick label visibility and axis units behave as for `subplots`. If False, each subplot's x- or y-axis will be independent. width_ratios : array-like of length *ncols*, optional Defines the relative widths of the columns. Each column gets a relative width of ``width_ratios[i] / sum(width_ratios)``. If not given, all columns will have the same width. Convenience for ``gridspec_kw={'width_ratios': [...]}``. height_ratios : array-like of length *nrows*, optional Defines the relative heights of the rows. Each row gets a relative height of ``height_ratios[i] / sum(height_ratios)``. If not given, all rows will have the same height. Convenience for ``gridspec_kw={'height_ratios': [...]}``. empty_sentinel : object, optional Entry in the layout to mean "leave this space empty". Defaults to ``'.'``. Note, if *layout* is a string, it is processed via `inspect.cleandoc` to remove leading white space, which may interfere with using white-space as the empty sentinel. subplot_kw : dict, optional Dictionary with keywords passed to the `.Figure.add_subplot` call used to create each subplot. These values may be overridden by values in *per_subplot_kw*. per_subplot_kw : dict, optional A dictionary mapping the Axes identifiers or tuples of identifiers to a dictionary of keyword arguments to be passed to the `.Figure.add_subplot` call used to create each subplot. The values in these dictionaries have precedence over the values in *subplot_kw*. If *mosaic* is a string, and thus all keys are single characters, it is possible to use a single string instead of a tuple as keys; i.e. ``"AB"`` is equivalent to ``("A", "B")``. .. versionadded:: 3.7 gridspec_kw : dict, optional Dictionary with keywords passed to the `.GridSpec` constructor used to create the grid the subplots are placed on. **fig_kw All additional keyword arguments are passed to the `.pyplot.figure` call. Returns ------- fig : `.Figure` The new figure dict[label, Axes] A dictionary mapping the labels to the Axes objects. The order of the Axes is left-to-right and top-to-bottom of their position in the total layout. """ fig = figure(**fig_kw) ax_dict = fig.subplot_mosaic( # type: ignore[misc] mosaic, # type: ignore[arg-type] sharex=sharex, sharey=sharey, height_ratios=height_ratios, width_ratios=width_ratios, subplot_kw=subplot_kw, gridspec_kw=gridspec_kw, empty_sentinel=empty_sentinel, per_subplot_kw=per_subplot_kw, # type: ignore[arg-type] ) return fig, ax_dict def subplot2grid( shape: tuple[int, int], loc: tuple[int, int], rowspan: int = 1, colspan: int = 1, fig: Figure | None = None, **kwargs ) -> matplotlib.axes.Axes: """ Create a subplot at a specific location inside a regular grid. Parameters ---------- shape : (int, int) Number of rows and of columns of the grid in which to place axis. loc : (int, int) Row number and column number of the axis location within the grid. rowspan : int, default: 1 Number of rows for the axis to span downwards. colspan : int, default: 1 Number of columns for the axis to span to the right. fig : `.Figure`, optional Figure to place the subplot in. Defaults to the current figure. **kwargs Additional keyword arguments are handed to `~.Figure.add_subplot`. Returns ------- `~.axes.Axes` The Axes of the subplot. The returned Axes can actually be an instance of a subclass, such as `.projections.polar.PolarAxes` for polar projections. Notes ----- The following call :: ax = subplot2grid((nrows, ncols), (row, col), rowspan, colspan) is identical to :: fig = gcf() gs = fig.add_gridspec(nrows, ncols) ax = fig.add_subplot(gs[row:row+rowspan, col:col+colspan]) """ if fig is None: fig = gcf() rows, cols = shape gs = GridSpec._check_gridspec_exists(fig, rows, cols) subplotspec = gs.new_subplotspec(loc, rowspan=rowspan, colspan=colspan) return fig.add_subplot(subplotspec, **kwargs) def twinx(ax: matplotlib.axes.Axes | None = None) -> _AxesBase: """ Make and return a second Axes that shares the *x*-axis. The new Axes will overlay *ax* (or the current Axes if *ax* is *None*), and its ticks will be on the right. Examples -------- :doc:`/gallery/subplots_axes_and_figures/two_scales` """ if ax is None: ax = gca() ax1 = ax.twinx() return ax1 def twiny(ax: matplotlib.axes.Axes | None = None) -> _AxesBase: """ Make and return a second Axes that shares the *y*-axis. The new Axes will overlay *ax* (or the current Axes if *ax* is *None*), and its ticks will be on the top. Examples -------- :doc:`/gallery/subplots_axes_and_figures/two_scales` """ if ax is None: ax = gca() ax1 = ax.twiny() return ax1 def subplot_tool(targetfig: Figure | None = None) -> SubplotTool | None: """ Launch a subplot tool window for a figure. Returns ------- `matplotlib.widgets.SubplotTool` """ if targetfig is None: targetfig = gcf() tb = targetfig.canvas.manager.toolbar # type: ignore[union-attr] if hasattr(tb, "configure_subplots"): # toolbar2 from matplotlib.backend_bases import NavigationToolbar2 return cast(NavigationToolbar2, tb).configure_subplots() elif hasattr(tb, "trigger_tool"): # toolmanager from matplotlib.backend_bases import ToolContainerBase cast(ToolContainerBase, tb).trigger_tool("subplots") return None else: raise ValueError("subplot_tool can only be launched for figures with " "an associated toolbar") def box(on: bool | None = None) -> None: """ Turn the Axes box on or off on the current Axes. Parameters ---------- on : bool or None The new `~matplotlib.axes.Axes` box state. If ``None``, toggle the state. See Also -------- :meth:`matplotlib.axes.Axes.set_frame_on` :meth:`matplotlib.axes.Axes.get_frame_on` """ ax = gca() if on is None: on = not ax.get_frame_on() ax.set_frame_on(on) ## Axis ## def xlim(*args, **kwargs) -> tuple[float, float]: """ Get or set the x limits of the current Axes. Call signatures:: left, right = xlim() # return the current xlim xlim((left, right)) # set the xlim to left, right xlim(left, right) # set the xlim to left, right If you do not specify args, you can pass *left* or *right* as kwargs, i.e.:: xlim(right=3) # adjust the right leaving left unchanged xlim(left=1) # adjust the left leaving right unchanged Setting limits turns autoscaling off for the x-axis. Returns ------- left, right A tuple of the new x-axis limits. Notes ----- Calling this function with no arguments (e.g. ``xlim()``) is the pyplot equivalent of calling `~.Axes.get_xlim` on the current Axes. Calling this function with arguments is the pyplot equivalent of calling `~.Axes.set_xlim` on the current Axes. All arguments are passed though. """ ax = gca() if not args and not kwargs: return ax.get_xlim() ret = ax.set_xlim(*args, **kwargs) return ret def ylim(*args, **kwargs) -> tuple[float, float]: """ Get or set the y-limits of the current Axes. Call signatures:: bottom, top = ylim() # return the current ylim ylim((bottom, top)) # set the ylim to bottom, top ylim(bottom, top) # set the ylim to bottom, top If you do not specify args, you can alternatively pass *bottom* or *top* as kwargs, i.e.:: ylim(top=3) # adjust the top leaving bottom unchanged ylim(bottom=1) # adjust the bottom leaving top unchanged Setting limits turns autoscaling off for the y-axis. Returns ------- bottom, top A tuple of the new y-axis limits. Notes ----- Calling this function with no arguments (e.g. ``ylim()``) is the pyplot equivalent of calling `~.Axes.get_ylim` on the current Axes. Calling this function with arguments is the pyplot equivalent of calling `~.Axes.set_ylim` on the current Axes. All arguments are passed though. """ ax = gca() if not args and not kwargs: return ax.get_ylim() ret = ax.set_ylim(*args, **kwargs) return ret def xticks( ticks: ArrayLike | None = None, labels: Sequence[str] | None = None, *, minor: bool = False, **kwargs ) -> tuple[list[Tick] | np.ndarray, list[Text]]: """ Get or set the current tick locations and labels of the x-axis. Pass no arguments to return the current values without modifying them. Parameters ---------- ticks : array-like, optional The list of xtick locations. Passing an empty list removes all xticks. labels : array-like, optional The labels to place at the given *ticks* locations. This argument can only be passed if *ticks* is passed as well. minor : bool, default: False If ``False``, get/set the major ticks/labels; if ``True``, the minor ticks/labels. **kwargs `.Text` properties can be used to control the appearance of the labels. .. warning:: This only sets the properties of the current ticks, which is only sufficient if you either pass *ticks*, resulting in a fixed list of ticks, or if the plot is static. Ticks are not guaranteed to be persistent. Various operations can create, delete and modify the Tick instances. There is an imminent risk that these settings can get lost if you work on the figure further (including also panning/zooming on a displayed figure). Use `~.pyplot.tick_params` instead if possible. Returns ------- locs The list of xtick locations. labels The list of xlabel `.Text` objects. Notes ----- Calling this function with no arguments (e.g. ``xticks()``) is the pyplot equivalent of calling `~.Axes.get_xticks` and `~.Axes.get_xticklabels` on the current Axes. Calling this function with arguments is the pyplot equivalent of calling `~.Axes.set_xticks` and `~.Axes.set_xticklabels` on the current Axes. Examples -------- >>> locs, labels = xticks() # Get the current locations and labels. >>> xticks(np.arange(0, 1, step=0.2)) # Set label locations. >>> xticks(np.arange(3), ['Tom', 'Dick', 'Sue']) # Set text labels. >>> xticks([0, 1, 2], ['January', 'February', 'March'], ... rotation=20) # Set text labels and properties. >>> xticks([]) # Disable xticks. """ ax = gca() locs: list[Tick] | np.ndarray if ticks is None: locs = ax.get_xticks(minor=minor) if labels is not None: raise TypeError("xticks(): Parameter 'labels' can't be set " "without setting 'ticks'") else: locs = ax.set_xticks(ticks, minor=minor) labels_out: list[Text] = [] if labels is None: labels_out = ax.get_xticklabels(minor=minor) for l in labels_out: l._internal_update(kwargs) else: labels_out = ax.set_xticklabels(labels, minor=minor, **kwargs) return locs, labels_out def yticks( ticks: ArrayLike | None = None, labels: Sequence[str] | None = None, *, minor: bool = False, **kwargs ) -> tuple[list[Tick] | np.ndarray, list[Text]]: """ Get or set the current tick locations and labels of the y-axis. Pass no arguments to return the current values without modifying them. Parameters ---------- ticks : array-like, optional The list of ytick locations. Passing an empty list removes all yticks. labels : array-like, optional The labels to place at the given *ticks* locations. This argument can only be passed if *ticks* is passed as well. minor : bool, default: False If ``False``, get/set the major ticks/labels; if ``True``, the minor ticks/labels. **kwargs `.Text` properties can be used to control the appearance of the labels. .. warning:: This only sets the properties of the current ticks, which is only sufficient if you either pass *ticks*, resulting in a fixed list of ticks, or if the plot is static. Ticks are not guaranteed to be persistent. Various operations can create, delete and modify the Tick instances. There is an imminent risk that these settings can get lost if you work on the figure further (including also panning/zooming on a displayed figure). Use `~.pyplot.tick_params` instead if possible. Returns ------- locs The list of ytick locations. labels The list of ylabel `.Text` objects. Notes ----- Calling this function with no arguments (e.g. ``yticks()``) is the pyplot equivalent of calling `~.Axes.get_yticks` and `~.Axes.get_yticklabels` on the current Axes. Calling this function with arguments is the pyplot equivalent of calling `~.Axes.set_yticks` and `~.Axes.set_yticklabels` on the current Axes. Examples -------- >>> locs, labels = yticks() # Get the current locations and labels. >>> yticks(np.arange(0, 1, step=0.2)) # Set label locations. >>> yticks(np.arange(3), ['Tom', 'Dick', 'Sue']) # Set text labels. >>> yticks([0, 1, 2], ['January', 'February', 'March'], ... rotation=45) # Set text labels and properties. >>> yticks([]) # Disable yticks. """ ax = gca() locs: list[Tick] | np.ndarray if ticks is None: locs = ax.get_yticks(minor=minor) if labels is not None: raise TypeError("yticks(): Parameter 'labels' can't be set " "without setting 'ticks'") else: locs = ax.set_yticks(ticks, minor=minor) labels_out: list[Text] = [] if labels is None: labels_out = ax.get_yticklabels(minor=minor) for l in labels_out: l._internal_update(kwargs) else: labels_out = ax.set_yticklabels(labels, minor=minor, **kwargs) return locs, labels_out def rgrids( radii: ArrayLike | None = None, labels: Sequence[str | Text] | None = None, angle: float | None = None, fmt: str | None = None, **kwargs ) -> tuple[list[Line2D], list[Text]]: """ Get or set the radial gridlines on the current polar plot. Call signatures:: lines, labels = rgrids() lines, labels = rgrids(radii, labels=None, angle=22.5, fmt=None, **kwargs) When called with no arguments, `.rgrids` simply returns the tuple (*lines*, *labels*). When called with arguments, the labels will appear at the specified radial distances and angle. Parameters ---------- radii : tuple with floats The radii for the radial gridlines labels : tuple with strings or None The labels to use at each radial gridline. The `matplotlib.ticker.ScalarFormatter` will be used if None. angle : float The angular position of the radius labels in degrees. fmt : str or None Format string used in `matplotlib.ticker.FormatStrFormatter`. For example '%f'. Returns ------- lines : list of `.lines.Line2D` The radial gridlines. labels : list of `.text.Text` The tick labels. Other Parameters ---------------- **kwargs *kwargs* are optional `.Text` properties for the labels. See Also -------- .pyplot.thetagrids .projections.polar.PolarAxes.set_rgrids .Axis.get_gridlines .Axis.get_ticklabels Examples -------- :: # set the locations of the radial gridlines lines, labels = rgrids( (0.25, 0.5, 1.0) ) # set the locations and labels of the radial gridlines lines, labels = rgrids( (0.25, 0.5, 1.0), ('Tom', 'Dick', 'Harry' )) """ ax = gca() if not isinstance(ax, PolarAxes): raise RuntimeError('rgrids only defined for polar Axes') if all(p is None for p in [radii, labels, angle, fmt]) and not kwargs: lines_out: list[Line2D] = ax.yaxis.get_gridlines() labels_out: list[Text] = ax.yaxis.get_ticklabels() elif radii is None: raise TypeError("'radii' cannot be None when other parameters are passed") else: lines_out, labels_out = ax.set_rgrids( radii, labels=labels, angle=angle, fmt=fmt, **kwargs) return lines_out, labels_out def thetagrids( angles: ArrayLike | None = None, labels: Sequence[str | Text] | None = None, fmt: str | None = None, **kwargs ) -> tuple[list[Line2D], list[Text]]: """ Get or set the theta gridlines on the current polar plot. Call signatures:: lines, labels = thetagrids() lines, labels = thetagrids(angles, labels=None, fmt=None, **kwargs) When called with no arguments, `.thetagrids` simply returns the tuple (*lines*, *labels*). When called with arguments, the labels will appear at the specified angles. Parameters ---------- angles : tuple with floats, degrees The angles of the theta gridlines. labels : tuple with strings or None The labels to use at each radial gridline. The `.projections.polar.ThetaFormatter` will be used if None. fmt : str or None Format string used in `matplotlib.ticker.FormatStrFormatter`. For example '%f'. Note that the angle in radians will be used. Returns ------- lines : list of `.lines.Line2D` The theta gridlines. labels : list of `.text.Text` The tick labels. Other Parameters ---------------- **kwargs *kwargs* are optional `.Text` properties for the labels. See Also -------- .pyplot.rgrids .projections.polar.PolarAxes.set_thetagrids .Axis.get_gridlines .Axis.get_ticklabels Examples -------- :: # set the locations of the angular gridlines lines, labels = thetagrids(range(45, 360, 90)) # set the locations and labels of the angular gridlines lines, labels = thetagrids(range(45, 360, 90), ('NE', 'NW', 'SW', 'SE')) """ ax = gca() if not isinstance(ax, PolarAxes): raise RuntimeError('thetagrids only defined for polar Axes') if all(param is None for param in [angles, labels, fmt]) and not kwargs: lines_out: list[Line2D] = ax.xaxis.get_ticklines() labels_out: list[Text] = ax.xaxis.get_ticklabels() elif angles is None: raise TypeError("'angles' cannot be None when other parameters are passed") else: lines_out, labels_out = ax.set_thetagrids(angles, labels=labels, fmt=fmt, **kwargs) return lines_out, labels_out @_api.deprecated("3.7", pending=True) def get_plot_commands() -> list[str]: """ Get a sorted list of all of the plotting commands. """ NON_PLOT_COMMANDS = { 'connect', 'disconnect', 'get_current_fig_manager', 'ginput', 'new_figure_manager', 'waitforbuttonpress'} return [name for name in _get_pyplot_commands() if name not in NON_PLOT_COMMANDS] def _get_pyplot_commands() -> list[str]: # This works by searching for all functions in this module and removing # a few hard-coded exclusions, as well as all of the colormap-setting # functions, and anything marked as private with a preceding underscore. exclude = {'colormaps', 'colors', 'get_plot_commands', *colormaps} this_module = inspect.getmodule(get_plot_commands) return sorted( name for name, obj in globals().items() if not name.startswith('_') and name not in exclude and inspect.isfunction(obj) and inspect.getmodule(obj) is this_module) ## Plotting part 1: manually generated functions and wrappers ## @_copy_docstring_and_deprecators(Figure.colorbar) def colorbar( mappable: ScalarMappable | ColorizingArtist | None = None, cax: matplotlib.axes.Axes | None = None, ax: matplotlib.axes.Axes | Iterable[matplotlib.axes.Axes] | None = None, **kwargs ) -> Colorbar: if mappable is None: mappable = gci() if mappable is None: raise RuntimeError('No mappable was found to use for colorbar ' 'creation. First define a mappable such as ' 'an image (with imshow) or a contour set (' 'with contourf).') ret = gcf().colorbar(mappable, cax=cax, ax=ax, **kwargs) return ret def clim(vmin: float | None = None, vmax: float | None = None) -> None: """ Set the color limits of the current image. If either *vmin* or *vmax* is None, the image min/max respectively will be used for color scaling. If you want to set the clim of multiple images, use `~.ScalarMappable.set_clim` on every image, for example:: for im in gca().get_images(): im.set_clim(0, 0.5) """ im = gci() if im is None: raise RuntimeError('You must first define an image, e.g., with imshow') im.set_clim(vmin, vmax) def get_cmap(name: Colormap | str | None = None, lut: int | None = None) -> Colormap: """ Get a colormap instance, defaulting to rc values if *name* is None. Parameters ---------- name : `~matplotlib.colors.Colormap` or str or None, default: None If a `.Colormap` instance, it will be returned. Otherwise, the name of a colormap known to Matplotlib, which will be resampled by *lut*. The default, None, means :rc:`image.cmap`. lut : int or None, default: None If *name* is not already a Colormap instance and *lut* is not None, the colormap will be resampled to have *lut* entries in the lookup table. Returns ------- Colormap """ if name is None: name = rcParams['image.cmap'] if isinstance(name, Colormap): return name _api.check_in_list(sorted(_colormaps), name=name) if lut is None: return _colormaps[name] else: return _colormaps[name].resampled(lut) def set_cmap(cmap: Colormap | str) -> None: """ Set the default colormap, and applies it to the current image if any. Parameters ---------- cmap : `~matplotlib.colors.Colormap` or str A colormap instance or the name of a registered colormap. See Also -------- colormaps get_cmap """ cmap = get_cmap(cmap) rc('image', cmap=cmap.name) im = gci() if im is not None: im.set_cmap(cmap) @_copy_docstring_and_deprecators(matplotlib.image.imread) def imread( fname: str | pathlib.Path | BinaryIO, format: str | None = None ) -> np.ndarray: return matplotlib.image.imread(fname, format) @_copy_docstring_and_deprecators(matplotlib.image.imsave) def imsave( fname: str | os.PathLike | BinaryIO, arr: ArrayLike, **kwargs ) -> None: matplotlib.image.imsave(fname, arr, **kwargs) def matshow(A: ArrayLike, fignum: None | int = None, **kwargs) -> AxesImage: """ Display a 2D array as a matrix in a new figure window. The origin is set at the upper left hand corner. The indexing is ``(row, column)`` so that the first index runs vertically and the second index runs horizontally in the figure: .. code-block:: none A[0, 0] ⋯ A[0, M-1] ⋮ ⋮ A[N-1, 0] ⋯ A[N-1, M-1] The aspect ratio of the figure window is that of the array, unless this would make an excessively short or narrow figure. Tick labels for the xaxis are placed on top. Parameters ---------- A : 2D array-like The matrix to be displayed. fignum : None or int If *None*, create a new, appropriately sized figure window. If 0, use the current Axes (creating one if there is none, without ever adjusting the figure size). Otherwise, create a new Axes on the figure with the given number (creating it at the appropriate size if it does not exist, but not adjusting the figure size otherwise). Note that this will be drawn on top of any preexisting Axes on the figure. Returns ------- `~matplotlib.image.AxesImage` Other Parameters ---------------- **kwargs : `~matplotlib.axes.Axes.imshow` arguments """ A = np.asanyarray(A) if fignum == 0: ax = gca() else: if fignum is not None and fignum_exists(fignum): # Do not try to set a figure size. figsize = None else: # Extract actual aspect ratio of array and make appropriately sized figure. figsize = figaspect(A) fig = figure(fignum, figsize=figsize) ax = fig.add_axes((0.15, 0.09, 0.775, 0.775)) im = ax.matshow(A, **kwargs) sci(im) return im def polar(*args, **kwargs) -> list[Line2D]: """ Make a polar plot. call signature:: polar(theta, r, [fmt], **kwargs) This is a convenience wrapper around `.pyplot.plot`. It ensures that the current Axes is polar (or creates one if needed) and then passes all parameters to ``.pyplot.plot``. .. note:: When making polar plots using the :ref:`pyplot API <pyplot_interface>`, ``polar()`` should typically be the first command because that makes sure a polar Axes is created. Using other commands such as ``plt.title()`` before this can lead to the implicit creation of a rectangular Axes, in which case a subsequent ``polar()`` call will fail. """ # If an axis already exists, check if it has a polar projection if gcf().get_axes(): ax = gca() if not isinstance(ax, PolarAxes): _api.warn_deprecated( "3.10", message="There exists a non-polar current Axes. Therefore, the " "resulting plot from 'polar()' is non-polar. You likely " "should call 'polar()' before any other pyplot plotting " "commands. " "Support for this scenario is deprecated in %(since)s and " "will raise an error in %(removal)s" ) else: ax = axes(projection="polar") return ax.plot(*args, **kwargs) # If rcParams['backend_fallback'] is true, and an interactive backend is # requested, ignore rcParams['backend'] and force selection of a backend that # is compatible with the current running interactive framework. if rcParams["backend_fallback"]: requested_backend = rcParams._get_backend_or_none() # type: ignore[attr-defined] requested_backend = None if requested_backend is None else requested_backend.lower() available_backends = backend_registry.list_builtin(BackendFilter.INTERACTIVE) if ( requested_backend in (set(available_backends) - {'webagg', 'nbagg'}) and cbook._get_running_interactive_framework() ): rcParams._set("backend", rcsetup._auto_backend_sentinel) # fmt: on ################# REMAINING CONTENT GENERATED BY boilerplate.py ############## # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Figure.figimage) def figimage( X: ArrayLike, xo: int = 0, yo: int = 0, alpha: float | None = None, norm: str | Normalize | None = None, cmap: str | Colormap | None = None, vmin: float | None = None, vmax: float | None = None, origin: Literal["upper", "lower"] | None = None, resize: bool = False, *, colorizer: Colorizer | None = None, **kwargs, ) -> FigureImage: return gcf().figimage( X, xo=xo, yo=yo, alpha=alpha, norm=norm, cmap=cmap, vmin=vmin, vmax=vmax, origin=origin, resize=resize, colorizer=colorizer, **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Figure.text) def figtext( x: float, y: float, s: str, fontdict: dict[str, Any] | None = None, **kwargs ) -> Text: return gcf().text(x, y, s, fontdict=fontdict, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Figure.gca) def gca() -> Axes: return gcf().gca() # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Figure._gci) def gci() -> ColorizingArtist | None: return gcf()._gci() # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Figure.ginput) def ginput( n: int = 1, timeout: float = 30, show_clicks: bool = True, mouse_add: MouseButton = MouseButton.LEFT, mouse_pop: MouseButton = MouseButton.RIGHT, mouse_stop: MouseButton = MouseButton.MIDDLE, ) -> list[tuple[int, int]]: return gcf().ginput( n=n, timeout=timeout, show_clicks=show_clicks, mouse_add=mouse_add, mouse_pop=mouse_pop, mouse_stop=mouse_stop, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Figure.subplots_adjust) def subplots_adjust( left: float | None = None, bottom: float | None = None, right: float | None = None, top: float | None = None, wspace: float | None = None, hspace: float | None = None, ) -> None: gcf().subplots_adjust( left=left, bottom=bottom, right=right, top=top, wspace=wspace, hspace=hspace ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Figure.suptitle) def suptitle(t: str, **kwargs) -> Text: return gcf().suptitle(t, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Figure.tight_layout) def tight_layout( *, pad: float = 1.08, h_pad: float | None = None, w_pad: float | None = None, rect: tuple[float, float, float, float] | None = None, ) -> None: gcf().tight_layout(pad=pad, h_pad=h_pad, w_pad=w_pad, rect=rect) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Figure.waitforbuttonpress) def waitforbuttonpress(timeout: float = -1) -> None | bool: return gcf().waitforbuttonpress(timeout=timeout) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.acorr) def acorr( x: ArrayLike, *, data=None, **kwargs ) -> tuple[np.ndarray, np.ndarray, LineCollection | Line2D, Line2D | None]: return gca().acorr(x, **({"data": data} if data is not None else {}), **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.angle_spectrum) def angle_spectrum( x: ArrayLike, Fs: float | None = None, Fc: int | None = None, window: Callable[[ArrayLike], ArrayLike] | ArrayLike | None = None, pad_to: int | None = None, sides: Literal["default", "onesided", "twosided"] | None = None, *, data=None, **kwargs, ) -> tuple[np.ndarray, np.ndarray, Line2D]: return gca().angle_spectrum( x, Fs=Fs, Fc=Fc, window=window, pad_to=pad_to, sides=sides, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.annotate) def annotate( text: str, xy: tuple[float, float], xytext: tuple[float, float] | None = None, xycoords: CoordsType = "data", textcoords: CoordsType | None = None, arrowprops: dict[str, Any] | None = None, annotation_clip: bool | None = None, **kwargs, ) -> Annotation: return gca().annotate( text, xy, xytext=xytext, xycoords=xycoords, textcoords=textcoords, arrowprops=arrowprops, annotation_clip=annotation_clip, **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.arrow) def arrow(x: float, y: float, dx: float, dy: float, **kwargs) -> FancyArrow: return gca().arrow(x, y, dx, dy, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.autoscale) def autoscale( enable: bool = True, axis: Literal["both", "x", "y"] = "both", tight: bool | None = None, ) -> None: gca().autoscale(enable=enable, axis=axis, tight=tight) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.axhline) def axhline(y: float = 0, xmin: float = 0, xmax: float = 1, **kwargs) -> Line2D: return gca().axhline(y=y, xmin=xmin, xmax=xmax, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.axhspan) def axhspan( ymin: float, ymax: float, xmin: float = 0, xmax: float = 1, **kwargs ) -> Rectangle: return gca().axhspan(ymin, ymax, xmin=xmin, xmax=xmax, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.axis) def axis( arg: tuple[float, float, float, float] | bool | str | None = None, /, *, emit: bool = True, **kwargs, ) -> tuple[float, float, float, float]: return gca().axis(arg, emit=emit, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.axline) def axline( xy1: tuple[float, float], xy2: tuple[float, float] | None = None, *, slope: float | None = None, **kwargs, ) -> AxLine: return gca().axline(xy1, xy2=xy2, slope=slope, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.axvline) def axvline(x: float = 0, ymin: float = 0, ymax: float = 1, **kwargs) -> Line2D: return gca().axvline(x=x, ymin=ymin, ymax=ymax, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.axvspan) def axvspan( xmin: float, xmax: float, ymin: float = 0, ymax: float = 1, **kwargs ) -> Rectangle: return gca().axvspan(xmin, xmax, ymin=ymin, ymax=ymax, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.bar) def bar( x: float | ArrayLike, height: float | ArrayLike, width: float | ArrayLike = 0.8, bottom: float | ArrayLike | None = None, *, align: Literal["center", "edge"] = "center", data=None, **kwargs, ) -> BarContainer: return gca().bar( x, height, width=width, bottom=bottom, align=align, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.barbs) def barbs(*args, data=None, **kwargs) -> Barbs: return gca().barbs(*args, **({"data": data} if data is not None else {}), **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.barh) def barh( y: float | ArrayLike, width: float | ArrayLike, height: float | ArrayLike = 0.8, left: float | ArrayLike | None = None, *, align: Literal["center", "edge"] = "center", data=None, **kwargs, ) -> BarContainer: return gca().barh( y, width, height=height, left=left, align=align, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.bar_label) def bar_label( container: BarContainer, labels: ArrayLike | None = None, *, fmt: str | Callable[[float], str] = "%g", label_type: Literal["center", "edge"] = "edge", padding: float = 0, **kwargs, ) -> list[Annotation]: return gca().bar_label( container, labels=labels, fmt=fmt, label_type=label_type, padding=padding, **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.boxplot) def boxplot( x: ArrayLike | Sequence[ArrayLike], notch: bool | None = None, sym: str | None = None, vert: bool | None = None, orientation: Literal["vertical", "horizontal"] = "vertical", whis: float | tuple[float, float] | None = None, positions: ArrayLike | None = None, widths: float | ArrayLike | None = None, patch_artist: bool | None = None, bootstrap: int | None = None, usermedians: ArrayLike | None = None, conf_intervals: ArrayLike | None = None, meanline: bool | None = None, showmeans: bool | None = None, showcaps: bool | None = None, showbox: bool | None = None, showfliers: bool | None = None, boxprops: dict[str, Any] | None = None, tick_labels: Sequence[str] | None = None, flierprops: dict[str, Any] | None = None, medianprops: dict[str, Any] | None = None, meanprops: dict[str, Any] | None = None, capprops: dict[str, Any] | None = None, whiskerprops: dict[str, Any] | None = None, manage_ticks: bool = True, autorange: bool = False, zorder: float | None = None, capwidths: float | ArrayLike | None = None, label: Sequence[str] | None = None, *, data=None, ) -> dict[str, Any]: return gca().boxplot( x, notch=notch, sym=sym, vert=vert, orientation=orientation, whis=whis, positions=positions, widths=widths, patch_artist=patch_artist, bootstrap=bootstrap, usermedians=usermedians, conf_intervals=conf_intervals, meanline=meanline, showmeans=showmeans, showcaps=showcaps, showbox=showbox, showfliers=showfliers, boxprops=boxprops, tick_labels=tick_labels, flierprops=flierprops, medianprops=medianprops, meanprops=meanprops, capprops=capprops, whiskerprops=whiskerprops, manage_ticks=manage_ticks, autorange=autorange, zorder=zorder, capwidths=capwidths, label=label, **({"data": data} if data is not None else {}), ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.broken_barh) def broken_barh( xranges: Sequence[tuple[float, float]], yrange: tuple[float, float], *, data=None, **kwargs, ) -> PolyCollection: return gca().broken_barh( xranges, yrange, **({"data": data} if data is not None else {}), **kwargs ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.clabel) def clabel(CS: ContourSet, levels: ArrayLike | None = None, **kwargs) -> list[Text]: return gca().clabel(CS, levels=levels, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.cohere) def cohere( x: ArrayLike, y: ArrayLike, NFFT: int = 256, Fs: float = 2, Fc: int = 0, detrend: Literal["none", "mean", "linear"] | Callable[[ArrayLike], ArrayLike] = mlab.detrend_none, window: Callable[[ArrayLike], ArrayLike] | ArrayLike = mlab.window_hanning, noverlap: int = 0, pad_to: int | None = None, sides: Literal["default", "onesided", "twosided"] = "default", scale_by_freq: bool | None = None, *, data=None, **kwargs, ) -> tuple[np.ndarray, np.ndarray]: return gca().cohere( x, y, NFFT=NFFT, Fs=Fs, Fc=Fc, detrend=detrend, window=window, noverlap=noverlap, pad_to=pad_to, sides=sides, scale_by_freq=scale_by_freq, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.contour) def contour(*args, data=None, **kwargs) -> QuadContourSet: __ret = gca().contour( *args, **({"data": data} if data is not None else {}), **kwargs ) if __ret._A is not None: # type: ignore[attr-defined] sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.contourf) def contourf(*args, data=None, **kwargs) -> QuadContourSet: __ret = gca().contourf( *args, **({"data": data} if data is not None else {}), **kwargs ) if __ret._A is not None: # type: ignore[attr-defined] sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.csd) def csd( x: ArrayLike, y: ArrayLike, NFFT: int | None = None, Fs: float | None = None, Fc: int | None = None, detrend: Literal["none", "mean", "linear"] | Callable[[ArrayLike], ArrayLike] | None = None, window: Callable[[ArrayLike], ArrayLike] | ArrayLike | None = None, noverlap: int | None = None, pad_to: int | None = None, sides: Literal["default", "onesided", "twosided"] | None = None, scale_by_freq: bool | None = None, return_line: bool | None = None, *, data=None, **kwargs, ) -> tuple[np.ndarray, np.ndarray] | tuple[np.ndarray, np.ndarray, Line2D]: return gca().csd( x, y, NFFT=NFFT, Fs=Fs, Fc=Fc, detrend=detrend, window=window, noverlap=noverlap, pad_to=pad_to, sides=sides, scale_by_freq=scale_by_freq, return_line=return_line, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.ecdf) def ecdf( x: ArrayLike, weights: ArrayLike | None = None, *, complementary: bool = False, orientation: Literal["vertical", "horizontal"] = "vertical", compress: bool = False, data=None, **kwargs, ) -> Line2D: return gca().ecdf( x, weights=weights, complementary=complementary, orientation=orientation, compress=compress, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.errorbar) def errorbar( x: float | ArrayLike, y: float | ArrayLike, yerr: float | ArrayLike | None = None, xerr: float | ArrayLike | None = None, fmt: str = "", ecolor: ColorType | None = None, elinewidth: float | None = None, capsize: float | None = None, barsabove: bool = False, lolims: bool | ArrayLike = False, uplims: bool | ArrayLike = False, xlolims: bool | ArrayLike = False, xuplims: bool | ArrayLike = False, errorevery: int | tuple[int, int] = 1, capthick: float | None = None, *, data=None, **kwargs, ) -> ErrorbarContainer: return gca().errorbar( x, y, yerr=yerr, xerr=xerr, fmt=fmt, ecolor=ecolor, elinewidth=elinewidth, capsize=capsize, barsabove=barsabove, lolims=lolims, uplims=uplims, xlolims=xlolims, xuplims=xuplims, errorevery=errorevery, capthick=capthick, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.eventplot) def eventplot( positions: ArrayLike | Sequence[ArrayLike], orientation: Literal["horizontal", "vertical"] = "horizontal", lineoffsets: float | Sequence[float] = 1, linelengths: float | Sequence[float] = 1, linewidths: float | Sequence[float] | None = None, colors: ColorType | Sequence[ColorType] | None = None, alpha: float | Sequence[float] | None = None, linestyles: LineStyleType | Sequence[LineStyleType] = "solid", *, data=None, **kwargs, ) -> EventCollection: return gca().eventplot( positions, orientation=orientation, lineoffsets=lineoffsets, linelengths=linelengths, linewidths=linewidths, colors=colors, alpha=alpha, linestyles=linestyles, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.fill) def fill(*args, data=None, **kwargs) -> list[Polygon]: return gca().fill(*args, **({"data": data} if data is not None else {}), **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.fill_between) def fill_between( x: ArrayLike, y1: ArrayLike | float, y2: ArrayLike | float = 0, where: Sequence[bool] | None = None, interpolate: bool = False, step: Literal["pre", "post", "mid"] | None = None, *, data=None, **kwargs, ) -> FillBetweenPolyCollection: return gca().fill_between( x, y1, y2=y2, where=where, interpolate=interpolate, step=step, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.fill_betweenx) def fill_betweenx( y: ArrayLike, x1: ArrayLike | float, x2: ArrayLike | float = 0, where: Sequence[bool] | None = None, step: Literal["pre", "post", "mid"] | None = None, interpolate: bool = False, *, data=None, **kwargs, ) -> FillBetweenPolyCollection: return gca().fill_betweenx( y, x1, x2=x2, where=where, step=step, interpolate=interpolate, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.grid) def grid( visible: bool | None = None, which: Literal["major", "minor", "both"] = "major", axis: Literal["both", "x", "y"] = "both", **kwargs, ) -> None: gca().grid(visible=visible, which=which, axis=axis, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.hexbin) def hexbin( x: ArrayLike, y: ArrayLike, C: ArrayLike | None = None, gridsize: int | tuple[int, int] = 100, bins: Literal["log"] | int | Sequence[float] | None = None, xscale: Literal["linear", "log"] = "linear", yscale: Literal["linear", "log"] = "linear", extent: tuple[float, float, float, float] | None = None, cmap: str | Colormap | None = None, norm: str | Normalize | None = None, vmin: float | None = None, vmax: float | None = None, alpha: float | None = None, linewidths: float | None = None, edgecolors: Literal["face", "none"] | ColorType = "face", reduce_C_function: Callable[[np.ndarray | list[float]], float] = np.mean, mincnt: int | None = None, marginals: bool = False, colorizer: Colorizer | None = None, *, data=None, **kwargs, ) -> PolyCollection: __ret = gca().hexbin( x, y, C=C, gridsize=gridsize, bins=bins, xscale=xscale, yscale=yscale, extent=extent, cmap=cmap, norm=norm, vmin=vmin, vmax=vmax, alpha=alpha, linewidths=linewidths, edgecolors=edgecolors, reduce_C_function=reduce_C_function, mincnt=mincnt, marginals=marginals, colorizer=colorizer, **({"data": data} if data is not None else {}), **kwargs, ) sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.hist) def hist( x: ArrayLike | Sequence[ArrayLike], bins: int | Sequence[float] | str | None = None, range: tuple[float, float] | None = None, density: bool = False, weights: ArrayLike | None = None, cumulative: bool | float = False, bottom: ArrayLike | float | None = None, histtype: Literal["bar", "barstacked", "step", "stepfilled"] = "bar", align: Literal["left", "mid", "right"] = "mid", orientation: Literal["vertical", "horizontal"] = "vertical", rwidth: float | None = None, log: bool = False, color: ColorType | Sequence[ColorType] | None = None, label: str | Sequence[str] | None = None, stacked: bool = False, *, data=None, **kwargs, ) -> tuple[ np.ndarray | list[np.ndarray], np.ndarray, BarContainer | Polygon | list[BarContainer | Polygon], ]: return gca().hist( x, bins=bins, range=range, density=density, weights=weights, cumulative=cumulative, bottom=bottom, histtype=histtype, align=align, orientation=orientation, rwidth=rwidth, log=log, color=color, label=label, stacked=stacked, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.stairs) def stairs( values: ArrayLike, edges: ArrayLike | None = None, *, orientation: Literal["vertical", "horizontal"] = "vertical", baseline: float | ArrayLike | None = 0, fill: bool = False, data=None, **kwargs, ) -> StepPatch: return gca().stairs( values, edges=edges, orientation=orientation, baseline=baseline, fill=fill, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.hist2d) def hist2d( x: ArrayLike, y: ArrayLike, bins: None | int | tuple[int, int] | ArrayLike | tuple[ArrayLike, ArrayLike] = 10, range: ArrayLike | None = None, density: bool = False, weights: ArrayLike | None = None, cmin: float | None = None, cmax: float | None = None, *, data=None, **kwargs, ) -> tuple[np.ndarray, np.ndarray, np.ndarray, QuadMesh]: __ret = gca().hist2d( x, y, bins=bins, range=range, density=density, weights=weights, cmin=cmin, cmax=cmax, **({"data": data} if data is not None else {}), **kwargs, ) sci(__ret[-1]) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.hlines) def hlines( y: float | ArrayLike, xmin: float | ArrayLike, xmax: float | ArrayLike, colors: ColorType | Sequence[ColorType] | None = None, linestyles: LineStyleType = "solid", label: str = "", *, data=None, **kwargs, ) -> LineCollection: return gca().hlines( y, xmin, xmax, colors=colors, linestyles=linestyles, label=label, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.imshow) def imshow( X: ArrayLike | PIL.Image.Image, cmap: str | Colormap | None = None, norm: str | Normalize | None = None, *, aspect: Literal["equal", "auto"] | float | None = None, interpolation: str | None = None, alpha: float | ArrayLike | None = None, vmin: float | None = None, vmax: float | None = None, colorizer: Colorizer | None = None, origin: Literal["upper", "lower"] | None = None, extent: tuple[float, float, float, float] | None = None, interpolation_stage: Literal["data", "rgba", "auto"] | None = None, filternorm: bool = True, filterrad: float = 4.0, resample: bool | None = None, url: str | None = None, data=None, **kwargs, ) -> AxesImage: __ret = gca().imshow( X, cmap=cmap, norm=norm, aspect=aspect, interpolation=interpolation, alpha=alpha, vmin=vmin, vmax=vmax, colorizer=colorizer, origin=origin, extent=extent, interpolation_stage=interpolation_stage, filternorm=filternorm, filterrad=filterrad, resample=resample, url=url, **({"data": data} if data is not None else {}), **kwargs, ) sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.legend) def legend(*args, **kwargs) -> Legend: return gca().legend(*args, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.locator_params) def locator_params( axis: Literal["both", "x", "y"] = "both", tight: bool | None = None, **kwargs ) -> None: gca().locator_params(axis=axis, tight=tight, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.loglog) def loglog(*args, **kwargs) -> list[Line2D]: return gca().loglog(*args, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.magnitude_spectrum) def magnitude_spectrum( x: ArrayLike, Fs: float | None = None, Fc: int | None = None, window: Callable[[ArrayLike], ArrayLike] | ArrayLike | None = None, pad_to: int | None = None, sides: Literal["default", "onesided", "twosided"] | None = None, scale: Literal["default", "linear", "dB"] | None = None, *, data=None, **kwargs, ) -> tuple[np.ndarray, np.ndarray, Line2D]: return gca().magnitude_spectrum( x, Fs=Fs, Fc=Fc, window=window, pad_to=pad_to, sides=sides, scale=scale, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.margins) def margins( *margins: float, x: float | None = None, y: float | None = None, tight: bool | None = True, ) -> tuple[float, float] | None: return gca().margins(*margins, x=x, y=y, tight=tight) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.minorticks_off) def minorticks_off() -> None: gca().minorticks_off() # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.minorticks_on) def minorticks_on() -> None: gca().minorticks_on() # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.pcolor) def pcolor( *args: ArrayLike, shading: Literal["flat", "nearest", "auto"] | None = None, alpha: float | None = None, norm: str | Normalize | None = None, cmap: str | Colormap | None = None, vmin: float | None = None, vmax: float | None = None, colorizer: Colorizer | None = None, data=None, **kwargs, ) -> Collection: __ret = gca().pcolor( *args, shading=shading, alpha=alpha, norm=norm, cmap=cmap, vmin=vmin, vmax=vmax, colorizer=colorizer, **({"data": data} if data is not None else {}), **kwargs, ) sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.pcolormesh) def pcolormesh( *args: ArrayLike, alpha: float | None = None, norm: str | Normalize | None = None, cmap: str | Colormap | None = None, vmin: float | None = None, vmax: float | None = None, colorizer: Colorizer | None = None, shading: Literal["flat", "nearest", "gouraud", "auto"] | None = None, antialiased: bool = False, data=None, **kwargs, ) -> QuadMesh: __ret = gca().pcolormesh( *args, alpha=alpha, norm=norm, cmap=cmap, vmin=vmin, vmax=vmax, colorizer=colorizer, shading=shading, antialiased=antialiased, **({"data": data} if data is not None else {}), **kwargs, ) sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.phase_spectrum) def phase_spectrum( x: ArrayLike, Fs: float | None = None, Fc: int | None = None, window: Callable[[ArrayLike], ArrayLike] | ArrayLike | None = None, pad_to: int | None = None, sides: Literal["default", "onesided", "twosided"] | None = None, *, data=None, **kwargs, ) -> tuple[np.ndarray, np.ndarray, Line2D]: return gca().phase_spectrum( x, Fs=Fs, Fc=Fc, window=window, pad_to=pad_to, sides=sides, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.pie) def pie( x: ArrayLike, explode: ArrayLike | None = None, labels: Sequence[str] | None = None, colors: ColorType | Sequence[ColorType] | None = None, autopct: str | Callable[[float], str] | None = None, pctdistance: float = 0.6, shadow: bool = False, labeldistance: float | None = 1.1, startangle: float = 0, radius: float = 1, counterclock: bool = True, wedgeprops: dict[str, Any] | None = None, textprops: dict[str, Any] | None = None, center: tuple[float, float] = (0, 0), frame: bool = False, rotatelabels: bool = False, *, normalize: bool = True, hatch: str | Sequence[str] | None = None, data=None, ) -> tuple[list[Wedge], list[Text]] | tuple[list[Wedge], list[Text], list[Text]]: return gca().pie( x, explode=explode, labels=labels, colors=colors, autopct=autopct, pctdistance=pctdistance, shadow=shadow, labeldistance=labeldistance, startangle=startangle, radius=radius, counterclock=counterclock, wedgeprops=wedgeprops, textprops=textprops, center=center, frame=frame, rotatelabels=rotatelabels, normalize=normalize, hatch=hatch, **({"data": data} if data is not None else {}), ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.plot) def plot( *args: float | ArrayLike | str, scalex: bool = True, scaley: bool = True, data=None, **kwargs, ) -> list[Line2D]: return gca().plot( *args, scalex=scalex, scaley=scaley, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.plot_date) def plot_date( x: ArrayLike, y: ArrayLike, fmt: str = "o", tz: str | datetime.tzinfo | None = None, xdate: bool = True, ydate: bool = False, *, data=None, **kwargs, ) -> list[Line2D]: return gca().plot_date( x, y, fmt=fmt, tz=tz, xdate=xdate, ydate=ydate, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.psd) def psd( x: ArrayLike, NFFT: int | None = None, Fs: float | None = None, Fc: int | None = None, detrend: Literal["none", "mean", "linear"] | Callable[[ArrayLike], ArrayLike] | None = None, window: Callable[[ArrayLike], ArrayLike] | ArrayLike | None = None, noverlap: int | None = None, pad_to: int | None = None, sides: Literal["default", "onesided", "twosided"] | None = None, scale_by_freq: bool | None = None, return_line: bool | None = None, *, data=None, **kwargs, ) -> tuple[np.ndarray, np.ndarray] | tuple[np.ndarray, np.ndarray, Line2D]: return gca().psd( x, NFFT=NFFT, Fs=Fs, Fc=Fc, detrend=detrend, window=window, noverlap=noverlap, pad_to=pad_to, sides=sides, scale_by_freq=scale_by_freq, return_line=return_line, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.quiver) def quiver(*args, data=None, **kwargs) -> Quiver: __ret = gca().quiver( *args, **({"data": data} if data is not None else {}), **kwargs ) sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.quiverkey) def quiverkey( Q: Quiver, X: float, Y: float, U: float, label: str, **kwargs ) -> QuiverKey: return gca().quiverkey(Q, X, Y, U, label, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.scatter) def scatter( x: float | ArrayLike, y: float | ArrayLike, s: float | ArrayLike | None = None, c: ArrayLike | Sequence[ColorType] | ColorType | None = None, marker: MarkerType | None = None, cmap: str | Colormap | None = None, norm: str | Normalize | None = None, vmin: float | None = None, vmax: float | None = None, alpha: float | None = None, linewidths: float | Sequence[float] | None = None, *, edgecolors: Literal["face", "none"] | ColorType | Sequence[ColorType] | None = None, colorizer: Colorizer | None = None, plotnonfinite: bool = False, data=None, **kwargs, ) -> PathCollection: __ret = gca().scatter( x, y, s=s, c=c, marker=marker, cmap=cmap, norm=norm, vmin=vmin, vmax=vmax, alpha=alpha, linewidths=linewidths, edgecolors=edgecolors, colorizer=colorizer, plotnonfinite=plotnonfinite, **({"data": data} if data is not None else {}), **kwargs, ) sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.semilogx) def semilogx(*args, **kwargs) -> list[Line2D]: return gca().semilogx(*args, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.semilogy) def semilogy(*args, **kwargs) -> list[Line2D]: return gca().semilogy(*args, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.specgram) def specgram( x: ArrayLike, NFFT: int | None = None, Fs: float | None = None, Fc: int | None = None, detrend: Literal["none", "mean", "linear"] | Callable[[ArrayLike], ArrayLike] | None = None, window: Callable[[ArrayLike], ArrayLike] | ArrayLike | None = None, noverlap: int | None = None, cmap: str | Colormap | None = None, xextent: tuple[float, float] | None = None, pad_to: int | None = None, sides: Literal["default", "onesided", "twosided"] | None = None, scale_by_freq: bool | None = None, mode: Literal["default", "psd", "magnitude", "angle", "phase"] | None = None, scale: Literal["default", "linear", "dB"] | None = None, vmin: float | None = None, vmax: float | None = None, *, data=None, **kwargs, ) -> tuple[np.ndarray, np.ndarray, np.ndarray, AxesImage]: __ret = gca().specgram( x, NFFT=NFFT, Fs=Fs, Fc=Fc, detrend=detrend, window=window, noverlap=noverlap, cmap=cmap, xextent=xextent, pad_to=pad_to, sides=sides, scale_by_freq=scale_by_freq, mode=mode, scale=scale, vmin=vmin, vmax=vmax, **({"data": data} if data is not None else {}), **kwargs, ) sci(__ret[-1]) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.spy) def spy( Z: ArrayLike, precision: float | Literal["present"] = 0, marker: str | None = None, markersize: float | None = None, aspect: Literal["equal", "auto"] | float | None = "equal", origin: Literal["upper", "lower"] = "upper", **kwargs, ) -> AxesImage: __ret = gca().spy( Z, precision=precision, marker=marker, markersize=markersize, aspect=aspect, origin=origin, **kwargs, ) if isinstance(__ret, _ColorizerInterface): sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.stackplot) def stackplot( x, *args, labels=(), colors=None, hatch=None, baseline="zero", data=None, **kwargs ): return gca().stackplot( x, *args, labels=labels, colors=colors, hatch=hatch, baseline=baseline, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.stem) def stem( *args: ArrayLike | str, linefmt: str | None = None, markerfmt: str | None = None, basefmt: str | None = None, bottom: float = 0, label: str | None = None, orientation: Literal["vertical", "horizontal"] = "vertical", data=None, ) -> StemContainer: return gca().stem( *args, linefmt=linefmt, markerfmt=markerfmt, basefmt=basefmt, bottom=bottom, label=label, orientation=orientation, **({"data": data} if data is not None else {}), ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.step) def step( x: ArrayLike, y: ArrayLike, *args, where: Literal["pre", "post", "mid"] = "pre", data=None, **kwargs, ) -> list[Line2D]: return gca().step( x, y, *args, where=where, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.streamplot) def streamplot( x, y, u, v, density=1, linewidth=None, color=None, cmap=None, norm=None, arrowsize=1, arrowstyle="-|>", minlength=0.1, transform=None, zorder=None, start_points=None, maxlength=4.0, integration_direction="both", broken_streamlines=True, *, data=None, ): __ret = gca().streamplot( x, y, u, v, density=density, linewidth=linewidth, color=color, cmap=cmap, norm=norm, arrowsize=arrowsize, arrowstyle=arrowstyle, minlength=minlength, transform=transform, zorder=zorder, start_points=start_points, maxlength=maxlength, integration_direction=integration_direction, broken_streamlines=broken_streamlines, **({"data": data} if data is not None else {}), ) sci(__ret.lines) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.table) def table( cellText=None, cellColours=None, cellLoc="right", colWidths=None, rowLabels=None, rowColours=None, rowLoc="left", colLabels=None, colColours=None, colLoc="center", loc="bottom", bbox=None, edges="closed", **kwargs, ): return gca().table( cellText=cellText, cellColours=cellColours, cellLoc=cellLoc, colWidths=colWidths, rowLabels=rowLabels, rowColours=rowColours, rowLoc=rowLoc, colLabels=colLabels, colColours=colColours, colLoc=colLoc, loc=loc, bbox=bbox, edges=edges, **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.text) def text( x: float, y: float, s: str, fontdict: dict[str, Any] | None = None, **kwargs ) -> Text: return gca().text(x, y, s, fontdict=fontdict, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.tick_params) def tick_params(axis: Literal["both", "x", "y"] = "both", **kwargs) -> None: gca().tick_params(axis=axis, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.ticklabel_format) def ticklabel_format( *, axis: Literal["both", "x", "y"] = "both", style: Literal["", "sci", "scientific", "plain"] | None = None, scilimits: tuple[int, int] | None = None, useOffset: bool | float | None = None, useLocale: bool | None = None, useMathText: bool | None = None, ) -> None: gca().ticklabel_format( axis=axis, style=style, scilimits=scilimits, useOffset=useOffset, useLocale=useLocale, useMathText=useMathText, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.tricontour) def tricontour(*args, **kwargs): __ret = gca().tricontour(*args, **kwargs) if __ret._A is not None: # type: ignore[attr-defined] sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.tricontourf) def tricontourf(*args, **kwargs): __ret = gca().tricontourf(*args, **kwargs) if __ret._A is not None: # type: ignore[attr-defined] sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.tripcolor) def tripcolor( *args, alpha=1.0, norm=None, cmap=None, vmin=None, vmax=None, shading="flat", facecolors=None, **kwargs, ): __ret = gca().tripcolor( *args, alpha=alpha, norm=norm, cmap=cmap, vmin=vmin, vmax=vmax, shading=shading, facecolors=facecolors, **kwargs, ) sci(__ret) return __ret # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.triplot) def triplot(*args, **kwargs): return gca().triplot(*args, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.violinplot) def violinplot( dataset: ArrayLike | Sequence[ArrayLike], positions: ArrayLike | None = None, vert: bool | None = None, orientation: Literal["vertical", "horizontal"] = "vertical", widths: float | ArrayLike = 0.5, showmeans: bool = False, showextrema: bool = True, showmedians: bool = False, quantiles: Sequence[float | Sequence[float]] | None = None, points: int = 100, bw_method: Literal["scott", "silverman"] | float | Callable[[GaussianKDE], float] | None = None, side: Literal["both", "low", "high"] = "both", *, data=None, ) -> dict[str, Collection]: return gca().violinplot( dataset, positions=positions, vert=vert, orientation=orientation, widths=widths, showmeans=showmeans, showextrema=showextrema, showmedians=showmedians, quantiles=quantiles, points=points, bw_method=bw_method, side=side, **({"data": data} if data is not None else {}), ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.vlines) def vlines( x: float | ArrayLike, ymin: float | ArrayLike, ymax: float | ArrayLike, colors: ColorType | Sequence[ColorType] | None = None, linestyles: LineStyleType = "solid", label: str = "", *, data=None, **kwargs, ) -> LineCollection: return gca().vlines( x, ymin, ymax, colors=colors, linestyles=linestyles, label=label, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.xcorr) def xcorr( x: ArrayLike, y: ArrayLike, normed: bool = True, detrend: Callable[[ArrayLike], ArrayLike] = mlab.detrend_none, usevlines: bool = True, maxlags: int = 10, *, data=None, **kwargs, ) -> tuple[np.ndarray, np.ndarray, LineCollection | Line2D, Line2D | None]: return gca().xcorr( x, y, normed=normed, detrend=detrend, usevlines=usevlines, maxlags=maxlags, **({"data": data} if data is not None else {}), **kwargs, ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes._sci) def sci(im: ColorizingArtist) -> None: gca()._sci(im) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.set_title) def title( label: str, fontdict: dict[str, Any] | None = None, loc: Literal["left", "center", "right"] | None = None, pad: float | None = None, *, y: float | None = None, **kwargs, ) -> Text: return gca().set_title(label, fontdict=fontdict, loc=loc, pad=pad, y=y, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.set_xlabel) def xlabel( xlabel: str, fontdict: dict[str, Any] | None = None, labelpad: float | None = None, *, loc: Literal["left", "center", "right"] | None = None, **kwargs, ) -> Text: return gca().set_xlabel( xlabel, fontdict=fontdict, labelpad=labelpad, loc=loc, **kwargs ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.set_ylabel) def ylabel( ylabel: str, fontdict: dict[str, Any] | None = None, labelpad: float | None = None, *, loc: Literal["bottom", "center", "top"] | None = None, **kwargs, ) -> Text: return gca().set_ylabel( ylabel, fontdict=fontdict, labelpad=labelpad, loc=loc, **kwargs ) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.set_xscale) def xscale(value: str | ScaleBase, **kwargs) -> None: gca().set_xscale(value, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. @_copy_docstring_and_deprecators(Axes.set_yscale) def yscale(value: str | ScaleBase, **kwargs) -> None: gca().set_yscale(value, **kwargs) # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def autumn() -> None: """ Set the colormap to 'autumn'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("autumn") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def bone() -> None: """ Set the colormap to 'bone'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("bone") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def cool() -> None: """ Set the colormap to 'cool'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("cool") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def copper() -> None: """ Set the colormap to 'copper'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("copper") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def flag() -> None: """ Set the colormap to 'flag'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("flag") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def gray() -> None: """ Set the colormap to 'gray'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("gray") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def hot() -> None: """ Set the colormap to 'hot'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("hot") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def hsv() -> None: """ Set the colormap to 'hsv'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("hsv") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def jet() -> None: """ Set the colormap to 'jet'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("jet") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def pink() -> None: """ Set the colormap to 'pink'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("pink") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def prism() -> None: """ Set the colormap to 'prism'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("prism") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def spring() -> None: """ Set the colormap to 'spring'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("spring") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def summer() -> None: """ Set the colormap to 'summer'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("summer") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def winter() -> None: """ Set the colormap to 'winter'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("winter") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def magma() -> None: """ Set the colormap to 'magma'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("magma") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def inferno() -> None: """ Set the colormap to 'inferno'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("inferno") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def plasma() -> None: """ Set the colormap to 'plasma'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("plasma") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def viridis() -> None: """ Set the colormap to 'viridis'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("viridis") # Autogenerated by boilerplate.py. Do not edit as changes will be lost. def nipy_spectral() -> None: """ Set the colormap to 'nipy_spectral'. This changes the default colormap as well as the colormap of the current image if there is one. See ``help(colormaps)`` for more information. """ set_cmap("nipy_spectral") 