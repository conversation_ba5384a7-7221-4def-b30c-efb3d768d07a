# -*- coding: utf-8 -*- """Pen to rasterize paths with FreeType.""" __all__ = ["FreeTypePen"] import os import ctypes import platform import subprocess import collections import math import freetype from freetype.raw import FT_Outline_Get_Bitmap, FT_Outline_Get_BBox, FT_Outline_Get_CBox from freetype.ft_types import FT_Pos from freetype.ft_structs import FT_Vector, FT_BBox, FT_Bitmap, FT_Outline from freetype.ft_enums import ( FT_OUTLINE_NONE, FT_OUTLINE_EVEN_ODD_FILL, FT_PIXEL_MODE_GRAY, FT_CURVE_TAG_ON, FT_CURVE_TAG_CONIC, FT_CURVE_TAG_CUBIC, ) from freetype.ft_errors import FT_Exception from fontTools.pens.basePen import BasePen, PenError from fontTools.misc.roundTools import otRound from fontTools.misc.transform import Transform Contour = collections.namedtuple("Contour", ("points", "tags")) class FreeTypePen(BasePen): """Pen to rasterize paths with FreeType. Requires `freetype-py` module. Constructs ``FT_Outline`` from the paths, and renders it within a bitmap buffer. For ``array()`` and ``show()``, `numpy` and `matplotlib` must be installed. For ``image()``, `Pillow` is required. Each module is lazily loaded when the corresponding method is called. Args: glyphSet: a dictionary of drawable glyph objects keyed by name used to resolve component references in composite glyphs. Examples: If `numpy` and `matplotlib` is available, the following code will show the glyph image of `fi` in a new window:: from fontTools.ttLib import TTFont from fontTools.pens.freetypePen import FreeTypePen from fontTools.misc.transform import Offset pen = FreeTypePen(None) font = TTFont('SourceSansPro-Regular.otf') glyph = font.getGlyphSet()['fi'] glyph.draw(pen) width, ascender, descender = glyph.width, font['OS/2'].usWinAscent, -font['OS/2'].usWinDescent height = ascender - descender pen.show(width=width, height=height, transform=Offset(0, -descender)) Combining with `uharfbuzz`, you can typeset a chunk of glyphs in a pen:: import uharfbuzz as hb from fontTools.pens.freetypePen import FreeTypePen from fontTools.pens.transformPen import TransformPen from fontTools.misc.transform import Offset en1, en2, ar, ja = 'Typesetting', 'Jeff', 'صف الحروف', '' for text, font_path, direction, typo_ascender, typo_descender, vhea_ascender, vhea_descender, contain, features in ( (en1, 'NotoSans-Regular.ttf', 'ltr', 2189, -600, None, None, False, {"kern": True, "liga": True}), (en2, 'NotoSans-Regular.ttf', 'ltr', 2189, -600, None, None, True, {"kern": True, "liga": True}), (ar, 'NotoSansArabic-Regular.ttf', 'rtl', 1374, -738, None, None, False, {"kern": True, "liga": True}), (ja, 'NotoSansJP-Regular.otf', 'ltr', 880, -120, 500, -500, False, {"palt": True, "kern": True}), (ja, 'NotoSansJP-Regular.otf', 'ttb', 880, -120, 500, -500, False, {"vert": True, "vpal": True, "vkrn": True}) ): blob = hb.Blob.from_file_path(font_path) face = hb.Face(blob) font = hb.Font(face) buf = hb.Buffer() buf.direction = direction buf.add_str(text) buf.guess_segment_properties() hb.shape(font, buf, features) x, y = 0, 0 pen = FreeTypePen(None) for info, pos in zip(buf.glyph_infos, buf.glyph_positions): gid = info.codepoint transformed = TransformPen(pen, Offset(x + pos.x_offset, y + pos.y_offset)) font.draw_glyph_with_pen(gid, transformed) x += pos.x_advance y += pos.y_advance offset, width, height = None, None, None if direction in ('ltr', 'rtl'): offset = (0, -typo_descender) width = x height = typo_ascender - typo_descender else: offset = (-vhea_descender, -y) width = vhea_ascender - vhea_descender height = -y pen.show(width=width, height=height, transform=Offset(*offset), contain=contain) For Jupyter Notebook, the rendered image will be displayed in a cell if you replace ``show()`` with ``image()`` in the examples. """ def __init__(self, glyphSet): BasePen.__init__(self, glyphSet) self.contours = [] def outline(self, transform=None, evenOdd=False): """Converts the current contours to ``FT_Outline``. Args: 