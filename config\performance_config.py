# -*- coding: utf-8 -*-
"""
إعدادات تحسين الأداء للبرنامج
Performance Configuration for the Application
"""

import sys
import gc

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'pool_size': 10,
    'max_overflow': 20,
    'pool_timeout': 30,
    'pool_recycle': 3600,
    'echo': False  # تعطيل طباعة SQL للأداء
}

# إعدادات الجداول
TABLE_CONFIG = {
    'batch_size': 100,  # عدد الصفوف المحملة في كل دفعة
    'lazy_loading': True,  # تحميل البيانات عند الحاجة
    'cache_size': 1000,  # حجم الذاكرة المؤقتة
    'auto_refresh_interval': 30000,  # 30 ثانية
}

# إعدادات الواجهة
UI_CONFIG = {
    'animation_duration': 200,  # مدة الحركات بالميلي ثانية
    'debounce_delay': 300,  # تأخير البحث بالميلي ثانية
    'max_visible_rows': 1000,  # أقصى عدد صفوف مرئية
    'virtual_scrolling': True,  # التمرير الافتراضي
}

# إعدادات التقارير
REPORTS_CONFIG = {
    'max_records': 10000,  # أقصى عدد سجلات في التقرير
    'export_chunk_size': 500,  # حجم دفعة التصدير
    'cache_reports': True,  # حفظ التقارير في الذاكرة المؤقتة
    'cache_duration': 300,  # مدة الحفظ بالثواني
}

# إعدادات الذاكرة
MEMORY_CONFIG = {
    'gc_threshold': 100,  # عتبة تنظيف الذاكرة
    'max_cache_size': 50 * 1024 * 1024,  # 50 MB
    'cleanup_interval': 60000,  # دقيقة واحدة
}

# إعدادات الشبكة (للمستقبل)
NETWORK_CONFIG = {
    'timeout': 30,
    'retry_attempts': 3,
    'connection_pool_size': 5,
}

def get_config(section):
    """الحصول على إعدادات قسم معين"""
    configs = {
        'database': DATABASE_CONFIG,
        'table': TABLE_CONFIG,
        'ui': UI_CONFIG,
        'reports': REPORTS_CONFIG,
        'memory': MEMORY_CONFIG,
        'network': NETWORK_CONFIG,
    }
    return configs.get(section, {})

def apply_performance_settings():
    """تطبيق إعدادات الأداء"""
    # تحسين جمع القمامة
    gc.set_threshold(
        MEMORY_CONFIG['gc_threshold'],
        MEMORY_CONFIG['gc_threshold'] * 10,
        MEMORY_CONFIG['gc_threshold'] * 100
    )

    # تفعيل التحسينات
    if hasattr(sys, 'setswitchinterval'):
        sys.setswitchinterval(0.005)  # تحسين التبديل بين الخيوط

if __name__ == "__main__":
    print("إعدادات تحسين الأداء:")
    for section, config in [
        ('قاعدة البيانات', DATABASE_CONFIG),
        ('الجداول', TABLE_CONFIG),
        ('الواجهة', UI_CONFIG),
        ('التقارير', REPORTS_CONFIG),
        ('الذاكرة', MEMORY_CONFIG),
    ]:
        print(f"\n{section}:")
        for key, value in config.items():
            print(f"  {key}: {value}")