from PyQt5.QtCore import * from datetime import datetime import sys from PyQt5.QtGui import * from PyQt5.QtWidgets import * class PurchasesWidget(QWidget): def __init__(self, session): super().__init__() self.session = session self.init_ui() self.create_sample_data() def init_ui(self): # إنشاء التخطيط الرئيسي main_layout = QVBoxLayout() main_layout.setContentsMargins(10, 10, 10, 10) main_layout.setSpacing(8) # العنوان الرئيسي title_label = QLabel(" إدارة المشتريات المتطورة - نظام شامل ومتقدم لإدارة المشتريات مع أدوات احترافية للبحث والتحليل والتقارير") title_label.setFont(QFont("Segoe UI", 18, QFont.Bold)) title_label.setAlignment(Qt.AlignCenter) title_label.setStyleSheet(""" QLabel { color: #ffffff; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e40af, stop:0.2 #3b82f6, stop:0.4 #6366f1, stop:0.6 #8b5cf6, stop:0.8 #a855f7, stop:1 #c084fc); border: 3px solid #000000; border-radius: 10px; padding: 4px 10px; margin: 2px; font-weight: bold; max-height: 40px; min-height: 40px; } main_layout.addWidget(title_label) # إطار البحث search_frame = QFrame() search_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } search_layout = QHBoxLayout() search_layout.setContentsMargins(10, 10, 10, 10) search_layout.setSpacing(10) # البحث search_label = QLabel(" بحث:") search_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b); border: 3px solid #7f1d1d; border-radius: 12px; min-width: 70px; max-width: 70px; max-height: 38px; min-height: 34px; } search_label.setAlignment(Qt.AlignCenter) self.search_edit = QLineEdit() self.search_edit.setPlaceholderText(" ابحث بالمورد، رقم الفاتورة أو المنتج...") self.search_edit.textChanged.connect(self.filter_purchases) self.search_edit.setStyleSheet(""" QLineEdit { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; } # فلتر الحالة status_label = QLabel(" حالة:") status_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309); border: 3px solid #92400e; border-radius: 12px; min-width: 65px; max-width: 65px; max-height: 38px; min-height: 34px; } status_label.setAlignment(Qt.AlignCenter) self.status_filter = QComboBox() self.status_filter.addItems(["جميع الحالات", "مكتملة", "معلقة", "ملغية"]) self.status_filter.currentIndexChanged.connect(self.filter_purchases) self.status_filter.setStyleSheet(""" QComboBox { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; min-width: 120px; } search_layout.addWidget(search_label) search_layout.addWidget(self.search_edit, 1) search_layout.addWidget(status_label) search_layout.addWidget(self.status_filter) search_layout.addStretch() search_frame.setLayout(search_layout) main_layout.addWidget(search_frame) # جدول المشتريات self.purchases_table = QTableWidget() self.purchases_table.setColumnCount(9) self.purchases_table.setHorizontalHeaderLabels([ "رقم الفاتورة", "المورد", "التاريخ", "المنتج", "الكمية", "سعر الوحدة", "الإجمالي", "الحالة", "ملاحظات" ]) # تصميم الجدول self.purchases_table.setStyleSheet(""" QTableWidget { background-color: #ffffff; border: 3px solid #000000; border-radius: 10px; gridline-color: #e5e7eb; font-size: 12px; selection-background-color: #3b82f6; selection-color: white; } QTableWidget::item { padding: 8px; border-bottom: 1px solid #e5e7eb; font-weight: bold; color: #000000; } QHeaderView::section { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4f46e5, stop:1 #3730a3); color: white; padding: 10px; border: 2px solid #1e1b4b; font-weight: bold; font-size: 13px; } # تعديل عرض الأعمدة header = self.purchases_table.horizontalHeader() header.setSectionResizeMode(0, QHeaderView.Fixed) # رقم الفاتورة header.setSectionResizeMode(1, QHeaderView.Stretch) # المورد header.setSectionResizeMode(2, QHeaderView.Fixed) # التاريخ header.setSectionResizeMode(3, QHeaderView.Stretch) # المنتج header.setSectionResizeMode(4, QHeaderView.Fixed) # الكمية header.setSectionResizeMode(5, QHeaderView.Fixed) # سعر الوحدة header.setSectionResizeMode(6, QHeaderView.Fixed) # الإجمالي header.setSectionResizeMode(7, QHeaderView.Fixed) # الحالة header.setSectionResizeMode(8, QHeaderView.Stretch) # ملاحظات # تحديد عرض الأعمدة self.purchases_table.setColumnWidth(0, 100) # رقم الفاتورة self.purchases_table.setColumnWidth(2, 120) # التاريخ self.purchases_table.setColumnWidth(4, 80) # الكمية self.purchases_table.setColumnWidth(5, 100) # سعر الوحدة self.purchases_table.setColumnWidth(6, 100) # الإجمالي self.purchases_table.setColumnWidth(7, 80) # الحالة main_layout.addWidget(self.purchases_table, 1) # إطار الأزرار buttons_frame = QFrame() buttons_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 5px; max-height: 70px; min-height: 65px; } buttons_layout = QHBoxLayout() buttons_layout.setContentsMargins(10, 5, 10, 5) buttons_layout.setSpacing(10) # أزرار العمليات مثل المخزون مع ألوان متنوعة self.add_button = QPushButton(" إضافة مشترى ") self.style_advanced_button(self.add_button, 'emerald', has_menu=True) # أخضر زمردي مميز مع قائمة self.add_button.clicked.connect(self.add_purchase) self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.edit_button = QPushButton(" تعديل") self.style_advanced_button(self.edit_button, 'primary') # أزرق كلاسيكي self.edit_button.clicked.connect(self.edit_purchase) self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.delete_button = QPushButton(" حذف") self.style_advanced_button(self.delete_button, 'danger') # أحمر تحذيري self.delete_button.clicked.connect(self.delete_purchase) self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.refresh_button = QPushButton(" تحديث") self.style_advanced_button(self.refresh_button, 'modern_teal') # تصميم حديث ومتطور self.refresh_button.clicked.connect(self.refresh_data) self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # المجموعة الثانية - العمليات المتقدمة self.view_button = QPushButton(" عرض التفاصيل ") self.style_advanced_button(self.view_button, 'indigo', has_menu=True) # بنفسجي للتفاصيل self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.export_button = QPushButton(" تصدير ") self.style_advanced_button(self.export_button, 'info', has_menu=True) # لون متسق مع نظام الألوان self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.statistics_button = QPushButton(" الإحصائيات") self.style_advanced_button(self.statistics_button, 'rose') # وردي للإحصائيات self.statistics_button.clicked.connect(self.show_statistics) self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إضافة ملخص المشتريات محسن مثل المخزون self.total_purchases_label = QLabel("إجمالي المشتريات: 0 | القيمة الإجمالية: 0.00 ج.م") self.total_purchases_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 16px; font-weight: bold; padding: 8px 16px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8); border: 3px solid #1e40af; border-radius: 12px; min-height: 34px; max-height: 38px; } self.total_purchases_label.setAlignment(Qt.AlignCenter) # إضافة الأزرار للتخطيط buttons_layout.addWidget(self.add_button) buttons_layout.addWidget(self.edit_button) buttons_layout.addWidget(self.delete_button) buttons_layout.addWidget(self.refresh_button) buttons_layout.addWidget(self.view_button) buttons_layout.addWidget(self.export_button) buttons_layout.addWidget(self.statistics_button) buttons_layout.addWidget(self.total_purchases_label) buttons_frame.setLayout(buttons_layout) main_layout.addWidget(buttons_frame) self.setLayout(main_layout) def filter_purchases(self): """تصفية المشتريات""" search_text = self.search_edit.text().lower() status_filter = self.status_filter.currentText() for row in range(self.purchases_table.rowCount()): show_row = True # فلترة النص if search_text: row_text = "" for col in range(self.purchases_table.columnCount()): item = self.purchases_table.item(row, col) if item: row_text += item.text().lower() + " " if search_text not in row_text: show_row = False # فلترة الحالة if status_filter != "جميع الحالات": status_item = self.purchases_table.item(row, 7) if status_item and status_item.text() != status_filter: show_row = False self.purchases_table.setRowHidden(row, not show_row) # تحديث الملخص بعد التصفية self.update_summary() def create_sample_data(self): sample_data = [ ["P001", "شركة الدهانات المتحدة", "2024-01-15", "دهان أبيض", "50", "25.00", "1250.00", "مكتملة", "تم التسليم"], ["P002", "مؤسسة السيراميك", "2024-01-16", "سيراميك أرضي", "100", "15.00", "1500.00", "معلقة", "في الانتظار"], ["P003", "شركة الأخشاب", "2024-01-17", "خشب صنوبر", "20", "100.00", "2000.00", "مكتملة", "تم التسليم"], ["P004", "مؤسسة الكهرباء", "2024-01-18", "مفاتيح كهربائية", "200", "5.00", "1000.00", "مكتملة", "تم التسليم"], ["P005", "شركة الأدوات الصحية", "2024-01-19", "حنفيات مياه", "30", "45.00", "1350.00", "معلقة", "قيد المراجعة"] ] self.purchases_table.setRowCount(len(sample_data)) for row, data in enumerate(sample_data): for col, value in enumerate(data): item = QTableWidgetItem(str(value)) item.setFont(QFont("Arial", 10, QFont.Bold)) item.setForeground(QColor("#000000")) # تلوين حسب الحالة if col == 7: # عمود الحالة if value == "مكتملة": item.setForeground(QColor("#059669")) elif value == "معلقة": item.setForeground(QColor("#d97706")) elif value == "ملغية": item.setForeground(QColor("#dc2626")) self.purchases_table.setItem(row, col, item) # تحديث الملخص self.update_summary() def add_purchase(self): """إضافة مشترى جديد""" QMessageBox.information(self, "قريباً", "ميزة إضافة مشترى جديد ستكون متاحة قريباً!") def edit_purchase(self): current_row = self.purchases_table.currentRow() if current_row >= 0: QMessageBox.information(self, "قريباً", "ميزة تعديل المشترى ستكون متاحة قريباً!") else: QMessageBox.warning(self, "تحذير", "يرجى اختيار مشترى للتعديل") def delete_purchase(self): """حذف مشترى""" current_row = self.purchases_table.currentRow() if current_row >= 0: reply = QMessageBox.question(self, "تأكيد الحذف", "هل أنت متأكد من حذف هذا المشترى؟") if reply == QMessageBox.Yes: self.purchases_table.removeRow(current_row) self.update_summary() QMessageBox.information(self, "نجح", "تم حذف المشترى بنجاح!") else: QMessageBox.warning(self, "تحذير", "يرجى اختيار مشترى للحذف") def refresh_data(self): self.create_sample_data() QMessageBox.information(self, "تم التحديث", "تم تحديث بيانات المشتريات بنجاح!") def update_summary(self): """تحديث ملخص المشتريات""" try: total_count = 0 total_value = 0.0 for row in range(self.purchases_table.rowCount()): if not self.purchases_table.isRowHidden(row): total_count += 1 total_item = self.purchases_table.item(row, 6) # عمود الإجمالي if total_item: total_value += float(total_item.text()) self.total_purchases_label.setText(f"إجمالي المشتريات: {total_count} | القيمة الإجمالية: {total_value:.2f} ج.م") except Exception as e: print(f"خطأ في تحديث الملخص: {str(e)}") def show_statistics(self): QMessageBox.information(self, "إحصائيات المشتريات", "سيتم إضافة ميزة الإحصائيات قريباً") def style_advanced_button(self, button, button_type, has_menu=False): """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة""" try: # تحديد الألوان المتنوعة والمميزة حسب نوع الزر colors = { 'primary': { 'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #3b82f6, stop:0.5 #2563eb, stop:1 #1d4ed8)', 'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #60a5fa, stop:0.5 #3b82f6, stop:1 #2563eb)', 'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1d4ed8, stop:1 #1e40af)', 'border': '#1e40af' }, 'emerald': { 'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #10b981, stop:0.5 #059669, stop:1 #047857)', 'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #34d399, stop:0.5 #10b981, stop:1 #059669)', 'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #047857, stop:1 #065f46)', 'border': '#065f46' }, 'danger': { 'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c)', 'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f87171, stop:0.5 #ef4444, stop:1 #dc2626)', 'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #b91c1c, stop:1 #991b1b)', 'border': '#991b1b' }, 'modern_teal': { 'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #14b8a6, stop:0.5 #0d9488, stop:1 #0f766e)', 'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5eead4, stop:0.5 #14b8a6, stop:1 #0d9488)', 'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0f766e, stop:1 #134e4a)', 'border': '#134e4a' }, 'indigo': { 'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #6366f1, stop:0.5 #4f46e5, stop:1 #4338ca)', 'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #a5b4fc, stop:0.5 #6366f1, stop:1 #4f46e5)', 'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4338ca, stop:1 #3730a3)', 'border': '#3730a3' }, 'info': { 'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #06b6d4, stop:0.5 #0891b2, stop:1 #0e7490)', 'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #67e8f9, stop:0.5 #06b6d4, stop:1 #0891b2)', 'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75)', 'border': '#155e75' }, 'rose': { 'normal': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f43f5e, stop:0.5 #e11d48, stop:1 #be123c)', 'hover': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fb7185, stop:0.5 #f43f5e, stop:1 #e11d48)', 'pressed': 'qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #be123c, stop:1 #9f1239)', 'border': '#9f1239' } } # الحصول على ألوان الزر color_scheme = colors.get(button_type, colors['primary']) # تطبيق التصميم المتطور QPushButton {{ background: {color_scheme['normal']}; color: #ffffff; border: 3px solid {color_scheme['border']}; border-radius: 12px; padding: 10px 20px; font-size: 14px; font-weight: bold; min-height: 35px; max-height: 45px; }} QPushButton:hover {{ background: {color_scheme['hover']}; border: 3px solid {color_scheme['border']}; transform: translateY(-2px); }} QPushButton:pressed {{ background: {color_scheme['pressed']}; border: 3px solid {color_scheme['border']}; transform: translateY(1px); }} QPushButton:disabled {{ background: #9ca3af; color: #6b7280; border: 3px solid #6b7280; }} """ button.setStyleSheet(style) print(f" تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}") except Exception as e: print(f" خطأ في تطبيق تصميم الزر: {str(e)}") # تطبيق تصميم بسيط في حالة الخطأ QPushButton { background-color: #3b82f6; color: white; border: 2px solid #1e40af; border-radius: 8px; padding: 8px 16px; font-weight: bold; } QPushButton:hover { background-color: #2563eb; } """)