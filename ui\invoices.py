import json from PyQt5.QtCore import Qt, pyqtSignal, QDate import datetime import csv from PyQt5.QtGui import QIcon, QFont, QTextDocument from PyQt5.QtPrintSupport import QPrinter, QPrintDialog from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, from utils import (show_error_message, show_info_message, show_confirmation_message, from database import Invoice, InvoiceItem, Client, Revenue from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox, QLabel, QLineEdit, QTableWidget, QTableWidgetItem, QFormLayout, QTextEdit, QHeaderView, QMessageBox, QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox, QSpinBox, QTabWidget, <PERSON>Split<PERSON>, Q<PERSON>rame, QTextBrowser, QFileDialog, QMenu, QAction, QSizePolicy) qdate_to_datetime, datetime_to_qdate, format_currency, format_quantity, generate_invoice_number, calculate_invoice_balance) StyledTable, StyledLabel) class InvoiceItemDialog(QDialog): def __init__(self, parent=None, item=None): super().__init__(parent) self.item = item self.init_ui() def init_ui(self): # إعداد نافذة الحوار if self.item: self.setWindowTitle("تعديل عنصر الفاتورة") else: self.setWindowTitle("إضافة عنصر جديد للفاتورة") self.setMinimumWidth(400) # إنشاء النموذج form_layout = QFormLayout() # حقل الوصف self.description_edit = QLineEdit() self.description_edit.setStyleSheet(UnifiedStyles.get_input_style()) if self.item: self.description_edit.setText(self.item.description) form_layout.addRow("الوصف:", self.description_edit) # حقل الكمية self.quantity_edit = QDoubleSpinBox() self.quantity_edit.setRange(1, 10000) # الحد الأدنى 1 بدلاً من 0.01 self.quantity_edit.setDecimals(0) # بدون كسور عشرية self.quantity_edit.setSingleStep(1) self.quantity_edit.setValue(1) if self.item: self.quantity_edit.setValue(self.item.quantity) self.quantity_edit.valueChanged.connect(self.calculate_total) form_layout.addRow("الكمية:", self.quantity_edit) # حقل سعر الوحدة self.unit_price_edit = QDoubleSpinBox() self.unit_price_edit.setRange(1, 1000000) # الحد الأدنى 1 بدلاً من 0.01 self.unit_price_edit.setDecimals(0) # بدون كسور عشرية self.unit_price_edit.setSingleStep(10) if self.item: self.unit_price_edit.setValue(self.item.unit_price) self.unit_price_edit.valueChanged.connect(self.calculate_total) form_layout.addRow("سعر الوحدة:", self.unit_price_edit) # حقل السعر الإجمالي self.total_price_label = QLabel("0.00") form_layout.addRow("السعر الإجمالي:", self.total_price_label) # حساب السعر الإجمالي الأولي self.calculate_total() # أزرار الحفظ والإلغاء button_layout = QHBoxLayout() self.save_button = StyledButton(" حفظ", "success", "normal") self.save_button.clicked.connect(self.accept) self.cancel_button = StyledButton(" إلغاء", "secondary", "normal") self.cancel_button.clicked.connect(self.reject) button_layout.addWidget(self.save_button.button) button_layout.addWidget(self.cancel_button.button) # تجميع التخطيط النهائي main_layout = QVBoxLayout() main_layout.addLayout(form_layout) main_layout.addLayout(button_layout) self.setLayout(main_layout) def calculate_total(self): """حساب السعر الإجمالي للعنصر""" quantity = self.quantity_edit.value() unit_price = self.unit_price_edit.value() total_price = quantity * unit_price self.total_price_label.setText(format_currency(total_price)) def get_data(self): description = self.description_edit.text().strip() quantity = self.quantity_edit.value() unit_price = self.unit_price_edit.value() total_price = quantity * unit_price # التحقق من صحة البيانات if not description: show_error_message("خطأ", "يجب إدخال وصف العنصر") return None if quantity <= 0: show_error_message("خطأ", "يجب أن تكون الكمية أكبر من صفر") return None if unit_price <= 0: show_error_message("خطأ", "يجب أن يكون سعر الوحدة أكبر من صفر") return None return { 'description': description, 'quantity': quantity, 'unit_price': unit_price, 'total_price': total_price } class InvoiceDialog(QDialog): """نافذة حوار لإضافة أو تعديل فاتورة""" def __init__(self, parent=None, invoice=None, session=None): super().__init__(parent) self.invoice = invoice self.session = session self.items = [] # إذا كانت هناك فاتورة موجودة، نسخ عناصرها if self.invoice and self.invoice.items: for item in self.invoice.items: self.items.append({ 'id': item.id, 'description': item.description, 'quantity': item.quantity, 'unit_price': item.unit_price, 'total_price': item.total_price }) self.init_ui() self.update_total() def init_ui(self): # إعداد نافذة الحوار if self.invoice: self.setWindowTitle("تعديل فاتورة") else: self.setWindowTitle("إنشاء فاتورة جديدة") self.setMinimumWidth(800) self.setMinimumHeight(600) # إنشاء التخطيط الرئيسي main_layout = QVBoxLayout() # إنشاء نموذج معلومات الفاتورة form_group = StyledGroupBox("معلومات الفاتورة", "primary") form_layout = QFormLayout() # حقل رقم الفاتورة self.invoice_number_edit = QLineEdit() if self.invoice: self.invoice_number_edit.setText(self.invoice.invoice_number) else: # إنشاء رقم فاتورة جديد self.invoice_number_edit.setText(generate_invoice_number(self.session)) form_layout.addRow("رقم الفاتورة:", self.invoice_number_edit) # حقل العميل self.client_combo = QComboBox() self.client_combo.addItem("-- اختر عميل --", None) # إضافة العملاء من قاعدة البيانات if self.session: clients = self.session.query(Client).all() for client in clients: self.client_combo.addItem(client.name, client.id) # تحديد العميل الحالي إذا كان موجودًا if self.invoice and self.invoice.client_id: index = self.client_combo.findData(self.invoice.client_id) if index >= 0: self.client_combo.setCurrentIndex(index) form_layout.addRow("العميل:", self.client_combo) # حقل تاريخ الفاتورة self.date_edit = QDateEdit() self.date_edit.setCalendarPopup(True) self.date_edit.setDate(QDate.currentDate()) if self.invoice and self.invoice.date: self.date_edit.setDate(datetime_to_qdate(self.invoice.date)) form_layout.addRow("تاريخ الفاتورة:", self.date_edit) # حقل تاريخ الدفع القادم self.due_date_edit = QDateEdit() self.due_date_edit.setCalendarPopup(True) due_date = QDate.currentDate().addDays(30) # افتراضي: 30 يوم self.due_date_edit.setDate(due_date) if self.invoice and self.invoice.due_date: self.due_date_edit.setDate(datetime_to_qdate(self.invoice.due_date)) form_layout.addRow("تاريخ الدفع القادم:", self.due_date_edit) # حقل الحالة self.status_combo = QComboBox() statuses = ["pending", "paid", "partially_paid", "cancelled"] status_labels = ["قيد الانتظار", "مدفوعة", "مدفوعة جزئيًا", "ملغاة"] for i, status in enumerate(statuses): self.status_combo.addItem(status_labels[i], status) if self.invoice and self.invoice.status: index = self.status_combo.findData(self.invoice.status) if index >= 0: self.status_combo.setCurrentIndex(index) form_layout.addRow("الحالة:", self.status_combo) # حقل المبلغ المدفوع self.paid_amount_edit = QDoubleSpinBox() self.paid_amount_edit.setRange(0, 1000000) self.paid_amount_edit.setDecimals(0) # بدون كسور عشرية self.paid_amount_edit.setSingleStep(100) if self.invoice: self.paid_amount_edit.setValue(self.invoice.paid_amount) form_layout.addRow("المبلغ المدفوع:", self.paid_amount_edit) form_group.setLayout(form_layout) # إنشاء جدول عناصر الفاتورة items_group = StyledGroupBox("عناصر الفاتورة", "primary") items_layout = QVBoxLayout() styled_table = StyledTable() self.items_table = styled_table.table self.items_table.setColumnCount(5) self.items_table.setHorizontalHeaderLabels(["الوصف", "الكمية", "سعر الوحدة", "السعر الإجمالي", ""]) self.items_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch) self.items_table.setSelectionBehavior(QTableWidget.SelectRows) self.items_table.setSelectionMode(QTableWidget.SingleSelection) self.items_table.setEditTriggers(QTableWidget.NoEditTriggers) # أزرار إدارة العناصر items_buttons_layout = QHBoxLayout() self.add_item_button = StyledButton(" إضافة", "success", "normal") self.add_item_button.clicked.connect(self.add_item) self.edit_item_button = StyledButton(" تعديل", "primary", "normal") self.edit_item_button.clicked.connect(self.edit_item) self.delete_item_button = StyledButton(" حذف", "danger", "normal") self.delete_item_button.clicked.connect(self.delete_item) items_buttons_layout.addWidget(self.add_item_button.button) items_buttons_layout.addWidget(self.edit_item_button.button) items_buttons_layout.addWidget(self.delete_item_button.button) # ملخص الفاتورة summary_layout = QHBoxLayout() self.total_amount_label = QLabel("المبلغ الإجمالي: 0.00") self.total_amount_label.setFont(QFont("Arial", 12, QFont.Bold)) summary_layout.addWidget(self.total_amount_label) # إضافة المكونات إلى تخطيط العناصر items_layout.addWidget(self.items_table) items_layout.addLayout(items_buttons_layout) items_layout.addLayout(summary_layout) items_group.setLayout(items_layout) # حقل الملاحظات notes_group = StyledGroupBox("ملاحظات", "primary") notes_layout = QVBoxLayout() self.notes_edit = QTextEdit() if self.invoice and self.invoice.notes: self.notes_edit.setText(self.invoice.notes) notes_layout.addWidget(self.notes_edit) notes_group.setLayout(notes_layout) # أزرار الحفظ والإلغاء button_layout = QHBoxLayout() self.save_button = StyledButton(" حفظ", "success", "normal") self.save_button.clicked.connect(self.accept) self.cancel_button = StyledButton(" إلغاء", "secondary", "normal") self.cancel_button.clicked.connect(self.reject) button_layout.addWidget(self.save_button.button) button_layout.addWidget(self.cancel_button.button) # تجميع التخطيط النهائي main_layout.addWidget(form_group.group_box) main_layout.addWidget(items_group.group_box) main_layout.addWidget(notes_group.group_box) main_layout.addLayout(button_layout) self.setLayout(main_layout) # تحديث جدول العناصر self.update_items_table() def update_items_table(self): try: # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء self.items_table.setUpdatesEnabled(False) # مسح الجدول self.items_table.setRowCount(0) # إضافة الصفوف for row, item in enumerate(self.items): self.items_table.insertRow(row) # التحقق من وجود المفاتيح المطلوبة description = item.get('description', '') quantity = item.get('quantity', 0) unit_price = item.get('unit_price', 0) total_price = item.get('total_price', 0) # إضافة العناصر إلى الجدول self.items_table.setItem(row, 0, QTableWidgetItem(description)) self.items_table.setItem(row, 1, QTableWidgetItem(format_quantity(quantity))) self.items_table.setItem(row, 2, QTableWidgetItem(format_currency(unit_price))) self.items_table.setItem(row, 3, QTableWidgetItem(format_currency(total_price))) # إضافة زر حذف delete_button = StyledButton(" حذف", "danger", "normal") # استخدام row كمعامل ثابت في lambda function row_index = row # تخزين قيمة row في متغير جديد delete_button.clicked.connect(lambda _, idx=row_index: self.delete_item_at_row(idx)) self.items_table.setCellWidget(row, 4, delete_button.button) # إعادة تمكين تحديث الجدول self.items_table.setUpdatesEnabled(True) except Exception as e: # إعادة تمكين تحديث الجدول في حالة حدوث خطأ self.items_table.setUpdatesEnabled(True) show_error_message("خطأ", f"حدث خطأ أثناء تحديث جدول العناصر: {str(e)}") def update_total(self): """تحديث المبلغ الإجمالي للفاتورة""" try: # حساب المبلغ الإجمالي بطريقة آمنة total = 0 for item in self.items: # التحقق من وجود المفتاح if 'total_price' in item: total += item['total_price'] self.total_amount_label.setText(f"المبلغ الإجمالي: {format_currency(total)}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء حساب المبلغ الإجمالي: {str(e)}") def add_item(self): try: dialog = InvoiceItemDialog(self) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: self.items.append(data) self.update_items_table() self.update_total() except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء إضافة العنصر: {str(e)}") def edit_item(self): """تعديل عنصر في الفاتورة""" try: selected_row = self.items_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة") return # التحقق من أن الصف موجود في القائمة if selected_row >= len(self.items): show_error_message("خطأ", "العنصر غير موجود") return # إنشاء كائن عنصر مؤقت للتعديل temp_item = type('obj', (object,), self.items[selected_row]) dialog = InvoiceItemDialog(self, temp_item) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # الاحتفاظ بمعرف العنصر إذا كان موجودًا if 'id' in self.items[selected_row]: data['id'] = self.items[selected_row]['id'] self.items[selected_row] = data self.update_items_table() self.update_total() except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تعديل العنصر: {str(e)}") def delete_item(self): selected_row = self.items_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة") return self.delete_item_at_row(selected_row) def delete_item_at_row(self, row): """حذف عنصر من الفاتورة بناءً على الصف""" try: # التحقق من أن الصف موجود في القائمة if row < 0 or row >= len(self.items): show_error_message("خطأ", "العنصر غير موجود") return if show_confirmation_message("تأكيد الحذف", "هل أنت متأكد من حذف هذا العنصر؟"): del self.items[row] self.update_items_table() self.update_total() except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء حذف العنصر: {str(e)}") def get_data(self): try: invoice_number = self.invoice_number_edit.text().strip() client_id = self.client_combo.currentData() date = qdate_to_datetime(self.date_edit.date()) due_date = qdate_to_datetime(self.due_date_edit.date()) status = self.status_combo.currentData() paid_amount = self.paid_amount_edit.value() notes = self.notes_edit.toPlainText().strip() # حساب المبلغ الإجمالي بطريقة آمنة total_amount = 0 for item in self.items: if 'total_price' in item: total_amount += item['total_price'] # التحقق من صحة البيانات if not invoice_number: show_error_message("خطأ", "يجب إدخال رقم الفاتورة") return None if not client_id: show_error_message("خطأ", "يجب اختيار عميل") return None if not self.items: show_error_message("خطأ", "يجب إضافة عنصر واحد على الأقل للفاتورة") return None if paid_amount > total_amount: show_error_message("خطأ", "المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ الإجمالي") return None # تحديث حالة الفاتورة بناءً على المبلغ المدفوع if paid_amount >= total_amount: status = 'paid' elif paid_amount > 0: status = 'partially_paid' elif status != 'cancelled': status = 'pending' return { 'invoice_number': invoice_number, 'client_id': client_id, 'date': date, 'due_date': due_date, 'total_amount': total_amount, 'paid_amount': paid_amount, 'status': status, 'notes': notes, 'items': self.items } except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء معالجة بيانات الفاتورة: {str(e)}") return None class InvoicesWidget(QWidget): """واجهة إدارة الفواتير""" def __init__(self, session): super().__init__() self.session = session self.init_ui() self.refresh_data() def init_ui(self): # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع main_layout = QVBoxLayout() main_layout.setContentsMargins(10, 10, 10, 10) main_layout.setSpacing(8) # إضافة العنوان الرئيسي المطور والمحسن مطابق للموردين title_label = QLabel(" إدارة الفواتير المتطورة - نظام شامل ومتقدم لإدارة الفواتير مع أدوات احترافية للبحث والتحليل والتقارير") title_label.setFont(QFont("Segoe UI", 18, QFont.Bold)) # خط أكبر وأوضح title_label.setAlignment(Qt.AlignCenter) # توسيط النص في المنتصف QLabel { color: #ffffff; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e40af, stop:0.2 #3b82f6, stop:0.4 #6366f1, stop:0.6 #8b5cf6, stop:0.8 #a855f7, stop:1 #c084fc); border: 3px solid #000000; border-radius: 10px; padding: 4px 10px; margin: 2px; font-weight: bold; max-height: 40px; min-height: 40px; } """) main_layout.addWidget(title_label) # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين top_frame = QFrame() QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } """) # تخطيط أفقي واحد محسن (الطريقة القديمة) search_layout = QHBoxLayout() search_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق search_layout.setSpacing(4) # مسافات متوازنة # إنشاء حاوي عمودي للتوسيط الحقيقي top_container = QVBoxLayout() top_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط top_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط top_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف top_container.addLayout(search_layout) # إضافة مساحة فارغة أسفل للتوسيط top_container.addStretch(1) # تسمية البحث محسنة مع ألوان موحدة مطابقة للموردين search_label = QLabel(" بحث:") QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b); border: 3px solid #7f1d1d; border-radius: 12px; min-width: 70px; max-width: 70px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c); border: 3px solid #ef4444; } """) search_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية self.search_edit = QLineEdit() self.search_edit.setPlaceholderText(" ابحث برقم الفاتورة، العميل أو الملاحظات...") self.search_edit.textChanged.connect(self.filter_invoices) QLineEdit { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; selection-background-color: #4f46e5; } QLineEdit:focus { border: 3px solid #3730a3; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f0f9ff, stop:1 #e0f2fe); } QLineEdit:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } """) search_button = QPushButton("") QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0891b2, stop:0.5 #0e7490, stop:1 #155e75); color: #ffffff; border: 3px solid #164e63; border-radius: 12px; padding: 8px; font-size: 20px; font-weight: bold; min-width: 50px; max-width: 50px; max-height: 38px; min-height: 34px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #22d3ee, stop:0.5 #0891b2, stop:1 #0e7490); border: 3px solid #22d3ee; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75); border: 3px solid #0e7490; } """) search_button.clicked.connect(self.filter_invoices) search_button.setToolTip("بحث متقدم") search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed) search_button.setContentsMargins(0, 0, 0, 0) # تسمية التصفية مطورة بألوان احترافية filter_label = QLabel(" حالة:") QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309); border: 3px solid #92400e; border-radius: 12px; min-width: 65px; max-width: 65px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706); border: 3px solid #fbbf24; } """) filter_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية # إضافة حقل تصفية حسب الحالة محسن self.status_filter = QComboBox() self.status_filter.addItem("جميع الحالات", None) self.status_filter.addItem("قيد الانتظار", "pending") self.status_filter.addItem("مدفوعة", "paid") self.status_filter.addItem("مدفوعة جزئيًا", "partially_paid") self.status_filter.addItem("ملغاة", "cancelled") self.status_filter.currentIndexChanged.connect(self.filter_invoices) QComboBox { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; min-width: 120px; } QComboBox:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } QComboBox::drop-down { border: none; width: 30px; } QComboBox::down-arrow { image: none; border: 2px solid #4f46e5; width: 8px; height: 8px; border-radius: 4px; background: #4f46e5; } """) # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار search_layout.addWidget(search_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter) # يأخذ مساحة أكبر search_layout.addWidget(search_button, 0, Qt.AlignVCenter) search_layout.addWidget(filter_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter) # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط top_frame.setLayout(top_container) # إنشاء جدول الفواتير المتطور والمحسن self.create_advanced_invoices_table() main_layout.addWidget(top_frame) main_layout.addWidget(self.invoices_table, 1) # إعطاء الجدول أولوية في التمدد # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين bottom_frame = QFrame() QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } """) actions_layout = QHBoxLayout() actions_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق actions_layout.setSpacing(4) # مسافة أكبر بين الأزرار لتوزيع أفضل # إنشاء حاوي عمودي للتوسيط الحقيقي bottom_container = QVBoxLayout() bottom_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط bottom_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط bottom_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف bottom_container.addLayout(actions_layout) # إضافة مساحة فارغة أسفل للتوسيط bottom_container.addStretch(1) # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة self.add_button = QPushButton(" إضافة فاتورة ") self.style_advanced_button(self.add_button, 'emerald', has_menu=True) # أخضر زمردي مميز مع قائمة self.add_button.clicked.connect(self.add_invoice) self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.edit_button = QPushButton(" تعديل") self.style_advanced_button(self.edit_button, 'primary') # أزرق كلاسيكي self.edit_button.clicked.connect(self.edit_invoice) self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.delete_button = QPushButton(" حذف") self.style_advanced_button(self.delete_button, 'danger') # أحمر تحذيري self.delete_button.clicked.connect(self.delete_invoice) self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.refresh_button = QPushButton(" تحديث") self.style_advanced_button(self.refresh_button, 'modern_teal') # تصميم حديث ومتطور self.refresh_button.clicked.connect(self.refresh_data) self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # المجموعة الثانية - العمليات المتقدمة self.view_button = QPushButton(" عرض التفاصيل ") self.style_advanced_button(self.view_button, 'indigo', has_menu=True) # بنفسجي للتفاصيل self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة لعرض التفاصيل view_menu = QMenu(self) view_details_action = QAction(" عرض التفاصيل", self) view_details_action.triggered.connect(self.view_invoice) view_menu.addAction(view_details_action) print_action = QAction(" طباعة الفاتورة", self) print_action.triggered.connect(self.print_invoice) view_menu.addAction(print_action) export_pdf_action = QAction(" تصدير PDF", self) export_pdf_action.triggered.connect(self.export_invoice_pdf) view_menu.addAction(export_pdf_action) duplicate_action = QAction(" نسخ الفاتورة", self) duplicate_action.triggered.connect(self.duplicate_invoice) view_menu.addAction(duplicate_action) self.view_button.setMenu(view_menu) self.add_payment_button = QPushButton(" إضافة دفعة") self.style_advanced_button(self.add_payment_button, 'orange') # برتقالي للدفعات self.add_payment_button.clicked.connect(self.add_payment) self.add_payment_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.export_button = QPushButton(" تصدير ") self.style_advanced_button(self.export_button, 'info', has_menu=True) # لون متسق مع نظام الألوان self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة للتصدير export_menu = QMenu(self) excel_action = QAction(" تصدير إلى Excel", self) excel_action.triggered.connect(self.export_data) export_menu.addAction(excel_action) csv_action = QAction(" تصدير إلى CSV", self) csv_action.triggered.connect(self.export_to_csv) export_menu.addAction(csv_action) json_action = QAction(" تصدير إلى JSON", self) json_action.triggered.connect(self.export_to_json) export_menu.addAction(json_action) pdf_report_action = QAction(" تقرير PDF", self) pdf_report_action.triggered.connect(self.export_report_pdf) export_menu.addAction(pdf_report_action) self.export_button.setMenu(export_menu) self.statistics_button = QPushButton(" الإحصائيات") self.style_advanced_button(self.statistics_button, 'rose') # وردي للإحصائيات self.statistics_button.clicked.connect(self.show_statistics) self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إضافة ملخص الفواتير محسن self.total_label = QLabel("إجمالي الفواتير: 0.00") QLabel { color: #ffffff; font-size: 16px; font-weight: bold; padding: 8px 16px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #059669, stop:0.5 #047857, stop:1 #065f46); border: 3px solid #064e3b; border-radius: 12px; min-height: 34px; max-height: 38px; } """) self.total_label.setAlignment(Qt.AlignCenter) # إضافة الأزرار للتخطيط actions_layout.addWidget(self.add_button) actions_layout.addWidget(self.edit_button) actions_layout.addWidget(self.delete_button) actions_layout.addWidget(self.refresh_button) actions_layout.addWidget(self.view_button) actions_layout.addWidget(self.add_payment_button) actions_layout.addWidget(self.export_button) actions_layout.addWidget(self.statistics_button) actions_layout.addWidget(self.total_label) # تعيين التخطيط للإطار السفلي bottom_frame.setLayout(bottom_container) # تجميع التخطيط النهائي main_layout.addWidget(bottom_frame) self.setLayout(main_layout) def create_advanced_invoices_table(self): styled_table = StyledTable() self.invoices_table = styled_table.table self.invoices_table.setColumnCount(8) self.invoices_table.setHorizontalHeaderLabels(["الرقم", "رقم الفاتورة", "العميل", "التاريخ", "تاريخ الدفع القادم", "المبلغ الإجمالي", "المبلغ المدفوع", "الحالة"]) # تحسين عرض الأعمدة header = self.invoices_table.horizontalHeader() header.setSectionResizeMode(0, QHeaderView.Fixed) # الرقم header.setSectionResizeMode(1, QHeaderView.Fixed) # رقم الفاتورة header.setSectionResizeMode(2, QHeaderView.Stretch) # العميل header.setSectionResizeMode(3, QHeaderView.Fixed) # التاريخ header.setSectionResizeMode(4, QHeaderView.Fixed) # تاريخ الدفع القادم header.setSectionResizeMode(5, QHeaderView.Fixed) # المبلغ الإجمالي header.setSectionResizeMode(6, QHeaderView.Fixed) # المبلغ المدفوع header.setSectionResizeMode(7, QHeaderView.Fixed) # الحالة # تحديد عرض الأعمدة الثابتة self.invoices_table.setColumnWidth(0, 80) # الرقم self.invoices_table.setColumnWidth(1, 120) # رقم الفاتورة self.invoices_table.setColumnWidth(3, 120) # التاريخ self.invoices_table.setColumnWidth(4, 150) # تاريخ الدفع القادم self.invoices_table.setColumnWidth(5, 120) # المبلغ الإجمالي self.invoices_table.setColumnWidth(6, 120) # المبلغ المدفوع self.invoices_table.setColumnWidth(7, 120) # الحالة self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows) self.invoices_table.setSelectionMode(QTableWidget.SingleSelection) self.invoices_table.setEditTriggers(QTableWidget.NoEditTriggers) self.invoices_table.setAlternatingRowColors(True) # تحسين تصميم الجدول self.invoices_table.setStyleSheet(""" QTableWidget { background-color: #ffffff; border: 3px solid #000000; border-radius: 8px; gridline-color: #e5e7eb; font-size: 13px; font-weight: bold; } QTableWidget::item { padding: 8px; border-bottom: 1px solid #e5e7eb; color: #000000; font-weight: bold; } QTableWidget::item:selected { background-color: #3b82f6; color: white; } QTableWidget::item:hover { background-color: #f3f4f6; } QHeaderView::section { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4f46e5, stop:1 #3730a3); color: white; padding: 8px; border: 1px solid #3730a3; font-weight: bold; font-size: 14px; } def refresh_data(self): """تحديث بيانات الفواتير في الجدول""" try: # الحصول على جميع الفواتير من قاعدة البيانات invoices = self.session.query(Invoice).all() self.populate_table(invoices) self.update_summary(invoices) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تحديث البيانات: {str(e)}") def populate_table(self, invoices): try: # تعطيل تحديث الجدول مؤقتًا لتحسين الأداء self.invoices_table.setUpdatesEnabled(False) # مسح الجدول self.invoices_table.setRowCount(0) # إضافة الصفوف for row, invoice in enumerate(invoices): try: self.invoices_table.insertRow(row) self.invoices_table.setItem(row, 0, QTableWidgetItem(str(invoice.id))) self.invoices_table.setItem(row, 1, QTableWidgetItem(invoice.invoice_number)) # التعامل مع العميل بشكل آمن try: client_name = invoice.client.name if invoice.client else "" except Exception: client_name = "" self.invoices_table.setItem(row, 2, QTableWidgetItem(client_name)) # التعامل مع التواريخ بشكل آمن try: date = invoice.date.strftime("%Y-%m-%d") if invoice.date else "" except Exception: date = "" self.invoices_table.setItem(row, 3, QTableWidgetItem(date)) try: due_date = invoice.due_date.strftime("%Y-%m-%d") if invoice.due_date else "" except Exception: due_date = "" self.invoices_table.setItem(row, 4, QTableWidgetItem(due_date)) # التعامل مع المبالغ بشكل آمن try: total_amount = format_currency(invoice.total_amount) except Exception: total_amount = "0.00" self.invoices_table.setItem(row, 5, QTableWidgetItem(total_amount)) try: paid_amount = format_currency(invoice.paid_amount) except Exception: paid_amount = "0.00" self.invoices_table.setItem(row, 6, QTableWidgetItem(paid_amount)) # ترجمة حالة الفاتورة status_map = { 'pending': 'قيد الانتظار', 'paid': 'مدفوعة', 'partially_paid': 'مدفوعة جزئيًا', 'cancelled': 'ملغاة' } status_text = status_map.get(invoice.status, invoice.status or "") self.invoices_table.setItem(row, 7, QTableWidgetItem(status_text)) except Exception as row_error: # تجاهل الصف الذي به خطأ والاستمرار في العملية print(f"خطأ في الصف {row}: {str(row_error)}") continue # إعادة تمكين تحديث الجدول self.invoices_table.setUpdatesEnabled(True) except Exception as e: # إعادة تمكين تحديث الجدول في حالة حدوث خطأ self.invoices_table.setUpdatesEnabled(True) show_error_message("خطأ", f"حدث خطأ أثناء تحديث جدول الفواتير: {str(e)}") def update_summary(self, invoices): """تحديث ملخص الفواتير""" try: # حساب المبالغ بطريقة آمنة total = 0 paid = 0 for invoice in invoices: try: if hasattr(invoice, 'total_amount') and invoice.total_amount is not None: total += invoice.total_amount except Exception: pass try: if hasattr(invoice, 'paid_amount') and invoice.paid_amount is not None: paid += invoice.paid_amount except Exception: pass balance = total - paid # تحديث النصوص self.total_label.setText(f"إجمالي الفواتير: {format_currency(total)} | المدفوعات: {format_currency(paid)} | المستحقات: {format_currency(balance)}") except Exception as e: # في حالة حدوث خطأ، عرض قيم افتراضية self.total_label.setText("إجمالي الفواتير: 0.00 | المدفوعات: 0.00 | المستحقات: 0.00") print(f"خطأ في تحديث ملخص الفواتير: {str(e)}") def filter_invoices(self): try: search_text = self.search_edit.text().strip().lower() status = self.status_filter.currentData() # بناء الاستعلام try: query = self.session.query(Invoice).join(Client, Invoice.client_id == Client.id, isouter=True) # تطبيق تصفية النص if search_text: query = query.filter( Invoice.invoice_number.like(f"%{search_text}%") | Client.name.like(f"%{search_text}%") | Invoice.notes.like(f"%{search_text}%") ) # تطبيق تصفية الحالة if status: query = query.filter(Invoice.status == status) # تنفيذ الاستعلام invoices = query.all() except Exception as query_error: # في حالة فشل الاستعلام، استخدم قائمة فارغة print(f"خطأ في استعلام الفواتير: {str(query_error)}") invoices = [] # تحديث الجدول والملخص self.populate_table(invoices) self.update_summary(invoices) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصفية الفواتير: {str(e)}") def add_invoice(self): """إنشاء فاتورة جديدة""" try: dialog = InvoiceDialog(self, session=self.session) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # استخراج عناصر الفاتورة items_data = data.pop('items') # إنشاء فاتورة جديدة في قاعدة البيانات invoice = Invoice(**data) self.session.add(invoice) self.session.flush() # للحصول على معرف الفاتورة # إضافة عناصر الفاتورة for item_data in items_data: # التأكد من وجود جميع البيانات المطلوبة if 'description' in item_data and 'quantity' in item_data and 'unit_price' in item_data and 'total_price' in item_data: item_data['invoice_id'] = invoice.id item = InvoiceItem(**item_data) self.session.add(item) self.session.commit() show_info_message("تم", "تم إنشاء الفاتورة بنجاح") self.refresh_data() except Exception as e: # التراجع عن التغييرات في حالة حدوث خطأ self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء إنشاء الفاتورة: {str(e)}") def edit_invoice(self): try: selected_row = self.invoices_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار فاتورة من القائمة") return # التحقق من وجود عنصر في الصف المحدد if not self.invoices_table.item(selected_row, 0): show_error_message("خطأ", "الرجاء اختيار فاتورة صالحة من القائمة") return invoice_id = int(self.invoices_table.item(selected_row, 0).text()) invoice = self.session.query(Invoice).get(invoice_id) if not invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return dialog = InvoiceDialog(self, invoice, self.session) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # استخراج عناصر الفاتورة items_data = data.pop('items') # تحديث بيانات الفاتورة for key, value in data.items(): setattr(invoice, key, value) # حذف جميع عناصر الفاتورة الحالية for item in invoice.items: self.session.delete(item) # إضافة عناصر الفاتورة الجديدة for item_data in items_data: # التأكد من وجود جميع البيانات المطلوبة if 'description' in item_data and 'quantity' in item_data and 'unit_price' in item_data and 'total_price' in item_data: item_data['invoice_id'] = invoice.id item = InvoiceItem(**item_data) self.session.add(item) self.session.commit() show_info_message("تم", "تم تحديث بيانات الفاتورة بنجاح") self.refresh_data() except Exception as e: # التراجع عن التغييرات في حالة حدوث خطأ self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء تعديل الفاتورة: {str(e)}") def delete_invoice(self): """حذف فاتورة""" try: selected_row = self.invoices_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار فاتورة من القائمة") return # التحقق من وجود عنصر في الصف المحدد if not self.invoices_table.item(selected_row, 0): show_error_message("خطأ", "الرجاء اختيار فاتورة صالحة من القائمة") return invoice_id = int(self.invoices_table.item(selected_row, 0).text()) invoice = self.session.query(Invoice).get(invoice_id) if not invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return # التحقق من وجود إيرادات مرتبطة بالفاتورة if invoice.revenues: show_error_message( "خطأ", f"لا يمكن حذف الفاتورة لأنها مرتبطة بـ {len(invoice.revenues)} إيراد. قم بحذف الإيرادات أولاً." ) return # طلب تأكيد الحذف if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف الفاتورة {invoice.invoice_number}؟"): # حذف جميع عناصر الفاتورة for item in invoice.items: self.session.delete(item) # حذف الفاتورة self.session.delete(invoice) self.session.commit() show_info_message("تم", "تم حذف الفاتورة بنجاح") self.refresh_data() except Exception as e: # التراجع عن التغييرات في حالة حدوث خطأ self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء حذف الفاتورة: {str(e)}") def view_invoice(self): invoice = None try: selected_row = self.invoices_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار فاتورة من القائمة") return # التحقق من وجود عنصر في الصف المحدد if not self.invoices_table.item(selected_row, 0): show_error_message("خطأ", "الرجاء اختيار فاتورة صالحة من القائمة") return invoice_id = int(self.invoices_table.item(selected_row, 0).text()) invoice = self.session.query(Invoice).get(invoice_id) if not invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء عرض الفاتورة: {str(e)}") return try: # إنشاء نافذة لعرض تفاصيل الفاتورة dialog = QDialog(self) dialog.setWindowTitle(f"تفاصيل الفاتورة - {invoice.invoice_number if invoice else ''}") dialog.setMinimumSize(800, 600) layout = QVBoxLayout() # إنشاء مستعرض نصي لعرض تفاصيل الفاتورة text_browser = QTextBrowser() text_browser.setOpenExternalLinks(False) # إنشاء محتوى HTML للفاتورة try: html_content = self.generate_invoice_html(invoice) text_browser.setHtml(html_content) except Exception as html_error: error_message = f"حدث خطأ أثناء إنشاء محتوى الفاتورة: {str(html_error)}" print(error_message) text_browser.setHtml(f""" <html dir="rtl"> <body> <h1>خطأ في عرض الفاتورة</h1> <p>{error_message}</p> </body> </html> layout.addWidget(text_browser) # أزرار الإجراءات button_layout = QHBoxLayout() button_layout.setSpacing(10) # تعيين نمط موحد للأزرار button_style = """ QPushButton { font-size: 14px; font-weight: bold; padding: 8px 16px; min-height: 35px; border-radius: 5px; border: none; min-width: 120px; } QPushButton:hover { opacity: 0.8; } QPushButton:pressed { opacity: 0.7; } # زر تصدير PDF export_pdf_button = StyledButton(" تصدير", "warning", "normal") export_pdf_button.clicked.connect(lambda: self.export_invoice_to_pdf(invoice, dialog)) export_pdf_button.setStyleSheet(button_style + "background-color: #e74c3c; color: white;") # زر طباعة print_button = QPushButton("طباعة") print_button.clicked.connect(lambda: self.print_invoice_from_dialog(invoice)) print_button.setStyleSheet(button_style + "background-color: #3498db; color: white;") # زر إغلاق close_button = QPushButton("إغلاق") close_button.clicked.connect(dialog.accept) close_button.setStyleSheet(button_style + "background-color: #95a5a6; color: white;") button_layout.addStretch() button_layout.addWidget(export_pdf_button.button) button_layout.addWidget(print_button) button_layout.addWidget(close_button) layout.addLayout(button_layout) dialog.setLayout(layout) dialog.exec_() except Exception as dialog_error: show_error_message("خطأ", f"حدث خطأ أثناء إنشاء نافذة عرض الفاتورة: {str(dialog_error)}") def print_invoice(self): """طباعة الفاتورة""" invoice = None try: selected_row = self.invoices_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار فاتورة من القائمة") return # التحقق من وجود عنصر في الصف المحدد if not self.invoices_table.item(selected_row, 0): show_error_message("خطأ", "الرجاء اختيار فاتورة صالحة من القائمة") return invoice_id = int(self.invoices_table.item(selected_row, 0).text()) invoice = self.session.query(Invoice).get(invoice_id) if not invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تحضير الفاتورة للطباعة: {str(e)}") return try: # إنشاء مستند للطباعة printer = QPrinter(QPrinter.HighResolution) printer.setPageSize(QPrinter.A4) # إظهار مربع حوار الطباعة dialog = QPrintDialog(printer, self) if dialog.exec_() == QDialog.Accepted: try: # إنشاء مستند نصي document = QTextDocument() # إنشاء محتوى HTML للفاتورة try: html_content = self.generate_invoice_html(invoice) document.setHtml(html_content) except Exception as html_error: error_message = f"حدث خطأ أثناء إنشاء محتوى الفاتورة للطباعة: {str(html_error)}" print(error_message) <html dir="rtl"> <body> <h1>خطأ في طباعة الفاتورة</h1> <p>{error_message}</p> </body> </html> """) # طباعة المستند document.print_(printer) except Exception as print_error: show_error_message("خطأ", f"حدث خطأ أثناء طباعة المستند: {str(print_error)}") except Exception as dialog_error: show_error_message("خطأ", f"حدث خطأ أثناء إنشاء مربع حوار الطباعة: {str(dialog_error)}") def generate_invoice_html(self, invoice): try: # ترجمة حالة الفاتورة status_map = { 'pending': 'قيد الانتظار', 'paid': 'مدفوعة', 'partially_paid': 'مدفوعة جزئيًا', 'cancelled': 'ملغاة' } status_text = status_map.get(invoice.status, invoice.status or "") # تنسيق التواريخ بطريقة آمنة try: date_str = invoice.date.strftime("%Y-%m-%d") if invoice.date else "" except Exception: date_str = "" try: due_date_str = invoice.due_date.strftime("%Y-%m-%d") if invoice.due_date else "" except Exception: due_date_str = "" # إنشاء جدول عناصر الفاتورة items_html = """ <table border="1" cellpadding="5" cellspacing="0" width="100%"> <tr style="background-color: #f2f2f2;"> <th>السعر الإجمالي</th> <th>سعر الوحدة</th> <th>الكمية</th> <th>الوصف</th> </tr> # التحقق من وجود عناصر الفاتورة if hasattr(invoice, 'items') and invoice.items: for item in invoice.items: try: description = item.description or "" quantity = format_quantity(item.quantity) if hasattr(item, 'quantity') else "0" unit_price = format_currency(item.unit_price) if hasattr(item, 'unit_price') else "0" total_price = format_currency(item.total_price) if hasattr(item, 'total_price') else "0" items_html += f""" <tr> <td>{total_price}</td> <td>{unit_price}</td> <td>{quantity}</td> <td>{description}</td> </tr> except Exception as item_error: print(f"خطأ في عنصر الفاتورة: {str(item_error)}") continue else: items_html += """ <tr> <td colspan="4" style="text-align: center;">لا توجد عناصر</td> </tr> items_html += "</table>" # حساب الرصيد المتبقي بطريقة آمنة try: total_amount = invoice.total_amount if hasattr(invoice, 'total_amount') and invoice.total_amount is not None else 0 paid_amount = invoice.paid_amount if hasattr(invoice, 'paid_amount') and invoice.paid_amount is not None else 0 balance = total_amount - paid_amount except Exception: total_amount = 0 paid_amount = 0 balance = 0 # الحصول على بيانات العميل بطريقة آمنة client_name = "" client_address = "" client_phone = "" try: if hasattr(invoice, 'client') and invoice.client: client_name = invoice.client.name if hasattr(invoice.client, 'name') else "" client_address = invoice.client.address if hasattr(invoice.client, 'address') else "" client_phone = invoice.client.phone if hasattr(invoice.client, 'phone') else "" except Exception as client_error: print(f"خطأ في بيانات العميل: {str(client_error)}") # إنشاء محتوى HTML كامل للفاتورة html = f""" <html dir="rtl"> <head> <style> body {{ font-family: Arial, sans-serif; }} .invoice-header {{ text-align: center; margin-bottom: 20px; }} .invoice-details {{ margin-bottom: 20px; }} .invoice-details table {{ width: 100%; }} .invoice-items {{ margin-bottom: 20px; }} .invoice-summary {{ text-align: left; margin-top: 20px; }} .invoice-notes {{ margin-top: 30px; border-top: 1px solid #ccc; padding-top: 10px; }} </style> </head> <body> <div class="invoice-header"> <h1>فاتورة</h1> <h2>{invoice.invoice_number if hasattr(invoice, 'invoice_number') else ""}</h2> </div> <div class="invoice-details"> <table width="100%" style="border-collapse: collapse;"> <tr> <td width="33%" style="text-align: right;"><strong>العميل:</strong> {client_name}</td> <td width="34%" style="text-align: center;"><strong>العنوان:</strong> {client_address}</td> <td width="33%" style="text-align: left;"><strong>التاريخ:</strong> {date_str}</td> </tr> <tr> <td style="text-align: right;"><strong>الهاتف:</strong> {client_phone}</td> <td style="text-align: center;"><strong>الحالة:</strong> {status_text}</td> <td style="text-align: left;"><strong>تاريخ الدفع القادم:</strong> {due_date_str}</td> </tr> </table> </div> <div class="invoice-items"> <h3>عناصر الفاتورة</h3> {items_html} </div> <div class="invoice-summary"> <p><strong>المبلغ الإجمالي:</strong> {format_currency(total_amount)}</p> <p><strong>المبلغ المدفوع:</strong> {format_currency(paid_amount)}</p> <p><strong>الرصيد المتبقي:</strong> {format_currency(balance)}</p> </div> <div class="invoice-notes"> <h3>ملاحظات</h3> <p>{invoice.notes or ""}</p> </div> </body> </html> return html except Exception as e: print(f"خطأ في إنشاء محتوى HTML للفاتورة: {str(e)}") # إرجاع محتوى HTML بسيط في حالة حدوث خطأ return f""" <html dir="rtl"> <body> <h1>خطأ في عرض الفاتورة</h1> <p>حدث خطأ أثناء محاولة عرض تفاصيل الفاتورة. الرجاء المحاولة مرة أخرى.</p> <p>رسالة الخطأ: {str(e)}</p> </body> </html> def add_payment(self): """إضافة دفعة للفاتورة المحددة""" try: selected_row = self.invoices_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار فاتورة من القائمة") return # التحقق من وجود عنصر في الصف المحدد if not self.invoices_table.item(selected_row, 0): show_error_message("خطأ", "الرجاء اختيار فاتورة صالحة من القائمة") return invoice_id = int(self.invoices_table.item(selected_row, 0).text()) invoice = self.session.query(Invoice).get(invoice_id) if not invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return # التحقق من حالة الفاتورة if invoice.status == 'cancelled': show_error_message("خطأ", "لا يمكن إضافة دفعة لفاتورة ملغاة") return if invoice.status == 'paid': show_error_message("خطأ", "الفاتورة مدفوعة بالكامل بالفعل") return # حساب الرصيد المتبقي balance = invoice.total_amount - invoice.paid_amount # التحقق من أن الرصيد المتبقي أكبر من صفر if balance <= 0: show_error_message("خطأ", "لا يوجد رصيد متبقي للفاتورة") return except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تحضير إضافة دفعة: {str(e)}") return # إنشاء نافذة حوار لإضافة دفعة dialog = QDialog(self) dialog.setWindowTitle(f"إضافة دفعة للفاتورة {invoice.invoice_number}") dialog.setMinimumWidth(400) layout = QVBoxLayout() form_layout = QFormLayout() # عرض معلومات الفاتورة invoice_label = QLabel(f"{invoice.invoice_number} - {invoice.client.name if invoice.client else ''}") form_layout.addRow("الفاتورة:", invoice_label) total_label = QLabel(format_currency(invoice.total_amount)) form_layout.addRow("المبلغ الإجمالي:", total_label) paid_label = QLabel(format_currency(invoice.paid_amount)) form_layout.addRow("المبلغ المدفوع:", paid_label) balance_label = QLabel(format_currency(balance)) form_layout.addRow("الرصيد المتبقي:", balance_label) # حقل مبلغ الدفعة amount_edit = QDoubleSpinBox() amount_edit.setRange(1, balance) # الحد الأدنى 1 بدلاً من 0.01 amount_edit.setDecimals(0) # بدون كسور عشرية amount_edit.setSingleStep(100) amount_edit.setValue(balance) # افتراضي: المبلغ المتبقي بالكامل form_layout.addRow("مبلغ الدفعة:", amount_edit) # حقل تاريخ الدفع date_edit = QDateEdit() date_edit.setCalendarPopup(True) date_edit.setDate(QDate.currentDate()) form_layout.addRow("تاريخ الدفع:", date_edit) # حقل الملاحظات notes_edit = QTextEdit() notes_edit.setPlaceholderText("ملاحظات حول الدفعة...") form_layout.addRow("ملاحظات:", notes_edit) layout.addLayout(form_layout) # أزرار الحفظ والإلغاء button_layout = QHBoxLayout() save_button = StyledButton(" حفظ", "success", "normal") save_button.clicked.connect(dialog.accept) cancel_button = StyledButton(" إلغاء", "secondary", "normal") cancel_button.clicked.connect(dialog.reject) button_layout.addWidget(save_button.button) button_layout.addWidget(cancel_button.button) layout.addLayout(button_layout) dialog.setLayout(layout) try: if dialog.exec_() == QDialog.Accepted: amount = amount_edit.value() payment_date = qdate_to_datetime(date_edit.date()) notes = notes_edit.toPlainText().strip() if amount <= 0: show_error_message("خطأ", "يجب أن يكون مبلغ الدفعة أكبر من صفر") return if amount > balance: show_error_message("خطأ", "مبلغ الدفعة لا يمكن أن يكون أكبر من الرصيد المتبقي") return # إنشاء إيراد جديد للدفعة revenue = Revenue( title=f"دفعة للفاتورة {invoice.invoice_number}", amount=amount, date=payment_date, category="مبيعات", invoice_id=invoice.id, notes=notes ) # إضافة الإيراد إلى قاعدة البيانات self.session.add(revenue) # تحديث المبلغ المدفوع وحالة الفاتورة invoice.paid_amount += amount if invoice.paid_amount >= invoice.total_amount: invoice.status = 'paid' else: invoice.status = 'partially_paid' # حفظ التغييرات self.session.commit() # تحديث البيانات show_info_message("تم", "تم إضافة الدفعة بنجاح") self.refresh_data() except Exception as e: # التراجع عن التغييرات في حالة حدوث خطأ self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء إضافة الدفعة: {str(e)}") def export_invoice_to_pdf(self, invoice, parent_dialog=None): try: # اختيار مكان حفظ الملف file_path, _ = QFileDialog.getSaveFileName( parent_dialog or self, "حفظ الفاتورة كـ PDF", f"فاتورة_{invoice.invoice_number}.pdf", "PDF Files (*.pdf)" ) if not file_path: return # المستخدم ألغى العملية # إنشاء طابعة PDF printer = QPrinter(QPrinter.HighResolution) printer.setOutputFormat(QPrinter.PdfFormat) printer.setOutputFileName(file_path) printer.setPageSize(QPrinter.A4) printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter) # إنشاء مستند نصي document = QTextDocument() # إنشاء محتوى HTML للفاتورة try: html_content = self.generate_invoice_html(invoice) document.setHtml(html_content) except Exception as html_error: error_message = f"حدث خطأ أثناء إنشاء محتوى الفاتورة: {str(html_error)}" print(error_message) document.setHtml(f""" <html dir="rtl"> <body> <h1>خطأ في تصدير الفاتورة</h1> <p>{error_message}</p> </body> </html> # طباعة المستند إلى PDF document.print_(printer) show_info_message("تم", f"تم تصدير الفاتورة بنجاح إلى:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير الفاتورة إلى PDF: {str(e)}") def print_invoice_from_dialog(self, invoice): """طباعة الفاتورة من نافذة التفاصيل""" try: # إنشاء مستند للطباعة printer = QPrinter(QPrinter.HighResolution) printer.setPageSize(QPrinter.A4) # إظهار مربع حوار الطباعة dialog = QPrintDialog(printer, self) if dialog.exec_() == QDialog.Accepted: try: # إنشاء مستند نصي document = QTextDocument() # إنشاء محتوى HTML للفاتورة try: html_content = self.generate_invoice_html(invoice) document.setHtml(html_content) except Exception as html_error: error_message = f"حدث خطأ أثناء إنشاء محتوى الفاتورة للطباعة: {str(html_error)}" print(error_message) <html dir="rtl"> <body> <h1>خطأ في طباعة الفاتورة</h1> <p>{error_message}</p> </body> </html> """) # طباعة المستند document.print_(printer) except Exception as print_error: show_error_message("خطأ", f"حدث خطأ أثناء طباعة المستند: {str(print_error)}") except Exception as dialog_error: show_error_message("خطأ", f"حدث خطأ أثناء إنشاء مربع حوار الطباعة: {str(dialog_error)}") def export_data(self): show_info_message("قريباً", "ستتم إضافة هذه الميزة قريباً") def export_to_csv(self): """تصدير بيانات الفواتير إلى CSV""" try: # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_الفواتير.csv", "ملفات CSV (*.csv)") if not file_path: return # جمع البيانات من الجدول data = [] headers = [] # الحصول على عناوين الأعمدة for col in range(self.invoices_table.columnCount()): headers.append(self.invoices_table.horizontalHeaderItem(col).text()) # جمع البيانات من الجدول for row in range(self.invoices_table.rowCount()): row_data = [] for col in range(self.invoices_table.columnCount()): item = self.invoices_table.item(row, col) row_data.append(item.text() if item else "") data.append(row_data) # كتابة البيانات إلى ملف CSV with open(file_path, 'w', newline='', encoding='utf-8') as csvfile: writer = csv.writer(csvfile) writer.writerow(headers) writer.writerows(data) show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}") def export_to_json(self): try: # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_الفواتير.json", "ملفات JSON (*.json)") if not file_path: return # جمع البيانات من الجدول data = [] headers = [] # الحصول على عناوين الأعمدة for col in range(self.invoices_table.columnCount()): headers.append(self.invoices_table.horizontalHeaderItem(col).text()) # جمع البيانات من الجدول for row in range(self.invoices_table.rowCount()): row_data = {} for col in range(self.invoices_table.columnCount()): item = self.invoices_table.item(row, col) row_data[headers[col]] = item.text() if item else "" data.append(row_data) # كتابة البيانات إلى ملف JSON with open(file_path, 'w', encoding='utf-8') as jsonfile: json.dump(data, jsonfile, ensure_ascii=False, indent=2) show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}") def export_invoice_pdf(self): """تصدير الفاتورة المحددة إلى PDF""" try: selected_row = self.invoices_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار فاتورة من القائمة") return if not self.invoices_table.item(selected_row, 0): show_error_message("خطأ", "الرجاء اختيار فاتورة صالحة من القائمة") return invoice_id = int(self.invoices_table.item(selected_row, 0).text()) invoice = self.session.query(Invoice).get(invoice_id) if not invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return self.export_invoice_to_pdf(invoice) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير الفاتورة: {str(e)}") def duplicate_invoice(self): try: selected_row = self.invoices_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار فاتورة من القائمة") return if not self.invoices_table.item(selected_row, 0): show_error_message("خطأ", "الرجاء اختيار فاتورة صالحة من القائمة") return invoice_id = int(self.invoices_table.item(selected_row, 0).text()) original_invoice = self.session.query(Invoice).get(invoice_id) if not original_invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return # إنشاء نسخة من الفاتورة dialog = InvoiceDialog(self, session=self.session) # ملء البيانات من الفاتورة الأصلية dialog.client_combo.setCurrentIndex(dialog.client_combo.findData(original_invoice.client_id)) dialog.notes_edit.setText(original_invoice.notes or "") # نسخ العناصر for item in original_invoice.items: dialog.items.append({ 'description': item.description, 'quantity': item.quantity, 'unit_price': item.unit_price, 'total_price': item.total_price }) dialog.update_items_table() dialog.update_total() if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # استخراج عناصر الفاتورة items_data = data.pop('items') # إنشاء فاتورة جديدة في قاعدة البيانات invoice = Invoice(**data) self.session.add(invoice) self.session.flush() # للحصول على معرف الفاتورة # إضافة عناصر الفاتورة for item_data in items_data: if 'description' in item_data and 'quantity' in item_data and 'unit_price' in item_data and 'total_price' in item_data: item_data['invoice_id'] = invoice.id item = InvoiceItem(**item_data) self.session.add(item) self.session.commit() show_info_message("تم", "تم نسخ الفاتورة بنجاح") self.refresh_data() except Exception as e: self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء نسخ الفاتورة: {str(e)}") def export_report_pdf(self): """تصدير تقرير الفواتير إلى PDF""" try: # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName(self, "حفظ تقرير الفواتير", "تقرير_الفواتير.pdf", "ملفات PDF (*.pdf)") if not file_path: return # إنشاء طابعة PDF printer = QPrinter(QPrinter.HighResolution) printer.setOutputFormat(QPrinter.PdfFormat) printer.setOutputFileName(file_path) printer.setPageSize(QPrinter.A4) printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter) # إنشاء مستند نصي document = QTextDocument() # إنشاء محتوى HTML للتقرير html_content = self.generate_invoices_report_html() document.setHtml(html_content) # طباعة المستند إلى PDF document.print_(printer) show_info_message("تم", f"تم تصدير التقرير بنجاح إلى:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}") def generate_invoices_report_html(self): try: # الحصول على جميع الفواتير invoices = self.session.query(Invoice).all() # حساب الإحصائيات total_amount = sum(invoice.total_amount for invoice in invoices) paid_amount = sum(invoice.paid_amount for invoice in invoices) remaining_amount = total_amount - paid_amount html = f""" <html dir="rtl"> <head> <meta charset="utf-8"> <title>تقرير الفواتير</title> <style> body {{ font-family: Arial, sans-serif; margin: 20px; }} h1 {{ color: #2c3e50; text-align: center; }} h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }} table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }} th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }} th {{ background-color: #3498db; color: white; }} .summary {{ background-color: #ecf0f1; padding: 15px; border-radius: 5px; margin: 20px 0; }} .total {{ font-weight: bold; color: #2c3e50; }} .paid {{ color: #27ae60; }} .remaining {{ color: #e74c3c; }} </style> </head> <body> <h1> تقرير الفواتير</h1> <p style="text-align: center;">تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}</p> <div class="summary"> <h2> ملخص الفواتير</h2> <p class="total">إجمالي عدد الفواتير: {len(invoices)}</p> <p class="total">إجمالي المبالغ: {format_currency(total_amount)}</p> <p class="paid">إجمالي المدفوعات: {format_currency(paid_amount)}</p> <p class="remaining">إجمالي المستحقات: {format_currency(remaining_amount)}</p> </div> <h2> تفاصيل الفواتير</h2> <table> <tr> <th>رقم الفاتورة</th> <th>العميل</th> <th>التاريخ</th> <th>المبلغ الإجمالي</th> <th>المبلغ المدفوع</th> <th>المبلغ المتبقي</th> <th>الحالة</th> </tr> # إضافة صفوف الفواتير for invoice in invoices: remaining = invoice.total_amount - invoice.paid_amount status_map = { 'pending': 'قيد الانتظار', 'paid': 'مدفوعة', 'partially_paid': 'مدفوعة جزئياً', 'cancelled': 'ملغاة' } status_text = status_map.get(invoice.status, invoice.status or "") html += f""" <tr> <td>{invoice.invoice_number}</td> <td>{invoice.client.name if invoice.client else ''}</td> <td>{invoice.date.strftime('%Y-%m-%d') if invoice.date else ''}</td> <td>{format_currency(invoice.total_amount)}</td> <td>{format_currency(invoice.paid_amount)}</td> <td>{format_currency(remaining)}</td> <td>{status_text}</td> </tr> html += """ </table> </body> </html> return html except Exception as e: return f""" <html dir="rtl"> <body> <h1>خطأ في إنشاء التقرير</h1> <p>حدث خطأ أثناء إنشاء تقرير الفواتير: {str(e)}</p> </body> </html> def show_statistics(self): """عرض إحصائيات الفواتير""" show_info_message("إحصائيات الفواتير", "سيتم إضافة ميزة الإحصائيات قريباً") def style_advanced_button(self, button, button_type, has_menu=False): try: # تحديد الألوان المتنوعة والمميزة حسب نوع الزر colors = { 'primary': { 'bg_start': '#1e3a8a', 'bg_mid': '#3b82f6', 'bg_end': '#1d4ed8', 'bg_bottom': '#1e40af', 'hover_start': '#3b82f6', 'hover_mid': '#60a5fa', 'hover_end': '#2563eb', 'hover_bottom': '#1d4ed8', 'hover_border': '#2563eb', 'pressed_start': '#1e40af', 'pressed_mid': '#1d4ed8', 'pressed_end': '#1e3a8a', 'pressed_bottom': '#172554', 'pressed_border': '#1e3a8a', 'border': '#1d4ed8', 'text': '#ffffff' }, 'emerald': { 'bg_start': '#064e3b', 'bg_mid': '#10b981', 'bg_end': '#059669', 'bg_bottom': '#022c22', 'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#059669', 'hover_bottom': '#064e3b', 'hover_border': '#059669', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b', 'pressed_end': '#014737', 'pressed_bottom': '#014737', 'pressed_border': '#064e3b', 'border': '#059669', 'text': '#ffffff' }, 'danger': { 'bg_start': '#991b1b', 'bg_mid': '#ef4444', 'bg_end': '#dc2626', 'bg_bottom': '#7f1d1d', 'hover_start': '#ef4444', 'hover_mid': '#f87171', 'hover_end': '#dc2626', 'hover_bottom': '#991b1b', 'hover_border': '#dc2626', 'pressed_start': '#7f1d1d', 'pressed_mid': '#991b1b', 'pressed_end': '#450a0a', 'pressed_bottom': '#450a0a', 'pressed_border': '#991b1b', 'border': '#dc2626', 'text': '#ffffff' }, 'info': { 'bg_start': '#0891b2', 'bg_mid': '#0ea5e9', 'bg_end': '#0284c7', 'bg_bottom': '#075985', 'hover_start': '#0ea5e9', 'hover_mid': '#38bdf8', 'hover_end': '#0891b2', 'hover_bottom': '#0284c7', 'hover_border': '#0891b2', 'pressed_start': '#0284c7', 'pressed_mid': '#0891b2', 'pressed_end': '#075985', 'pressed_bottom': '#0c4a6e', 'pressed_border': '#075985', 'border': '#0891b2', 'text': '#ffffff' }, 'modern_teal': { 'bg_start': '#0d9488', 'bg_mid': '#14b8a6', 'bg_end': '#0f766e', 'bg_bottom': '#134e4a', 'hover_start': '#14b8a6', 'hover_mid': '#2dd4bf', 'hover_end': '#0d9488', 'hover_bottom': '#0f766e', 'hover_border': '#14b8a6', 'pressed_start': '#0f766e', 'pressed_mid': '#0d9488', 'pressed_end': '#134e4a', 'pressed_bottom': '#042f2e', 'pressed_border': '#0f766e', 'border': '#0d9488', 'text': '#ffffff' }, 'cyan': { 'bg_start': '#0891b2', 'bg_mid': '#06b6d4', 'bg_end': '#0e7490', 'bg_bottom': '#164e63', 'hover_start': '#06b6d4', 'hover_mid': '#22d3ee', 'hover_end': '#0891b2', 'hover_bottom': '#0e7490', 'hover_border': '#06b6d4', 'pressed_start': '#0e7490', 'pressed_mid': '#0891b2', 'pressed_end': '#164e63', 'pressed_bottom': '#083344', 'pressed_border': '#0e7490', 'border': '#0891b2', 'text': '#ffffff' }, 'rose': { 'bg_start': '#be185d', 'bg_mid': '#ec4899', 'bg_end': '#be185d', 'bg_bottom': '#9d174d', 'hover_start': '#ec4899', 'hover_mid': '#f472b6', 'hover_end': '#be185d', 'hover_bottom': '#be185d', 'hover_border': '#ec4899', 'pressed_start': '#9d174d', 'pressed_mid': '#be185d', 'pressed_end': '#831843', 'pressed_bottom': '#500724', 'pressed_border': '#9d174d', 'border': '#be185d', 'text': '#ffffff' }, 'indigo': { 'bg_start': '#3730a3', 'bg_mid': '#6366f1', 'bg_end': '#4f46e5', 'bg_bottom': '#312e81', 'hover_start': '#6366f1', 'hover_mid': '#818cf8', 'hover_end': '#4f46e5', 'hover_bottom': '#3730a3', 'hover_border': '#6366f1', 'pressed_start': '#312e81', 'pressed_mid': '#3730a3', 'pressed_end': '#1e1b4b', 'pressed_bottom': '#1e1b4b', 'pressed_border': '#3730a3', 'border': '#4f46e5', 'text': '#ffffff' }, 'orange': { 'bg_start': '#c2410c', 'bg_mid': '#f97316', 'bg_end': '#ea580c', 'bg_bottom': '#9a3412', 'hover_start': '#f97316', 'hover_mid': '#fb923c', 'hover_end': '#ea580c', 'hover_bottom': '#c2410c', 'hover_border': '#f97316', 'pressed_start': '#9a3412', 'pressed_mid': '#c2410c', 'pressed_end': '#7c2d12', 'pressed_bottom': '#431407', 'pressed_border': '#9a3412', 'border': '#ea580c', 'text': '#ffffff' } } # الحصول على ألوان الزر المحدد color_scheme = colors.get(button_type, colors['primary']) # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else "" # تطبيق التصميم المتطور style = f""" QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['bg_start']}, stop:0.3 {color_scheme['bg_mid']}, stop:0.7 {color_scheme['bg_end']}, stop:1 {color_scheme['bg_bottom']}); color: {color_scheme['text']}; border: 2px solid {color_scheme['border']}; border-radius: 8px; padding: 8px 16px; font-weight: bold; font-size: 13px; min-height: 38px; max-height: 38px; min-width: 100px; text-align: center; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['hover_start']}, stop:0.3 {color_scheme['hover_mid']}, stop:0.7 {color_scheme['hover_end']}, stop:1 {color_scheme['hover_bottom']}); border: 2px solid {color_scheme['hover_border']}; }} QPushButton:pressed {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['pressed_start']}, stop:0.3 {color_scheme['pressed_mid']}, stop:0.7 {color_scheme['pressed_end']}, stop:1 {color_scheme['pressed_bottom']}); border: 2px solid {color_scheme['pressed_border']}; }} {menu_indicator} button.setStyleSheet(style) print(f" تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}") except Exception as e: print(f" خطأ في تطبيق التصميم على الزر: {str(e)}")