# -*- coding: utf-8 -*- """ from datetime import datetime import json import os import re import shutil import ast # -*- coding: utf-8 -*- أداة التحسين التلقائي الذكي Smart Auto Optimizer """ class AutoOptimizer: def __init__(self): self.optimizations_applied = [] self.backup_created = False def optimize_project(self, project_path=".", create_backup=True): """تحسين المشروع تلقائياً""" print(" بدء التحسين التلقائي الذكي...") if create_backup and not self.backup_created: self._create_backup(project_path) python_files = self._find_python_files(project_path) for file_path in python_files: if 'venv' in file_path or '__pycache__' in file_path: continue try: self._optimize_file(file_path) except Exception as e: print(f" خطأ في تحسين {file_path}: {e}") self._generate_optimization_report() print(f" تم تطبيق {len(self.optimizations_applied)} تحسين") def _create_backup(self, project_path): backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}" try: shutil.copytree(project_path, f"{backup_name}", ignore=shutil.ignore_patterns('venv', '__pycache__', '*.pyc')) print(f" تم إنشاء نسخة احتياطية: {backup_name}") self.backup_created = True except Exception as e: print(f" تعذر إنشاء نسخة احتياطية: {e}") def _find_python_files(self, path): """العثور على ملفات Python""" python_files = [] for root, dirs, files in os.walk(path): for file in files: if file.endswith('.py'): python_files.append(os.path.join(root, file)) return python_files def _optimize_file(self, file_path): with open(file_path, 'r', encoding='utf-8') as f: original_content = f.read() optimized_content = original_content file_optimizations = [] # تطبيق التحسينات المختلفة optimized_content, opts1 = self._remove_unused_imports(optimized_content, file_path) file_optimizations.extend(opts1) optimized_content, opts2 = self._optimize_string_formatting(optimized_content, file_path) file_optimizations.extend(opts2) optimized_content, opts3 = self._remove_redundant_code(optimized_content, file_path) file_optimizations.extend(opts3) optimized_content, opts4 = self._optimize_imports(optimized_content, file_path) file_optimizations.extend(opts4) optimized_content, opts5 = self._clean_whitespace(optimized_content, file_path) file_optimizations.extend(opts5) # حفظ الملف إذا تم تطبيق تحسينات if file_optimizations: with open(file_path, 'w', encoding='utf-8') as f: f.write(optimized_content) self.optimizations_applied.extend(file_optimizations) print(f" تم تحسين {file_path} ({len(file_optimizations)} تحسين)") def _remove_unused_imports(self, content, file_path): """إزالة الاستيرادات غير المستخدمة""" optimizations = [] lines = content.splitlines() used_imports = set() import_lines = [] # العثور على الاستيرادات for i, line in enumerate(lines): if line.strip().startswith(('import ', 'from ')): import_lines.append((i, line)) # فحص الاستيرادات المستخدمة for i, import_line in import_lines: # استخراج أسماء الوحدات if import_line.strip().startswith('import '): modules = import_line.replace('import ', '').split(',') for module in modules: module_name = module.strip().split(' as ')[0].split('.')[0] if module_name in content: used_imports.add(i) elif import_line.strip().startswith('from '): # معالجة from ... import ... parts = import_line.split(' import ') if len(parts) == 2: imported_items = parts[1].split(',') for item in imported_items: item_name = item.strip().split(' as ')[0] if item_name in content: used_imports.add(i) # إزالة الاستيرادات غير المستخدمة new_lines = [] for i, line in enumerate(lines): if i in [imp[0] for imp in import_lines] and i not in used_imports: optimizations.append({ 'type': 'Removed unused import', 'file': file_path, 'line': i + 1, 'description': f'إزالة استيراد غير مستخدم: {line.strip()}' }) else: new_lines.append(line) return '\n'.join(new_lines), optimizations def _optimize_string_formatting(self, content, file_path): optimizations = [] # تحويل % formatting إلى f-strings (مبسط) old_format_pattern = r'["\']([^"\']*%[sd][^"\']*)["\'] % \([^)]+\)' matches = re.finditer(old_format_pattern, content) for match in matches: optimizations.append({ 'type': 'String formatting optimization', 'file': file_path, 'description': 'يمكن تحسين تنسيق النصوص باستخدام f-strings' }) return content, optimizations def _remove_redundant_code(self, content, file_path): """إزالة الكود المكرر""" optimizations = [] lines = content.splitlines() # إزالة الأسطر الفارغة المتتالية new_lines = [] prev_empty = False for i, line in enumerate(lines): if line.strip() == '': if not prev_empty: new_lines.append(line) prev_empty = True else: optimizations.append({ 'type': 'Removed redundant empty line', 'file': file_path, 'line': i + 1, 'description': 'إزالة سطر فارغ مكرر' }) else: new_lines.append(line) prev_empty = False # إزالة التعليقات المكررة comment_counts = {} for i, line in enumerate(new_lines): stripped = line.strip() if stripped.startswith('#') and len(stripped) > 10: if stripped in comment_counts: comment_counts[stripped].append(i) else: comment_counts[stripped] = [i] for comment, positions in comment_counts.items(): if len(positions) > 1: optimizations.append({ 'type': 'Duplicate comment detected', 'file': file_path, 'description': f'تعليق مكرر في {len(positions)} مواضع' }) return '\n'.join(new_lines), optimizations def _optimize_imports(self, content, file_path): optimizations = [] lines = content.splitlines() import_lines = [] other_lines = [] # فصل الاستيرادات عن باقي الكود for line in lines: if line.strip().startswith(('import ', 'from ')) and not line.strip().startswith('#'): import_lines.append(line) else: other_lines.append(line) # ترتيب الاستيرادات if import_lines: # فصل أنواع الاستيرادات stdlib_imports = [] third_party_imports = [] local_imports = [] for imp in import_lines: if any(lib in imp for lib in ['os', 'sys', 'datetime', 're', 'json']): stdlib_imports.append(imp) elif imp.strip().startswith('from ui') or imp.strip().startswith('from database'): local_imports.append(imp) else: third_party_imports.append(imp) # ترتيب كل مجموعة stdlib_imports.sort() third_party_imports.sort() local_imports.sort() # دمج الاستيرادات المرتبة sorted_imports = [] if stdlib_imports: sorted_imports.extend(stdlib_imports) sorted_imports.append('') if third_party_imports: sorted_imports.extend(third_party_imports) sorted_imports.append('') if local_imports: sorted_imports.extend(local_imports) sorted_imports.append('') if sorted_imports != import_lines: optimizations.append({ 'type': 'Import organization', 'file': file_path, 'description': 'تم ترتيب الاستيرادات حسب المعايير' }) # دمج النتيجة النهائية result_lines = [] # إضافة التعليقات والـ docstrings في البداية for line in other_lines: if line.strip().startswith('#') or '"""' in line or "'''" in line: result_lines.append(line) else: break result_lines.extend(sorted_imports) # إضافة باقي الكود in_docstring = False for line in other_lines: result_lines.append(line) if '"""' in line: in_docstring = not in_docstring return '\n'.join(result_lines), optimizations return content, optimizations def _clean_whitespace(self, content, file_path): optimizations = [] lines = content.splitlines() new_lines = [] for i, line in enumerate(lines): # إزالة المسافات في نهاية السطر cleaned_line = line.rstrip() if cleaned_line != line: optimizations.append({ 'type': 'Removed trailing whitespace', 'file': file_path, 'line': i + 1, 'description': 'إزالة مسافات في نهاية السطر' }) new_lines.append(cleaned_line) return '\n'.join(new_lines), optimizations def _generate_optimization_report(self): """إنتاج تقرير التحسينات""" report = { 'timestamp': datetime.now().isoformat(), 'total_optimizations': len(self.optimizations_applied), 'optimizations_by_type': {}, 'optimizations_by_file': {}, 'details': self.optimizations_applied } # تجميع حسب النوع for opt in self.optimizations_applied: opt_type = opt['type'] if opt_type in report['optimizations_by_type']: report['optimizations_by_type'][opt_type] += 1 else: report['optimizations_by_type'][opt_type] = 1 # تجميع حسب الملف for opt in self.optimizations_applied: file_name = opt['file'] if file_name in report['optimizations_by_file']: report['optimizations_by_file'][file_name] += 1 else: report['optimizations_by_file'][file_name] = 1 with open('optimization_report.json', 'w', encoding='utf-8') as f: json.dump(report, f, ensure_ascii=False, indent=2) print(" تم حفظ تقرير التحسينات في: optimization_report.json") if __name__ == "__main__": optimizer = AutoOptimizer() optimizer.optimize_project() print("\n ملخص التحسين التلقائي:") print(f" إجمالي التحسينات: {len(optimizer.optimizations_applied)}") # عرض ملخص أنواع التحسينات opt_types = {} for opt in optimizer.optimizations_applied: opt_type = opt['type'] opt_types[opt_type] = opt_types.get(opt_type, 0) + 1 for opt_type, count in opt_types.items(): print(f" • {opt_type}: {count}")