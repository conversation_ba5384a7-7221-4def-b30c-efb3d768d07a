import json from PyQt5.Qt<PERSON>ore import Qt, QDate import datetime from PyQt5.QtGui import QTextDocument from PyQt5.QtPrintSupport import QPrinter from PyQt5.QtWidgets import QFileDialog from PyQt5.QtWidgets import QFileDialog from PyQt5.QtWidgets import QFileDialog import csv from PyQt5.QtGui import QIcon, QFont, QColor from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, from utils import (show_error_message, show_info_message, show_confirmation_message, from ui.purchases import PurchasesWidget from ui.sales import SalesWidget from database import (Inventory, Supplier, get_session) from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox, QLabel, QLineEdit, QTableWidget, QTableWidgetItem, QFormLayout, QTextEdit, QHeaderView, QMessageBox, QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox, QSpinBox, QTabWidget, QSplitter, QFrame, QMenu, QAction, QSizePolicy, QFrame) qdate_to_datetime, datetime_to_qdate, format_currency, format_quantity) StyledTable, StyledLabel, StyledTabWidget) class InventoryItemDialog(QDialog): def __init__(self, parent=None, item=None, session=None): super().__init__(parent) self.item = item self.session = session self.init_ui() def init_ui(self): # إعداد نافذة الحوار if self.item: self.setWindowTitle("تعديل عنصر المخزون") else: self.setWindowTitle("إضافة عنصر جديد للمخزون") self.setMinimumWidth(600) self.setMinimumHeight(500) # إنشاء التخطيط الرئيسي main_layout = QVBoxLayout() # إنشاء نموذج معلومات العنصر مع تخطيط محسن form_group = QGroupBox("معلومات العنصر") form_group.setStyleSheet(""" QGroupBox { font-size: 14px; font-weight: bold; color: #2c3e50; border: 2px solid #3498db; border-radius: 8px; margin-top: 10px; padding-top: 10px; } QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 10px 0 10px; background-color: white; } # تخطيط رئيسي للنموذج main_form_layout = QVBoxLayout() # الصف الأول: اسم العنصر والفئة first_row_layout = QHBoxLayout() # اسم العنصر name_layout = QVBoxLayout() name_layout.addWidget(QLabel("اسم العنصر:")) self.name_edit = QLineEdit() self.name_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;") if self.item: self.name_edit.setText(self.item.name) name_layout.addWidget(self.name_edit) first_row_layout.addLayout(name_layout) # الفئة category_layout = QVBoxLayout() category_layout.addWidget(QLabel("الفئة:")) self.category_combo = QComboBox() self.category_combo.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;") categories = ["دهانات", "سيراميك", "أخشاب", "أدوات صحية", "أدوات كهربائية", "مواد بناء", "أخرى"] for category in categories: self.category_combo.addItem(category) if self.item and self.item.category: index = self.category_combo.findText(self.item.category) if index >= 0: self.category_combo.setCurrentIndex(index) category_layout.addWidget(self.category_combo) first_row_layout.addLayout(category_layout) main_form_layout.addLayout(first_row_layout) # الصف الثاني: وحدة القياس والكمية والحد الأدنى second_row_layout = QHBoxLayout() # وحدة القياس unit_layout = QVBoxLayout() unit_layout.addWidget(QLabel("وحدة القياس:")) self.unit_combo = QComboBox() self.unit_combo.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;") units = ["قطعة", "متر", "متر مربع", "كيلوجرام", "لتر", "طن", "علبة", "كرتون", "لوح"] for unit in units: self.unit_combo.addItem(unit) if self.item and self.item.unit: index = self.unit_combo.findText(self.item.unit) if index >= 0: self.unit_combo.setCurrentIndex(index) unit_layout.addWidget(self.unit_combo) second_row_layout.addLayout(unit_layout) # الكمية quantity_layout = QVBoxLayout() quantity_layout.addWidget(QLabel("الكمية:")) self.quantity_edit = QDoubleSpinBox() self.quantity_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;") self.quantity_edit.setRange(0, 100000) self.quantity_edit.setDecimals(0) if self.item: self.quantity_edit.setValue(self.item.quantity or 0) quantity_layout.addWidget(self.quantity_edit) second_row_layout.addLayout(quantity_layout) # الحد الأدنى min_quantity_layout = QVBoxLayout() min_quantity_layout.addWidget(QLabel("الحد الأدنى:")) self.min_quantity_edit = QDoubleSpinBox() self.min_quantity_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;") self.min_quantity_edit.setRange(0, 10000) self.min_quantity_edit.setDecimals(0) if self.item: self.min_quantity_edit.setValue(self.item.min_quantity or 0) min_quantity_layout.addWidget(self.min_quantity_edit) second_row_layout.addLayout(min_quantity_layout) main_form_layout.addLayout(second_row_layout) # الصف الثالث: سعر التكلفة وسعر البيع third_row_layout = QHBoxLayout() # سعر التكلفة cost_layout = QVBoxLayout() cost_layout.addWidget(QLabel("سعر التكلفة:")) self.cost_price_edit = QDoubleSpinBox() self.cost_price_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;") self.cost_price_edit.setRange(0, 1000000) self.cost_price_edit.setDecimals(0) self.cost_price_edit.setSingleStep(10) if self.item: self.cost_price_edit.setValue(self.item.cost_price or 0) cost_layout.addWidget(self.cost_price_edit) third_row_layout.addLayout(cost_layout) # سعر البيع selling_layout = QVBoxLayout() selling_layout.addWidget(QLabel("سعر البيع:")) self.selling_price_edit = QDoubleSpinBox() self.selling_price_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;") self.selling_price_edit.setRange(0, 1000000) self.selling_price_edit.setDecimals(0) self.selling_price_edit.setSingleStep(10) if self.item: self.selling_price_edit.setValue(self.item.selling_price or 0) selling_layout.addWidget(self.selling_price_edit) third_row_layout.addLayout(selling_layout) main_form_layout.addLayout(third_row_layout) # الصف الرابع: المورد وموقع التخزين fourth_row_layout = QHBoxLayout() # المورد supplier_layout = QVBoxLayout() supplier_layout.addWidget(QLabel("المورد:")) self.supplier_combo = QComboBox() self.supplier_combo.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;") self.supplier_combo.addItem("-- اختر مورد --", None) if self.session: suppliers = self.session.query(Supplier).all() for supplier in suppliers: self.supplier_combo.addItem(supplier.name, supplier.id) if self.item and self.item.supplier_id: index = self.supplier_combo.findData(self.item.supplier_id) if index >= 0: self.supplier_combo.setCurrentIndex(index) supplier_layout.addWidget(self.supplier_combo) fourth_row_layout.addLayout(supplier_layout) # موقع التخزين location_layout = QVBoxLayout() location_layout.addWidget(QLabel("موقع التخزين:")) self.location_edit = QLineEdit() self.location_edit.setStyleSheet("padding: 8px; font-size: 13px; border: 2px solid #bdc3c7; border-radius: 4px;") if self.item: self.location_edit.setText(self.item.location or "") location_layout.addWidget(self.location_edit) fourth_row_layout.addLayout(location_layout) main_form_layout.addLayout(fourth_row_layout) form_group.setLayout(main_form_layout) # أزرار الحفظ والإلغاء button_layout = QHBoxLayout() self.save_button = QPushButton("حفظ") self.save_button.clicked.connect(self.accept) self.save_button.setStyleSheet(""" QPushButton { background-color: #10b981; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #059669; } self.cancel_button = QPushButton("إلغاء") self.cancel_button.clicked.connect(self.reject) self.cancel_button.setStyleSheet(""" QPushButton { background-color: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #4b5563; } button_layout.addStretch() button_layout.addWidget(self.save_button) button_layout.addWidget(self.cancel_button) # تجميع التخطيط النهائي main_layout.addWidget(form_group) main_layout.addLayout(button_layout) self.setLayout(main_layout) def get_data(self): """الحصول على بيانات عنصر المخزون من النموذج""" name = self.name_edit.text().strip() category = self.category_combo.currentText() unit = self.unit_combo.currentText() quantity = self.quantity_edit.value() min_quantity = self.min_quantity_edit.value() cost_price = self.cost_price_edit.value() selling_price = self.selling_price_edit.value() supplier_id = self.supplier_combo.currentData() location = self.location_edit.text().strip() # التحقق من صحة البيانات if not name: show_error_message("خطأ", "يجب إدخال اسم العنصر") return None if selling_price < cost_price: if not show_confirmation_message("تحذير", "سعر البيع أقل من سعر التكلفة. هل تريد المتابعة؟"): return None return { 'name': name, 'category': category, 'unit': unit, 'quantity': quantity, 'min_quantity': min_quantity, 'cost_price': cost_price, 'selling_price': selling_price, 'supplier_id': supplier_id, 'location': location, 'notes': '', # إزالة الملاحظات 'last_updated': datetime.datetime.now() } class InventoryMainWidget(QWidget): def __init__(self, session): super().__init__() self.session = session print(" بدء إنشاء واجهة المخازن...") try: self.init_ui() print(" تم إنشاء واجهة المخازن بنجاح") except Exception as e: print(f" خطأ في إنشاء واجهة المخازن: {str(e)}") self.create_emergency_ui() def init_ui(self): # إنشاء التخطيط الرئيسي مطابق للعمال main_layout = QVBoxLayout() main_layout.setContentsMargins(10, 10, 10, 10) main_layout.setSpacing(8) # إنشاء تبويبات للمخزون والمشتريات مع تنسيق مطابق للمشاريع والعمال self.tabs = QTabWidget() self.tabs.setStyleSheet(""" QTabWidget::pane { border: 3px solid #000000; border-radius: 8px; background: #ffffff; margin-top: -1px; } QTabBar::tab { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e2e8f0, stop:0.5 #cbd5e1, stop:1 #94a3b8); color: #1e293b; border: 3px solid #000000; border-bottom: none; border-radius: 8px 8px 0 0; padding: 8px 32px; margin: 2px; font-size: 16px; font-weight: bold; min-width: 400px; max-width: 600px; min-height: 35px; } QTabBar::tab:selected { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4285f4, stop:0.5 #1a73e8, stop:1 #1557b0); color: white; border: 3px solid #000000; border-bottom: none; margin-top: -1px; padding: 9px 32px; font-size: 17px; min-height: 35px; max-height: 40px; } QTabBar::tab:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc); border: 2px solid #000000; } # إنشاء تبويب المخزون مع أيقونة مثل المشاريع print(" إنشاء تبويب المخزون...") try: self.inventory_widget = InventoryWidget(self.session) self.tabs.addTab(self.inventory_widget, " إدارة المخزون") print(" تم إنشاء تبويب المخزون بنجاح") except Exception as e: print(f" خطأ في إنشاء تبويب المخزون: {str(e)}") # إنشاء تبويب بسيط للمخزون simple_inventory = self.create_simple_inventory_widget() self.tabs.addTab(simple_inventory, " إدارة المخزون") # إنشاء تبويب المشتريات مع أيقونة مثل المشاريع print(" إنشاء تبويب المشتريات...") try: self.purchases_widget = PurchasesWidget(self.session) self.tabs.addTab(self.purchases_widget, " إدارة المشتريات") print(" تم تحميل تبويب المشتريات المتطور بنجاح") except Exception as e: print(f" خطأ في تحميل المشتريات المتطورة: {str(e)}") # إضافة تبويب بسيط للمشتريات self.purchases_widget = self.create_simple_purchases_widget() self.tabs.addTab(self.purchases_widget, " إدارة المشتريات") print(" تم إنشاء تبويب المشتريات البسيط بنجاح") # إنشاء تبويب المبيعات مع أيقونة مثل المشاريع print(" إنشاء تبويب المبيعات...") try: self.sales_widget = SalesWidget(self.session) self.tabs.addTab(self.sales_widget, " إدارة المبيعات") print(" تم تحميل تبويب المبيعات المتطور بنجاح") except Exception as e: print(f" خطأ في تحميل المبيعات المتطورة: {str(e)}") # إضافة تبويب بسيط للمبيعات self.sales_widget = self.create_simple_sales_widget() self.tabs.addTab(self.sales_widget, " إدارة المبيعات") print(" تم إنشاء تبويب المبيعات البسيط بنجاح") # إضافة التبويبات إلى التخطيط الرئيسي main_layout.addWidget(self.tabs, 1) # إعطاء التبويبات أولوية في التمدد self.setLayout(main_layout) def create_emergency_ui(self): """إنشاء واجهة طوارئ بسيطة""" print(" إنشاء واجهة طوارئ للمخازن...") layout = QVBoxLayout() # عنوان title = QLabel(" إدارة المخازن") title.setFont(QFont("Arial", 18, QFont.Bold)) title.setAlignment(Qt.AlignCenter) QLabel { background-color: #3b82f6; color: white; padding: 15px; border-radius: 10px; margin: 10px; } """) # رسالة message = QLabel("حدث خطأ في تحميل واجهة المخازن.\nسيتم إصلاح هذه المشكلة قريباً.") message.setAlignment(Qt.AlignCenter) QLabel { font-size: 16px; color: #6b7280; padding: 30px; background-color: #f9fafb; border-radius: 8px; margin: 20px; } """) layout.addWidget(title) layout.addWidget(message) layout.addStretch() self.setLayout(layout) print(" تم إنشاء واجهة طوارئ للمخازن") def create_simple_inventory_widget(self): widget = QWidget() layout = QVBoxLayout() # عنوان title = QLabel(" المخزون") title.setFont(QFont("Arial", 16, QFont.Bold)) title.setAlignment(Qt.AlignCenter) title.setStyleSheet(""" QLabel { background-color: #059669; color: white; padding: 10px; border-radius: 8px; margin: 10px; } # رسالة message = QLabel("سيتم تطوير هذا القسم قريباً...") message.setAlignment(Qt.AlignCenter) message.setStyleSheet(""" QLabel { font-size: 14px; color: #6b7280; padding: 20px; } layout.addWidget(title) layout.addWidget(message) layout.addStretch() widget.setLayout(layout) return widget def create_simple_purchases_widget(self): """إنشاء تبويب مشتريات بسيط""" widget = QWidget() layout = QVBoxLayout() # عنوان title = QLabel(" المشتريات") title.setFont(QFont("Arial", 16, QFont.Bold)) title.setAlignment(Qt.AlignCenter) QLabel { background-color: #3b82f6; color: white; padding: 10px; border-radius: 8px; margin: 10px; } """) # رسالة message = QLabel("سيتم تطوير هذا القسم قريباً...") message.setAlignment(Qt.AlignCenter) QLabel { font-size: 14px; color: #6b7280; padding: 20px; } """) layout.addWidget(title) layout.addWidget(message) layout.addStretch() widget.setLayout(layout) return widget def create_simple_sales_widget(self): widget = QWidget() layout = QVBoxLayout() # عنوان title = QLabel(" المبيعات") title.setFont(QFont("Arial", 16, QFont.Bold)) title.setAlignment(Qt.AlignCenter) title.setStyleSheet(""" QLabel { background-color: #10b981; color: white; padding: 10px; border-radius: 8px; margin: 10px; } # رسالة message = QLabel("سيتم تطوير هذا القسم قريباً...") message.setAlignment(Qt.AlignCenter) message.setStyleSheet(""" QLabel { font-size: 14px; color: #6b7280; padding: 20px; } layout.addWidget(title) layout.addWidget(message) layout.addStretch() widget.setLayout(layout) return widget class InventoryWidget(QWidget): """واجهة إدارة المخزون""" def __init__(self, session): super().__init__() self.session = session self.init_ui() try: self.refresh_data() except Exception as e: print(f" خطأ في تحديث بيانات المخزون: {str(e)}") # إنشاء بيانات تجريبية بسيطة self.create_sample_data() def init_ui(self): # إنشاء التخطيط الرئيسي مطابق للموردين والعمال والمصروفات والإيرادات والمشاريع والفواتير main_layout = QVBoxLayout() main_layout.setContentsMargins(10, 10, 10, 10) main_layout.setSpacing(8) # إضافة العنوان الرئيسي المطور والمحسن مطابق للموردين title_label = QLabel(" إدارة المخزون المتطورة - نظام شامل ومتقدم لإدارة المخزون مع أدوات احترافية للبحث والتحليل والتقارير") title_label.setFont(QFont("Segoe UI", 18, QFont.Bold)) # خط أكبر وأوضح title_label.setAlignment(Qt.AlignCenter) # توسيط النص في المنتصف QLabel { color: #ffffff; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e40af, stop:0.2 #3b82f6, stop:0.4 #6366f1, stop:0.6 #8b5cf6, stop:0.8 #a855f7, stop:1 #c084fc); border: 3px solid #000000; border-radius: 10px; padding: 4px 10px; margin: 2px; font-weight: bold; max-height: 40px; min-height: 40px; } """) main_layout.addWidget(title_label) # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين top_frame = QFrame() QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } """) # تخطيط أفقي واحد محسن (الطريقة القديمة) search_layout = QHBoxLayout() search_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق search_layout.setSpacing(4) # مسافات متوازنة # إنشاء حاوي عمودي للتوسيط الحقيقي top_container = QVBoxLayout() top_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط top_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط top_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف top_container.addLayout(search_layout) # إضافة مساحة فارغة أسفل للتوسيط top_container.addStretch(1) # تسمية البحث محسنة مع ألوان موحدة مطابقة للموردين search_label = QLabel(" بحث:") QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b); border: 3px solid #7f1d1d; border-radius: 12px; min-width: 70px; max-width: 70px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c); border: 3px solid #ef4444; } """) search_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية self.search_edit = QLineEdit() self.search_edit.setPlaceholderText(" ابحث بالاسم، الفئة، المورد أو الموقع...") self.search_edit.textChanged.connect(self.filter_inventory) QLineEdit { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; selection-background-color: #4f46e5; } QLineEdit:focus { border: 3px solid #3730a3; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f0f9ff, stop:1 #e0f2fe); } QLineEdit:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } """) search_button = QPushButton("") QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0891b2, stop:0.5 #0e7490, stop:1 #155e75); color: #ffffff; border: 3px solid #164e63; border-radius: 12px; padding: 8px; font-size: 20px; font-weight: bold; min-width: 50px; max-width: 50px; max-height: 38px; min-height: 34px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #22d3ee, stop:0.5 #0891b2, stop:1 #0e7490); border: 3px solid #22d3ee; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75); border: 3px solid #0e7490; } """) search_button.clicked.connect(self.filter_inventory) search_button.setToolTip("بحث متقدم") search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed) search_button.setContentsMargins(0, 0, 0, 0) # تسمية التصفية مطورة بألوان احترافية filter_label = QLabel(" فئة:") QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309); border: 3px solid #92400e; border-radius: 12px; min-width: 65px; max-width: 65px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706); border: 3px solid #fbbf24; } """) filter_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية # إضافة حقل تصفية حسب الفئة محسن self.category_filter = QComboBox() self.category_filter.addItem("جميع الفئات", None) categories = ["دهانات", "سيراميك", "أخشاب", "أدوات صحية", "أدوات كهربائية", "مواد بناء", "أخرى"] for category in categories: self.category_filter.addItem(category, category) self.category_filter.currentIndexChanged.connect(self.filter_inventory) QComboBox { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; min-width: 120px; } QComboBox:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } QComboBox::drop-down { border: none; width: 30px; } QComboBox::down-arrow { image: none; border: 2px solid #4f46e5; width: 8px; height: 8px; border-radius: 4px; background: #4f46e5; } """) # تسمية المخزون المنخفض stock_label = QLabel(" مخزون:") QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b); border: 3px solid #7f1d1d; border-radius: 12px; min-width: 70px; max-width: 70px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c); border: 3px solid #ef4444; } """) stock_label.setAlignment(Qt.AlignCenter) # إضافة حقل تصفية حسب المخزون المنخفض محسن self.low_stock_filter = QComboBox() self.low_stock_filter.addItem("الكل", None) self.low_stock_filter.addItem("المخزون المنخفض فقط", "low") self.low_stock_filter.currentIndexChanged.connect(self.filter_inventory) QComboBox { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; min-width: 120px; } QComboBox:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } QComboBox::drop-down { border: none; width: 30px; } QComboBox::down-arrow { image: none; border: 2px solid #4f46e5; width: 8px; height: 8px; border-radius: 4px; background: #4f46e5; } """) # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار search_layout.addWidget(search_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter) # يأخذ مساحة أكبر search_layout.addWidget(search_button, 0, Qt.AlignVCenter) search_layout.addWidget(filter_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.category_filter, 1, Qt.AlignVCenter) search_layout.addWidget(stock_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.low_stock_filter, 1, Qt.AlignVCenter) # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط top_frame.setLayout(top_container) # إنشاء جدول المخزون المتطور والمحسن self.create_advanced_inventory_table() main_layout.addWidget(top_frame) main_layout.addWidget(self.inventory_table, 1) # إعطاء الجدول أولوية في التمدد # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين bottom_frame = QFrame() QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } """) actions_layout = QHBoxLayout() actions_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق actions_layout.setSpacing(4) # مسافة أكبر بين الأزرار لتوزيع أفضل # إنشاء حاوي عمودي للتوسيط الحقيقي bottom_container = QVBoxLayout() bottom_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط bottom_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط bottom_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف bottom_container.addLayout(actions_layout) # إضافة مساحة فارغة أسفل للتوسيط bottom_container.addStretch(1) # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة self.add_button = QPushButton(" إضافة عنصر ") self.style_advanced_button(self.add_button, 'emerald', has_menu=True) # أخضر زمردي مميز مع قائمة self.add_button.clicked.connect(self.add_item) self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.edit_button = QPushButton(" تعديل") self.style_advanced_button(self.edit_button, 'primary') # أزرق كلاسيكي self.edit_button.clicked.connect(self.edit_item) self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.delete_button = QPushButton(" حذف") self.style_advanced_button(self.delete_button, 'danger') # أحمر تحذيري self.delete_button.clicked.connect(self.delete_item) self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.refresh_button = QPushButton(" تحديث") self.style_advanced_button(self.refresh_button, 'modern_teal') # تصميم حديث ومتطور self.refresh_button.clicked.connect(self.refresh_data) self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # المجموعة الثانية - العمليات المتقدمة self.view_button = QPushButton(" عرض التفاصيل ") self.style_advanced_button(self.view_button, 'indigo', has_menu=True) # بنفسجي للتفاصيل self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة لعرض التفاصيل view_menu = QMenu(self) view_details_action = QAction(" عرض التفاصيل", self) view_details_action.triggered.connect(self.view_item) view_menu.addAction(view_details_action) stock_history_action = QAction(" تاريخ المخزون", self) stock_history_action.triggered.connect(self.view_stock_history) view_menu.addAction(stock_history_action) supplier_info_action = QAction(" معلومات المورد", self) supplier_info_action.triggered.connect(self.view_supplier_info) view_menu.addAction(supplier_info_action) self.view_button.setMenu(view_menu) self.adjust_button = QPushButton(" تعديل الكمية") self.style_advanced_button(self.adjust_button, 'orange') # برتقالي للكميات self.adjust_button.clicked.connect(self.adjust_quantity) self.adjust_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.export_button = QPushButton(" تصدير ") self.style_advanced_button(self.export_button, 'info', has_menu=True) # لون متسق مع نظام الألوان self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة للتصدير export_menu = QMenu(self) excel_action = QAction(" تصدير إلى Excel", self) excel_action.triggered.connect(self.export_data) export_menu.addAction(excel_action) csv_action = QAction(" تصدير إلى CSV", self) csv_action.triggered.connect(self.export_to_csv) export_menu.addAction(csv_action) json_action = QAction(" تصدير إلى JSON", self) json_action.triggered.connect(self.export_to_json) export_menu.addAction(json_action) low_stock_report_action = QAction(" تقرير المخزون المنخفض", self) low_stock_report_action.triggered.connect(self.export_low_stock_report) export_menu.addAction(low_stock_report_action) self.export_button.setMenu(export_menu) self.statistics_button = QPushButton(" الإحصائيات") self.style_advanced_button(self.statistics_button, 'rose') # وردي للإحصائيات self.statistics_button.clicked.connect(self.show_statistics) self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إضافة ملخص المخزون محسن self.total_label = QLabel("إجمالي العناصر: 0") QLabel { color: #ffffff; font-size: 16px; font-weight: bold; padding: 8px 16px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #059669, stop:0.5 #047857, stop:1 #065f46); border: 3px solid #064e3b; border-radius: 12px; min-height: 34px; max-height: 38px; } """) self.total_label.setAlignment(Qt.AlignCenter) # إضافة الأزرار للتخطيط actions_layout.addWidget(self.add_button) actions_layout.addWidget(self.edit_button) actions_layout.addWidget(self.delete_button) actions_layout.addWidget(self.refresh_button) actions_layout.addWidget(self.view_button) actions_layout.addWidget(self.adjust_button) actions_layout.addWidget(self.export_button) actions_layout.addWidget(self.statistics_button) actions_layout.addWidget(self.total_label) # تعيين التخطيط للإطار السفلي bottom_frame.setLayout(bottom_container) # تجميع التخطيط النهائي main_layout.addWidget(bottom_frame) self.setLayout(main_layout) def create_advanced_inventory_table(self): self.inventory_table = QTableWidget() self.inventory_table.setColumnCount(9) self.inventory_table.setHorizontalHeaderLabels(["الرقم", "اسم العنصر", "الفئة", "الكمية", "الوحدة", "الحد الأدنى", "سعر التكلفة", "سعر البيع", "المورد"]) # تحسين عرض الأعمدة header = self.inventory_table.horizontalHeader() header.setSectionResizeMode(0, QHeaderView.Fixed) # الرقم header.setSectionResizeMode(1, QHeaderView.Stretch) # اسم العنصر header.setSectionResizeMode(2, QHeaderView.Fixed) # الفئة header.setSectionResizeMode(3, QHeaderView.Fixed) # الكمية header.setSectionResizeMode(4, QHeaderView.Fixed) # الوحدة header.setSectionResizeMode(5, QHeaderView.Fixed) # الحد الأدنى header.setSectionResizeMode(6, QHeaderView.Fixed) # سعر التكلفة header.setSectionResizeMode(7, QHeaderView.Fixed) # سعر البيع header.setSectionResizeMode(8, QHeaderView.Stretch) # المورد # تحديد عرض الأعمدة الثابتة self.inventory_table.setColumnWidth(0, 80) # الرقم self.inventory_table.setColumnWidth(2, 100) # الفئة self.inventory_table.setColumnWidth(3, 80) # الكمية self.inventory_table.setColumnWidth(4, 80) # الوحدة self.inventory_table.setColumnWidth(5, 100) # الحد الأدنى self.inventory_table.setColumnWidth(6, 120) # سعر التكلفة self.inventory_table.setColumnWidth(7, 120) # سعر البيع self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows) self.inventory_table.setSelectionMode(QTableWidget.SingleSelection) self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers) self.inventory_table.setAlternatingRowColors(True) # تحسين تصميم الجدول self.inventory_table.setStyleSheet(""" QTableWidget { background-color: #ffffff; border: 3px solid #000000; border-radius: 8px; gridline-color: #e5e7eb; font-size: 13px; font-weight: bold; } QTableWidget::item { padding: 8px; border-bottom: 1px solid #e5e7eb; color: #000000; font-weight: bold; } QTableWidget::item:selected { background-color: #3b82f6; color: white; } QTableWidget::item:hover { background-color: #f3f4f6; } QHeaderView::section { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4f46e5, stop:1 #3730a3); color: white; padding: 8px; border: 1px solid #3730a3; font-weight: bold; font-size: 14px; } def refresh_data(self): """تحديث بيانات المخزون في الجدول""" # الحصول على جميع عناصر المخزون من قاعدة البيانات inventory_items = self.session.query(Inventory).order_by(Inventory.name).all() # إذا لم توجد بيانات، إنشاء بيانات تجريبية if not inventory_items: print("🧪 لا توجد بيانات في المخزون، إنشاء بيانات تجريبية...") self.create_sample_inventory_data() inventory_items = self.session.query(Inventory).order_by(Inventory.name).all() self.populate_table(inventory_items) self.update_summary(inventory_items) def populate_table(self, items): self.inventory_table.setRowCount(0) for row, item in enumerate(items): self.inventory_table.insertRow(row) # الرقم self.inventory_table.setItem(row, 0, QTableWidgetItem(str(item.id))) # اسم العنصر self.inventory_table.setItem(row, 1, QTableWidgetItem(item.name)) # الفئة self.inventory_table.setItem(row, 2, QTableWidgetItem(item.category or "")) # الكمية quantity_item = QTableWidgetItem(format_quantity(item.quantity)) # تلوين خلية الكمية باللون الأحمر إذا كانت أقل من الحد الأدنى if item.quantity <= item.min_quantity: quantity_item.setBackground(QColor(255, 200, 200)) self.inventory_table.setItem(row, 3, quantity_item) # الوحدة self.inventory_table.setItem(row, 4, QTableWidgetItem(item.unit or "")) # الحد الأدنى self.inventory_table.setItem(row, 5, QTableWidgetItem(format_quantity(item.min_quantity))) # سعر التكلفة self.inventory_table.setItem(row, 6, QTableWidgetItem(format_currency(item.cost_price))) # سعر البيع self.inventory_table.setItem(row, 7, QTableWidgetItem(format_currency(item.selling_price))) # المورد supplier_name = item.supplier.name if item.supplier else "" self.inventory_table.setItem(row, 8, QTableWidgetItem(supplier_name)) def update_summary(self, items): """تحديث ملخص المخزون""" total_items = len(items) low_stock_items = sum(1 for item in items if item.quantity <= item.min_quantity) total_value = sum(item.quantity * item.cost_price for item in items) self.total_label.setText(f"إجمالي العناصر: {total_items} | منخفض المخزون: {low_stock_items} | القيمة: {format_currency(total_value)}") def filter_inventory(self): search_text = self.search_edit.text().strip().lower() category = self.category_filter.currentData() low_stock = self.low_stock_filter.currentData() # بناء الاستعلام query = self.session.query(Inventory) # تطبيق تصفية النص if search_text: query = query.filter( Inventory.name.like(f"%{search_text}%") | Inventory.location.like(f"%{search_text}%") ) # تطبيق تصفية الفئة if category: query = query.filter(Inventory.category == category) # تطبيق تصفية المخزون المنخفض if low_stock == "low": query = query.filter(Inventory.quantity <= Inventory.min_quantity) # تنفيذ الاستعلام items = query.order_by(Inventory.name).all() # تحديث الجدول والملخص self.populate_table(items) self.update_summary(items) def add_item(self): """إضافة عنصر جديد للمخزون""" dialog = InventoryItemDialog(self, session=self.session) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # إنشاء عنصر جديد item = Inventory(**data) self.session.add(item) self.session.commit() show_info_message("تم", "تم إضافة العنصر بنجاح") self.refresh_data() def edit_item(self): selected_row = self.inventory_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة") return item_id = int(self.inventory_table.item(selected_row, 0).text()) item = self.session.query(Inventory).get(item_id) if not item: show_error_message("خطأ", "لم يتم العثور على العنصر") return dialog = InventoryItemDialog(self, item, self.session) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # تحديث بيانات العنصر for key, value in data.items(): setattr(item, key, value) self.session.commit() show_info_message("تم", "تم تحديث العنصر بنجاح") self.refresh_data() def delete_item(self): """حذف عنصر من المخزون""" selected_row = self.inventory_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة") return item_id = int(self.inventory_table.item(selected_row, 0).text()) item = self.session.query(Inventory).get(item_id) if not item: show_error_message("خطأ", "لم يتم العثور على العنصر") return # طلب تأكيد الحذف if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف العنصر '{item.name}'؟"): self.session.delete(item) self.session.commit() show_info_message("تم", "تم حذف العنصر بنجاح") self.refresh_data() def view_item(self): selected_row = self.inventory_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة") return item_id = int(self.inventory_table.item(selected_row, 0).text()) item = self.session.query(Inventory).get(item_id) if not item: show_error_message("خطأ", "لم يتم العثور على العنصر") return # إنشاء نافذة لعرض تفاصيل العنصر dialog = QDialog(self) dialog.setWindowTitle(f"تفاصيل العنصر: {item.name}") dialog.setMinimumSize(500, 300) layout = QVBoxLayout() # عنوان العنصر title_label = QLabel(item.name) title_label.setFont(QFont("Arial", 16, QFont.Bold)) layout.addWidget(title_label) # معلومات العنصر info_layout = QFormLayout() info_layout.addRow("الفئة:", QLabel(item.category or "")) unit_text = f"{format_quantity(item.quantity)} {item.unit}" if item.unit else format_quantity(item.quantity) info_layout.addRow("الكمية:", QLabel(unit_text)) info_layout.addRow("الحد الأدنى:", QLabel(format_quantity(item.min_quantity))) info_layout.addRow("سعر التكلفة:", QLabel(format_currency(item.cost_price))) info_layout.addRow("سعر البيع:", QLabel(format_currency(item.selling_price))) supplier_name = item.supplier.name if item.supplier else "" info_layout.addRow("المورد:", QLabel(supplier_name)) info_layout.addRow("موقع التخزين:", QLabel(item.location or "")) last_updated = item.last_updated.strftime("%Y-%m-%d %H:%M") if item.last_updated else "" info_layout.addRow("آخر تحديث:", QLabel(last_updated)) layout.addLayout(info_layout) # زر إغلاق close_button = QPushButton("إغلاق") close_button.clicked.connect(dialog.accept) close_button.setStyleSheet(""" QPushButton { background-color: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #4b5563; } button_layout = QHBoxLayout() button_layout.addStretch() button_layout.addWidget(close_button) layout.addLayout(button_layout) dialog.setLayout(layout) dialog.exec_() def adjust_quantity(self): """تعديل كمية عنصر في المخزون""" selected_row = self.inventory_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة") return item_id = int(self.inventory_table.item(selected_row, 0).text()) item = self.session.query(Inventory).get(item_id) if not item: show_error_message("خطأ", "لم يتم العثور على العنصر") return # إنشاء نافذة لتعديل الكمية dialog = QDialog(self) dialog.setWindowTitle(f"تعديل كمية العنصر: {item.name}") dialog.setMinimumWidth(300) layout = QVBoxLayout() # معلومات العنصر info_label = QLabel(f"العنصر: {item.name}") layout.addWidget(info_label) current_quantity_label = QLabel(f"الكمية الحالية: {item.quantity} {item.unit}") layout.addWidget(current_quantity_label) # نموذج تعديل الكمية form_layout = QFormLayout() # حقل الكمية الجديدة self.new_quantity_edit = QDoubleSpinBox() self.new_quantity_edit.setRange(0, 100000) self.new_quantity_edit.setDecimals(0) # بدون كسور عشرية self.new_quantity_edit.setValue(item.quantity) form_layout.addRow("الكمية الجديدة:", self.new_quantity_edit) layout.addLayout(form_layout) # أزرار الحفظ والإلغاء button_layout = QHBoxLayout() save_button = QPushButton("حفظ") save_button.clicked.connect(lambda: self.save_quantity_adjustment(dialog, item)) QPushButton { background-color: #10b981; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #059669; } """) cancel_button = QPushButton("إلغاء") cancel_button.clicked.connect(dialog.reject) QPushButton { background-color: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #4b5563; } """) button_layout.addStretch() button_layout.addWidget(save_button) button_layout.addWidget(cancel_button) layout.addLayout(button_layout) dialog.setLayout(layout) dialog.exec_() def save_quantity_adjustment(self, dialog, item): new_quantity = self.new_quantity_edit.value() # تحديث كمية العنصر item.quantity = new_quantity item.last_updated = datetime.datetime.now() self.session.commit() show_info_message("تم", f"تم تحديث كمية العنصر '{item.name}' بنجاح") self.refresh_data() dialog.accept() def export_data(self): """تصدير بيانات المخزون إلى Excel""" show_info_message("قريباً", "ستتم إضافة هذه الميزة قريباً") def export_to_csv(self): try: # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_المخزون.csv", "ملفات CSV (*.csv)") if not file_path: return # جمع البيانات من الجدول data = [] headers = [] # الحصول على عناوين الأعمدة for col in range(self.inventory_table.columnCount()): headers.append(self.inventory_table.horizontalHeaderItem(col).text()) # جمع البيانات من الجدول for row in range(self.inventory_table.rowCount()): row_data = [] for col in range(self.inventory_table.columnCount()): item = self.inventory_table.item(row, col) row_data.append(item.text() if item else "") data.append(row_data) # كتابة البيانات إلى ملف CSV with open(file_path, 'w', newline='', encoding='utf-8') as csvfile: writer = csv.writer(csvfile) writer.writerow(headers) writer.writerows(data) show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}") def export_to_json(self): """تصدير بيانات المخزون إلى JSON""" try: # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_المخزون.json", "ملفات JSON (*.json)") if not file_path: return # جمع البيانات من الجدول data = [] headers = [] # الحصول على عناوين الأعمدة for col in range(self.inventory_table.columnCount()): headers.append(self.inventory_table.horizontalHeaderItem(col).text()) # جمع البيانات من الجدول for row in range(self.inventory_table.rowCount()): row_data = {} for col in range(self.inventory_table.columnCount()): item = self.inventory_table.item(row, col) row_data[headers[col]] = item.text() if item else "" data.append(row_data) # كتابة البيانات إلى ملف JSON with open(file_path, 'w', encoding='utf-8') as jsonfile: json.dump(data, jsonfile, ensure_ascii=False, indent=2) show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}") def view_stock_history(self): selected_row = self.inventory_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة") return item_id = int(self.inventory_table.item(selected_row, 0).text()) item = self.session.query(Inventory).get(item_id) if not item: show_error_message("خطأ", "لم يتم العثور على العنصر") return # إنشاء نافذة لعرض تاريخ المخزون dialog = QDialog(self) dialog.setWindowTitle(f"تاريخ المخزون - {item.name}") dialog.setMinimumSize(600, 400) layout = QVBoxLayout() # معلومات العنصر info_text = f""" تاريخ المخزون - {item.name} المعلومات الحالية: • الكمية الحالية: {item.quantity} {item.unit} • الحد الأدنى: {item.min_quantity} {item.unit} • سعر التكلفة: {format_currency(item.cost_price)} • سعر البيع: {format_currency(item.selling_price)} • آخر تحديث: {item.last_updated.strftime('%Y-%m-%d %H:%M') if item.last_updated else 'غير متوفر'} الإحصائيات: • قيمة المخزون: {format_currency(item.quantity * item.cost_price)} • الربح المتوقع: {format_currency(item.quantity * (item.selling_price - item.cost_price))} • حالة المخزون: {'منخفض ' if item.quantity <= item.min_quantity else 'طبيعي '} ملاحظات: • يُنصح بإعادة الطلب عند الوصول للحد الأدنى • تحقق من تواريخ انتهاء الصلاحية إن وجدت info_label = QLabel(info_text) info_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;") layout.addWidget(info_label) # زر إغلاق close_button = QPushButton("إغلاق") close_button.clicked.connect(dialog.accept) close_button.setStyleSheet(""" QPushButton { background-color: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #4b5563; } layout.addWidget(close_button) dialog.setLayout(layout) dialog.exec_() def view_supplier_info(self): """عرض معلومات المورد للعنصر المحدد""" selected_row = self.inventory_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عنصر من القائمة") return item_id = int(self.inventory_table.item(selected_row, 0).text()) item = self.session.query(Inventory).get(item_id) if not item: show_error_message("خطأ", "لم يتم العثور على العنصر") return if not item.supplier: show_info_message("معلومات", f"لا يوجد مورد محدد للعنصر '{item.name}'") return supplier = item.supplier # إنشاء نافذة لعرض معلومات المورد dialog = QDialog(self) dialog.setWindowTitle(f"معلومات المورد - {supplier.name}") dialog.setMinimumSize(500, 350) layout = QVBoxLayout() # معلومات المورد معلومات المورد البيانات الأساسية: • الاسم: {supplier.name} • الهاتف: {supplier.phone or 'غير متوفر'} • البريد الإلكتروني: {supplier.email or 'غير متوفر'} • العنوان: {supplier.address or 'غير متوفر'} المعلومات المالية: • الرصيد الحالي: {format_currency(supplier.balance)} • حالة الرصيد: {'دائن' if supplier.balance > 0 else 'مدين' if supplier.balance < 0 else 'متوازن'} معلومات العنصر: • اسم العنصر: {item.name} • سعر التكلفة: {format_currency(item.cost_price)} • الكمية المتوفرة: {item.quantity} {item.unit} ملاحظات: {supplier.notes or 'لا توجد ملاحظات'} """ supplier_label = QLabel(supplier_text) supplier_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;") layout.addWidget(supplier_label) # زر إغلاق close_button = QPushButton("إغلاق") close_button.clicked.connect(dialog.accept) QPushButton { background-color: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; font-weight: bold; } QPushButton:hover { background-color: #4b5563; } """) layout.addWidget(close_button) dialog.setLayout(layout) dialog.exec_() def export_low_stock_report(self): try: # الحصول على العناصر منخفضة المخزون low_stock_items = self.session.query(Inventory).filter( Inventory.quantity <= Inventory.min_quantity ).all() if not low_stock_items: show_info_message("معلومات", "لا توجد عناصر منخفضة المخزون حالياً") return # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName(self, "حفظ تقرير المخزون المنخفض", "تقرير_المخزون_المنخفض.pdf", "ملفات PDF (*.pdf)") if not file_path: return # إنشاء طابعة PDF printer = QPrinter(QPrinter.HighResolution) printer.setOutputFormat(QPrinter.PdfFormat) printer.setOutputFileName(file_path) printer.setPageSize(QPrinter.A4) printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter) # إنشاء مستند نصي document = QTextDocument() # إنشاء محتوى HTML للتقرير html_content = self.generate_low_stock_report_html(low_stock_items) document.setHtml(html_content) # طباعة المستند إلى PDF document.print_(printer) show_info_message("تم", f"تم تصدير التقرير بنجاح إلى:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}") def generate_low_stock_report_html(self, low_stock_items): """إنشاء محتوى HTML لتقرير المخزون المنخفض""" try: <html dir="rtl"> <head> <meta charset="utf-8"> <title>تقرير المخزون المنخفض</title> <style> body {{ font-family: Arial, sans-serif; margin: 20px; }} h1 {{ color: #e74c3c; text-align: center; }} h2 {{ color: #34495e; border-bottom: 2px solid #e74c3c; padding-bottom: 5px; }} table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }} th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }} th {{ background-color: #e74c3c; color: white; }} .warning {{ background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107; }} .critical {{ background-color: #f8d7da; color: #721c24; }} </style> </head> <body> <h1> تقرير المخزون المنخفض</h1> <p style="text-align: center;">تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}</p> <div class="warning"> <h2> تحذير</h2> <p>يوجد <strong>{len(low_stock_items)}</strong> عنصر منخفض المخزون يحتاج إلى إعادة طلب فوري!</p> </div> <h2> تفاصيل العناصر المنخفضة</h2> <table> <tr> <th>اسم العنصر</th> <th>الفئة</th> <th>الكمية الحالية</th> <th>الحد الأدنى</th> <th>الوحدة</th> <th>المورد</th> <th>الحالة</th> </tr> """ # إضافة صفوف العناصر for item in low_stock_items: status_class = "critical" if item.quantity == 0 else "" status_text = "نفد المخزون" if item.quantity == 0 else "منخفض" <tr class="{status_class}"> <td>{item.name}</td> <td>{item.category or ''}</td> <td>{item.quantity}</td> <td>{item.min_quantity}</td> <td>{item.unit or ''}</td> <td>{item.supplier.name if item.supplier else 'غير محدد'}</td> <td>{status_text}</td> </tr> """ </table> <div class="warning"> <h2> توصيات</h2> <ul> <li>قم بإعادة طلب العناصر المنخفضة فوراً</li> <li>تواصل مع الموردين لتأكيد توفر العناصر</li> <li>راجع الحد الأدنى للمخزون بانتظام</li> <li>فكر في زيادة الحد الأدنى للعناصر سريعة الاستهلاك</li> </ul> </div> </body> </html> """ return html except Exception as e: <html dir="rtl"> <body> <h1>خطأ في إنشاء التقرير</h1> <p>حدث خطأ أثناء إنشاء تقرير المخزون المنخفض: {str(e)}</p> </body> </html> """ def create_sample_inventory_data(self): try: print("🧪 إنشاء بيانات تجريبية للمخزون...") sample_items = [ { 'name': 'دهان أبيض داخلي', 'category': 'دهانات', 'unit': 'علبة', 'quantity': 50, 'min_quantity': 10, 'cost_price': 45, 'selling_price': 60, 'location': 'مخزن A - رف 1' }, { 'name': 'سيراميك أرضي 60x60', 'category': 'سيراميك', 'unit': 'متر مربع', 'quantity': 200, 'min_quantity': 50, 'cost_price': 25, 'selling_price': 35, 'location': 'مخزن B - منطقة 2' }, { 'name': 'خشب صنوبر 2x4', 'category': 'أخشاب', 'unit': 'لوح', 'quantity': 30, 'min_quantity': 5, 'cost_price': 80, 'selling_price': 120, 'location': 'مخزن C - منطقة الأخشاب' }, { 'name': 'مغسلة حمام بيضاء', 'category': 'أدوات صحية', 'unit': 'قطعة', 'quantity': 15, 'min_quantity': 3, 'cost_price': 150, 'selling_price': 220, 'location': 'مخزن D - الأدوات الصحية' }, { 'name': 'كابل كهربائي 2.5 مم', 'category': 'أدوات كهربائية', 'unit': 'متر', 'quantity': 500, 'min_quantity': 100, 'cost_price': 3, 'selling_price': 5, 'location': 'مخزن E - الكهربائيات' }, { 'name': 'أسمنت بورتلاندي', 'category': 'مواد بناء', 'unit': 'كيس', 'quantity': 100, 'min_quantity': 20, 'cost_price': 18, 'selling_price': 25, 'location': 'مخزن F - مواد البناء' } ] for item_data in sample_items: inventory_item = Inventory( name=item_data['name'], category=item_data['category'], unit=item_data['unit'], quantity=item_data['quantity'], min_quantity=item_data['min_quantity'], cost_price=item_data['cost_price'], selling_price=item_data['selling_price'], location=item_data['location'], notes=f"عنصر تجريبي - {item_data['category']}" ) self.session.add(inventory_item) self.session.commit() print(f" تم إنشاء {len(sample_items)} عنصر تجريبي في المخزون") except Exception as e: print(f" خطأ في إنشاء البيانات التجريبية: {str(e)}") self.session.rollback() def show_statistics(self): """عرض إحصائيات المخزون""" show_info_message("إحصائيات المخزون", "سيتم إضافة ميزة الإحصائيات قريباً") def style_advanced_button(self, button, button_type, has_menu=False): try: # تحديد الألوان المتنوعة والمميزة حسب نوع الزر colors = { 'primary': { 'bg_start': '#1e3a8a', 'bg_mid': '#3b82f6', 'bg_end': '#1d4ed8', 'bg_bottom': '#1e40af', 'hover_start': '#3b82f6', 'hover_mid': '#60a5fa', 'hover_end': '#2563eb', 'hover_bottom': '#1d4ed8', 'hover_border': '#2563eb', 'pressed_start': '#1e40af', 'pressed_mid': '#1d4ed8', 'pressed_end': '#1e3a8a', 'pressed_bottom': '#172554', 'pressed_border': '#1e3a8a', 'border': '#1d4ed8', 'text': '#ffffff' }, 'emerald': { 'bg_start': '#064e3b', 'bg_mid': '#10b981', 'bg_end': '#059669', 'bg_bottom': '#022c22', 'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#059669', 'hover_bottom': '#064e3b', 'hover_border': '#059669', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b', 'pressed_end': '#014737', 'pressed_bottom': '#014737', 'pressed_border': '#064e3b', 'border': '#059669', 'text': '#ffffff' }, 'danger': { 'bg_start': '#991b1b', 'bg_mid': '#ef4444', 'bg_end': '#dc2626', 'bg_bottom': '#7f1d1d', 'hover_start': '#ef4444', 'hover_mid': '#f87171', 'hover_end': '#dc2626', 'hover_bottom': '#991b1b', 'hover_border': '#dc2626', 'pressed_start': '#7f1d1d', 'pressed_mid': '#991b1b', 'pressed_end': '#450a0a', 'pressed_bottom': '#450a0a', 'pressed_border': '#991b1b', 'border': '#dc2626', 'text': '#ffffff' }, 'info': { 'bg_start': '#0891b2', 'bg_mid': '#0ea5e9', 'bg_end': '#0284c7', 'bg_bottom': '#075985', 'hover_start': '#0ea5e9', 'hover_mid': '#38bdf8', 'hover_end': '#0891b2', 'hover_bottom': '#0284c7', 'hover_border': '#0891b2', 'pressed_start': '#0284c7', 'pressed_mid': '#0891b2', 'pressed_end': '#075985', 'pressed_bottom': '#0c4a6e', 'pressed_border': '#075985', 'border': '#0891b2', 'text': '#ffffff' }, 'modern_teal': { 'bg_start': '#0d9488', 'bg_mid': '#14b8a6', 'bg_end': '#0f766e', 'bg_bottom': '#134e4a', 'hover_start': '#14b8a6', 'hover_mid': '#2dd4bf', 'hover_end': '#0d9488', 'hover_bottom': '#0f766e', 'hover_border': '#14b8a6', 'pressed_start': '#0f766e', 'pressed_mid': '#0d9488', 'pressed_end': '#134e4a', 'pressed_bottom': '#042f2e', 'pressed_border': '#0f766e', 'border': '#0d9488', 'text': '#ffffff' }, 'cyan': { 'bg_start': '#0891b2', 'bg_mid': '#06b6d4', 'bg_end': '#0e7490', 'bg_bottom': '#164e63', 'hover_start': '#06b6d4', 'hover_mid': '#22d3ee', 'hover_end': '#0891b2', 'hover_bottom': '#0e7490', 'hover_border': '#06b6d4', 'pressed_start': '#0e7490', 'pressed_mid': '#0891b2', 'pressed_end': '#164e63', 'pressed_bottom': '#083344', 'pressed_border': '#0e7490', 'border': '#0891b2', 'text': '#ffffff' }, 'rose': { 'bg_start': '#be185d', 'bg_mid': '#ec4899', 'bg_end': '#be185d', 'bg_bottom': '#9d174d', 'hover_start': '#ec4899', 'hover_mid': '#f472b6', 'hover_end': '#be185d', 'hover_bottom': '#be185d', 'hover_border': '#ec4899', 'pressed_start': '#9d174d', 'pressed_mid': '#be185d', 'pressed_end': '#831843', 'pressed_bottom': '#500724', 'pressed_border': '#9d174d', 'border': '#be185d', 'text': '#ffffff' }, 'indigo': { 'bg_start': '#3730a3', 'bg_mid': '#6366f1', 'bg_end': '#4f46e5', 'bg_bottom': '#312e81', 'hover_start': '#6366f1', 'hover_mid': '#818cf8', 'hover_end': '#4f46e5', 'hover_bottom': '#3730a3', 'hover_border': '#6366f1', 'pressed_start': '#312e81', 'pressed_mid': '#3730a3', 'pressed_end': '#1e1b4b', 'pressed_bottom': '#1e1b4b', 'pressed_border': '#3730a3', 'border': '#4f46e5', 'text': '#ffffff' }, 'orange': { 'bg_start': '#c2410c', 'bg_mid': '#f97316', 'bg_end': '#ea580c', 'bg_bottom': '#9a3412', 'hover_start': '#f97316', 'hover_mid': '#fb923c', 'hover_end': '#ea580c', 'hover_bottom': '#c2410c', 'hover_border': '#f97316', 'pressed_start': '#9a3412', 'pressed_mid': '#c2410c', 'pressed_end': '#7c2d12', 'pressed_bottom': '#431407', 'pressed_border': '#9a3412', 'border': '#ea580c', 'text': '#ffffff' } } # الحصول على ألوان الزر المحدد color_scheme = colors.get(button_type, colors['primary']) # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else "" # تطبيق التصميم المتطور style = f""" QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['bg_start']}, stop:0.3 {color_scheme['bg_mid']}, stop:0.7 {color_scheme['bg_end']}, stop:1 {color_scheme['bg_bottom']}); color: {color_scheme['text']}; border: 2px solid {color_scheme['border']}; border-radius: 8px; padding: 8px 16px; font-weight: bold; font-size: 13px; min-height: 38px; max-height: 38px; min-width: 100px; text-align: center; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['hover_start']}, stop:0.3 {color_scheme['hover_mid']}, stop:0.7 {color_scheme['hover_end']}, stop:1 {color_scheme['hover_bottom']}); border: 2px solid {color_scheme['hover_border']}; }} QPushButton:pressed {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['pressed_start']}, stop:0.3 {color_scheme['pressed_mid']}, stop:0.7 {color_scheme['pressed_end']}, stop:1 {color_scheme['pressed_bottom']}); border: 2px solid {color_scheme['pressed_border']}; }} {menu_indicator} button.setStyleSheet(style) print(f" تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}") except Exception as e: print(f" خطأ في تطبيق التصميم على الزر: {str(e)}") def create_sample_data(self): """إنشاء بيانات تجريبية بسيطة للمخزون""" try: # إضافة صفوف تجريبية للجدول sample_data = [ ["1", "دهان أبيض", "دهانات", "100", "50", "25.00", "30.00", "المخزن الرئيسي", "متوفر"], ["2", "سيراميك أرضي", "سيراميك", "200", "150", "15.00", "20.00", "المخزن الفرعي", "متوفر"], ["3", "خشب صنوبر", "أخشاب", "50", "30", "100.00", "120.00", "المخزن الرئيسي", "منخفض"], ["4", "مفتاح كهربائي", "أدوات كهربائية", "500", "400", "5.00", "8.00", "المخزن الفرعي", "متوفر"], ["5", "حنفية مياه", "أدوات صحية", "75", "60", "45.00", "55.00", "المخزن الرئيسي", "متوفر"] ] self.inventory_table.setRowCount(len(sample_data)) for row, data in enumerate(sample_data): for col, value in enumerate(data): item = QTableWidgetItem(str(value)) item.setFont(QFont("Arial", 10, QFont.Bold)) item.setForeground(QColor("#000000")) # تلوين حسب الحالة if col == 8: # عمود الحالة if value == "منخفض": item.setForeground(QColor("#dc2626")) elif value == "متوفر": item.setForeground(QColor("#059669")) self.inventory_table.setItem(row, col, item) # تحديث الملخص self.total_label.setText("إجمالي العناصر: 5 | منخفض المخزون: 1 | القيمة: 12,500.00 ج.م") print(" تم إنشاء بيانات تجريبية للمخزون") except Exception as e: print(f" خطأ في إنشاء البيانات التجريبية: {str(e)}") # إنشاء جدول فارغ على الأقل self.inventory_table.setRowCount(0) self.total_label.setText("إجمالي العناصر: 0")