from PyQt5.QtCore import Qt, QDateTime import datetime from PyQt5.QtGui import QIcon, QFont, QColor from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, from utils import show_error_message, show_info_message, show_confirmation_message, format_date from database import Reminder from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox, QLabel, QLineEdit, QTableWidget, QTableWidgetItem, QHeaderView, QMessageBox, QDialog, QTextEdit, QComboBox, QDateTimeEdit, QFormLayout, QGroupBox, QCheckBox, QSizePolicy, QFrame) StyledTable, StyledLabel, StyledTabWidget) class ReminderDialog(QDialog): def __init__(self, parent=None, reminder=None, session=None): super().__init__(parent) self.reminder = reminder self.session = session self.init_ui() def init_ui(self): self.setWindowTitle("تنبيه جديد" if not self.reminder else "تعديل التنبيه") self.setMinimumWidth(400) # إنشاء التخطيط الرئيسي layout = QVBoxLayout() # إنشاء نموذج الإدخال form_layout = QFormLayout() # عنوان التنبيه self.title_edit = QLineEdit() if self.reminder: self.title_edit.setText(self.reminder.title) form_layout.addRow("العنوان:", self.title_edit) # وصف التنبيه self.description_edit = QTextEdit() self.description_edit.setMaximumHeight(100) if self.reminder and self.reminder.description: self.description_edit.setText(self.reminder.description) form_layout.addRow("الوصف:", self.description_edit) # تاريخ ووقت التنبيه self.date_time_edit = QDateTimeEdit() self.date_time_edit.setCalendarPopup(True) self.date_time_edit.setDateTime(QDateTime.currentDateTime()) if self.reminder and self.reminder.reminder_date: self.date_time_edit.setDateTime(QDateTime(self.reminder.reminder_date)) form_layout.addRow("تاريخ ووقت التنبيه:", self.date_time_edit) # الأولوية self.priority_combo = QComboBox() self.priority_combo.addItem("عالية", "high") self.priority_combo.addItem("متوسطة", "medium") self.priority_combo.addItem("منخفضة", "low") if self.reminder and self.reminder.priority: index = self.priority_combo.findData(self.reminder.priority) if index >= 0: self.priority_combo.setCurrentIndex(index) form_layout.addRow("الأولوية:", self.priority_combo) # حالة الإكمال self.completed_check = QCheckBox("مكتمل") if self.reminder and self.reminder.is_completed: self.completed_check.setChecked(True) form_layout.addRow("الحالة:", self.completed_check) # إضافة التخطيط إلى التخطيط الرئيسي layout.addLayout(form_layout) # إنشاء أزرار الإجراءات button_layout = QHBoxLayout() self.save_button = StyledButton("حفظ", "success", "normal") self.save_button.clicked.connect(self.save_reminder) self.cancel_button = StyledButton("إلغاء", "secondary", "normal") self.cancel_button.clicked.connect(self.reject) button_layout.addWidget(self.save_button.button) button_layout.addWidget(self.cancel_button.button) layout.addLayout(button_layout) self.setLayout(layout) def save_reminder(self): """حفظ التنبيه""" title = self.title_edit.text().strip() if not title: show_error_message("خطأ", "يجب إدخال عنوان للتنبيه") return description = self.description_edit.toPlainText().strip() reminder_date = self.date_time_edit.dateTime().toPyDateTime() priority = self.priority_combo.currentData() is_completed = self.completed_check.isChecked() try: if not self.reminder: # إنشاء تنبيه جديد self.reminder = Reminder( title=title, description=description, reminder_date=reminder_date, priority=priority, is_completed=is_completed, user_id=None # إزالة المستخدم المسؤول ) self.session.add(self.reminder) else: # تحديث التنبيه الموجود self.reminder.title = title self.reminder.description = description self.reminder.reminder_date = reminder_date self.reminder.priority = priority self.reminder.is_completed = is_completed self.reminder.user_id = None # إزالة المستخدم المسؤول self.session.commit() self.accept() except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء حفظ التنبيه: {str(e)}") class RemindersWidget(QWidget): def __init__(self, session): super().__init__() self.session = session self.init_ui() self.refresh_data() def init_ui(self): # إنشاء التخطيط الرئيسي مع تحسين العرض الكامل main_layout = QVBoxLayout() main_layout.setContentsMargins(5, 5, 5, 5) # هوامش أصغر لاستغلال المساحة main_layout.setSpacing(5) # تعيين سياسة الحجم للويدجت ليأخذ العرض الكامل self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding) # إضافة العنوان الرئيسي المطور والمحسن مطابق للموردين والعملاء والعمال والمصروفات والإيرادات والمشاريع والفواتير title_label = QLabel("⏰ إدارة التنبيهات المتطورة - نظام شامل ومتقدم لإدارة التنبيهات مع أدوات احترافية للبحث والتحليل والتقارير") title_label.setFont(QFont("Segoe UI", 18, QFont.Bold)) # خط أكبر وأوضح title_label.setAlignment(Qt.AlignCenter) # توسيط النص في المنتصف title_label.setStyleSheet(""" QLabel { color: #ffffff; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e40af, stop:0.2 #3b82f6, stop:0.4 #6366f1, stop:0.6 #8b5cf6, stop:0.8 #a855f7, stop:1 #c084fc); border: 3px solid #000000; border-radius: 10px; padding: 4px 10px; margin: 2px; font-weight: bold; max-height: 40px; min-height: 40px; } main_layout.addWidget(title_label) # إنشاء إطار علوي محسن مع إطار أسود top_frame = QFrame() top_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e1); border: 3px solid #000000; border-radius: 8px; margin: 1px; padding: 2px; max-height: 55px; min-height: 50px; } # تعيين سياسة الحجم للإطار العلوي top_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) # تخطيط أفقي محسن مع مساحة أكبر للعناصر filter_layout = QHBoxLayout() filter_layout.setContentsMargins(5, 5, 5, 5) # هوامش صغيرة filter_layout.setSpacing(6) # مسافات مناسبة بين العناصر # إنشاء حاوي عمودي للتوسيط مع تحسين المساحة top_container = QVBoxLayout() top_container.setContentsMargins(3, 3, 3, 3) # هوامش صغيرة top_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط (أقل) top_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف top_container.addLayout(filter_layout) # إضافة مساحة فارغة أسفل للتوسيط (أقل) top_container.addStretch(1) # تسمية البحث محسنة مع حجم أصغر search_label = QLabel(" بحث:") search_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 12px; font-weight: bold; padding: 6px 10px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b); border: 2px solid #7f1d1d; border-radius: 8px; min-width: 60px; max-width: 60px; max-height: 30px; min-height: 28px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c); border: 2px solid #ef4444; } search_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية self.search_edit = QLineEdit() self.search_edit.setPlaceholderText(" ابحث بالعنوان أو الوصف...") self.search_edit.textChanged.connect(self.filter_reminders) self.search_edit.setStyleSheet(""" QLineEdit { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 2px solid #4f46e5; border-radius: 8px; padding: 6px 12px; font-size: 12px; font-weight: bold; color: #1f2937; max-height: 30px; min-height: 28px; selection-background-color: #4f46e5; } QLineEdit:focus { border: 2px solid #3730a3; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f0f9ff, stop:1 #e0f2fe); } QLineEdit:hover { border: 2px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } # تعيين سياسة الحجم لحقل البحث ليأخذ المساحة المتاحة self.search_edit.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) search_button = QPushButton("") search_button.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0891b2, stop:0.5 #0e7490, stop:1 #155e75); color: #ffffff; border: 2px solid #164e63; border-radius: 8px; padding: 6px; font-size: 16px; font-weight: bold; min-width: 40px; max-width: 40px; max-height: 30px; min-height: 28px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #22d3ee, stop:0.5 #0891b2, stop:1 #0e7490); border: 2px solid #22d3ee; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75); border: 2px solid #0e7490; } search_button.clicked.connect(self.filter_reminders) search_button.setToolTip("بحث متقدم") search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed) search_button.setContentsMargins(0, 0, 0, 0) # تسمية الأولوية محسنة مع حجم أصغر priority_label = QLabel(" أولوية:") priority_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 12px; font-weight: bold; padding: 6px 10px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309); border: 2px solid #92400e; border-radius: 8px; min-width: 65px; max-width: 65px; max-height: 30px; min-height: 28px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706); border: 2px solid #fbbf24; } priority_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية # إضافة حقل تصفية حسب الأولوية محسن self.priority_filter = QComboBox() self.priority_filter.addItem("جميع الأولويات", None) self.priority_filter.addItem("عالية", "high") self.priority_filter.addItem("متوسطة", "medium") self.priority_filter.addItem("منخفضة", "low") self.priority_filter.currentIndexChanged.connect(self.filter_reminders) self.priority_filter.setStyleSheet(""" QComboBox { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 2px solid #4f46e5; border-radius: 8px; padding: 6px 12px; font-size: 12px; font-weight: bold; color: #1f2937; max-height: 30px; min-height: 28px; min-width: 100px; } QComboBox:hover { border: 2px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } QComboBox::drop-down { border: none; width: 25px; } QComboBox::down-arrow { image: none; border: 2px solid #4f46e5; width: 6px; height: 6px; border-radius: 3px; background: #4f46e5; } self.priority_filter.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed) # تسمية الحالة محسنة status_label = QLabel(" حالة:") status_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 12px; font-weight: bold; padding: 6px 10px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b); border: 2px solid #7f1d1d; border-radius: 8px; min-width: 55px; max-width: 55px; max-height: 30px; min-height: 28px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c); border: 2px solid #ef4444; } status_label.setAlignment(Qt.AlignCenter) # إضافة حقل تصفية حسب الحالة محسن self.status_filter = QComboBox() self.status_filter.addItem("جميع الحالات", None) self.status_filter.addItem("مكتمل", True) self.status_filter.addItem("غير مكتمل", False) self.status_filter.currentIndexChanged.connect(self.filter_reminders) self.status_filter.setStyleSheet(""" QComboBox { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 2px solid #4f46e5; border-radius: 8px; padding: 6px 12px; font-size: 12px; font-weight: bold; color: #1f2937; max-height: 30px; min-height: 28px; min-width: 100px; } QComboBox:hover { border: 2px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } QComboBox::drop-down { border: none; width: 25px; } QComboBox::down-arrow { image: none; border: 2px solid #4f46e5; width: 6px; height: 6px; border-radius: 3px; background: #4f46e5; } self.status_filter.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed) # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل filter_layout.addWidget(search_label, 0, Qt.AlignVCenter) filter_layout.addWidget(self.search_edit, 3, Qt.AlignVCenter) # يأخذ مساحة أكبر filter_layout.addWidget(search_button, 0, Qt.AlignVCenter) filter_layout.addWidget(priority_label, 0, Qt.AlignVCenter) filter_layout.addWidget(self.priority_filter, 1, Qt.AlignVCenter) filter_layout.addWidget(status_label, 0, Qt.AlignVCenter) filter_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter) # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط top_frame.setLayout(top_container) # إنشاء جدول التنبيهات المتطور والمحسن self.create_advanced_reminders_table() main_layout.addWidget(top_frame) main_layout.addWidget(self.reminders_table, 1) # إعطاء الجدول أولوية في التمدد # إنشاء إطار سفلي للأزرار مع إطار أسود bottom_frame = QFrame() bottom_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e1); border: 3px solid #000000; border-radius: 8px; margin: 1px; padding: 2px; max-height: 60px; min-height: 55px; } actions_layout = QHBoxLayout() actions_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق actions_layout.setSpacing(4) # مسافة أكبر بين الأزرار لتوزيع أفضل # إنشاء حاوي عمودي للتوسيط الحقيقي bottom_container = QVBoxLayout() bottom_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط bottom_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط bottom_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف bottom_container.addLayout(actions_layout) # إضافة مساحة فارغة أسفل للتوسيط bottom_container.addStretch(1) # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة self.add_button = QPushButton(" إضافة تنبيه ") self.style_advanced_button(self.add_button, 'emerald', has_menu=True) # أخضر زمردي مميز مع قائمة self.add_button.clicked.connect(self.add_reminder) self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.edit_button = QPushButton(" تعديل") self.style_advanced_button(self.edit_button, 'primary') # أزرق كلاسيكي self.edit_button.clicked.connect(self.edit_reminder) self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.delete_button = QPushButton(" حذف") self.style_advanced_button(self.delete_button, 'danger') # أحمر تحذيري self.delete_button.clicked.connect(self.delete_reminder) self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.refresh_button = QPushButton(" تحديث") self.style_advanced_button(self.refresh_button, 'modern_teal') # تصميم حديث ومتطور self.refresh_button.clicked.connect(self.refresh_data) self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # المجموعة الثانية - العمليات المتقدمة self.mark_completed_button = QPushButton(" تعليم كمكتمل") self.style_advanced_button(self.mark_completed_button, 'emerald') # أخضر للإكمال self.mark_completed_button.clicked.connect(self.mark_as_completed) self.mark_completed_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.view_button = QPushButton(" عرض التفاصيل ") self.style_advanced_button(self.view_button, 'indigo', has_menu=True) # بنفسجي للتفاصيل self.view_button.clicked.connect(self.view_reminder) self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.export_button = QPushButton(" تصدير ") self.style_advanced_button(self.export_button, 'info', has_menu=True) # لون متسق مع نظام الألوان self.export_button.clicked.connect(self.export_data) self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.statistics_button = QPushButton(" الإحصائيات") self.style_advanced_button(self.statistics_button, 'rose') # وردي للإحصائيات self.statistics_button.clicked.connect(self.show_statistics) self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إضافة ملخص التنبيهات محسن self.total_label = QLabel("إجمالي التنبيهات: 0") self.total_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 16px; font-weight: bold; padding: 8px 16px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #059669, stop:0.5 #047857, stop:1 #065f46); border: 3px solid #064e3b; border-radius: 12px; min-height: 34px; max-height: 38px; } self.total_label.setAlignment(Qt.AlignCenter) # إضافة الأزرار للتخطيط actions_layout.addWidget(self.add_button) actions_layout.addWidget(self.edit_button) actions_layout.addWidget(self.delete_button) actions_layout.addWidget(self.refresh_button) actions_layout.addWidget(self.mark_completed_button) actions_layout.addWidget(self.view_button) actions_layout.addWidget(self.export_button) actions_layout.addWidget(self.statistics_button) actions_layout.addWidget(self.total_label) # تعيين التخطيط للإطار السفلي bottom_frame.setLayout(bottom_container) # تجميع التخطيط النهائي main_layout.addWidget(bottom_frame) self.setLayout(main_layout) def create_advanced_reminders_table(self): """إنشاء جدول التنبيهات المتطور والمحسن مطابق للموردين""" styled_table = StyledTable() self.reminders_table = styled_table.table self.reminders_table.setColumnCount(6) self.reminders_table.setHorizontalHeaderLabels(["الرقم", "العنوان", "تاريخ التنبيه", "تاريخ الإنشاء", "الأولوية", "الحالة"]) # تحسين عرض الأعمدة header = self.reminders_table.horizontalHeader() header.setSectionResizeMode(0, QHeaderView.Fixed) # الرقم header.setSectionResizeMode(1, QHeaderView.Stretch) # العنوان header.setSectionResizeMode(2, QHeaderView.Fixed) # تاريخ التنبيه header.setSectionResizeMode(3, QHeaderView.Fixed) # تاريخ الإنشاء header.setSectionResizeMode(4, QHeaderView.Fixed) # الأولوية header.setSectionResizeMode(5, QHeaderView.Fixed) # الحالة # تحديد عرض الأعمدة الثابتة self.reminders_table.setColumnWidth(0, 80) # الرقم self.reminders_table.setColumnWidth(2, 150) # تاريخ التنبيه self.reminders_table.setColumnWidth(3, 150) # تاريخ الإنشاء self.reminders_table.setColumnWidth(4, 100) # الأولوية self.reminders_table.setColumnWidth(5, 120) # الحالة self.reminders_table.setSelectionBehavior(QTableWidget.SelectRows) self.reminders_table.setSelectionMode(QTableWidget.SingleSelection) self.reminders_table.setEditTriggers(QTableWidget.NoEditTriggers) self.reminders_table.setAlternatingRowColors(True) self.reminders_table.doubleClicked.connect(self.view_reminder) # تحسين تصميم الجدول QTableWidget { background-color: #ffffff; border: 3px solid #000000; border-radius: 8px; gridline-color: #e5e7eb; font-size: 13px; font-weight: bold; } QTableWidget::item { padding: 8px; border-bottom: 1px solid #e5e7eb; color: #000000; font-weight: bold; } QTableWidget::item:selected { background-color: #3b82f6; color: white; } QTableWidget::item:hover { background-color: #f3f4f6; } QHeaderView::section { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4f46e5, stop:1 #3730a3); color: white; padding: 8px; border: 2px solid #000000; font-weight: bold; font-size: 14px; } """) def select_reminder(self, reminder_id): # البحث عن التنبيه في الجدول for row in range(self.reminders_table.rowCount()): item = self.reminders_table.item(row, 0) # عمود الرقم if item and int(item.text()) == reminder_id: # تحديد الصف self.reminders_table.selectRow(row) # التمرير إلى الصف المحدد self.reminders_table.scrollToItem(item) # عرض تفاصيل التنبيه self.view_reminder() return True # إذا لم يتم العثور على التنبيه، قم بتحديث البيانات وحاول مرة أخرى self.refresh_data() # محاولة أخرى بعد تحديث البيانات for row in range(self.reminders_table.rowCount()): item = self.reminders_table.item(row, 0) # عمود الرقم if item and int(item.text()) == reminder_id: # تحديد الصف self.reminders_table.selectRow(row) # التمرير إلى الصف المحدد self.reminders_table.scrollToItem(item) # عرض تفاصيل التنبيه self.view_reminder() return True return False def refresh_data(self): """تحديث بيانات التنبيهات في الجدول""" # الحصول على جميع التنبيهات من قاعدة البيانات reminders = self.session.query(Reminder).order_by(Reminder.reminder_date).all() self.populate_table(reminders) self.update_summary(reminders) def update_summary(self, reminders): total = len(reminders) completed = sum(1 for reminder in reminders if reminder.is_completed) pending = total - completed self.total_label.setText(f"إجمالي التنبيهات: {total} | مكتمل: {completed} | معلق: {pending}") def populate_table(self, reminders): """ملء جدول التنبيهات بالبيانات""" self.reminders_table.setRowCount(0) for row, reminder in enumerate(reminders): self.reminders_table.insertRow(row) self.reminders_table.setItem(row, 0, QTableWidgetItem(str(reminder.id))) self.reminders_table.setItem(row, 1, QTableWidgetItem(reminder.title)) # تاريخ التنبيه date_str = format_date(reminder.reminder_date) if reminder.reminder_date else "" self.reminders_table.setItem(row, 2, QTableWidgetItem(date_str)) # تاريخ الإنشاء created_date_str = format_date(reminder.created_date) if reminder.created_date else "" self.reminders_table.setItem(row, 3, QTableWidgetItem(created_date_str)) # ترجمة الأولوية priority_map = { 'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة' } priority_text = priority_map.get(reminder.priority, reminder.priority or "") priority_item = QTableWidgetItem(priority_text) # تلوين الأولوية if reminder.priority == 'high': priority_item.setForeground(QColor(255, 0, 0)) # أحمر للأولوية العالية elif reminder.priority == 'medium': priority_item.setForeground(QColor(255, 165, 0)) # برتقالي للأولوية المتوسطة elif reminder.priority == 'low': priority_item.setForeground(QColor(0, 128, 0)) # أخضر للأولوية المنخفضة self.reminders_table.setItem(row, 4, priority_item) # حالة التنبيه (مكتمل/غير مكتمل) status_item = QTableWidgetItem("مكتمل" if reminder.is_completed else "غير مكتمل") # تلوين الحالة if reminder.is_completed: status_item.setForeground(QColor(0, 128, 0)) # أخضر للمكتمل else: # تلوين التنبيهات المتأخرة باللون الأحمر if reminder.reminder_date < datetime.datetime.now() and not reminder.is_completed: status_item.setForeground(QColor(255, 0, 0)) # أحمر للمتأخر # تلوين الصف بلون خفيف للإشارة إلى أنه متأخر for col in range(self.reminders_table.columnCount()): item = self.reminders_table.item(row, col) if item: item.setBackground(QColor(255, 235, 235)) self.reminders_table.setItem(row, 5, status_item) def filter_reminders(self): search_text = self.search_edit.text().strip().lower() priority = self.priority_filter.currentData() status = self.status_filter.currentData() # بناء الاستعلام query = self.session.query(Reminder) # إضافة شروط البحث if search_text: query = query.filter(Reminder.title.like(f"%{search_text}%") | Reminder.description.like(f"%{search_text}%")) if priority is not None: query = query.filter(Reminder.priority == priority) if status is not None: query = query.filter(Reminder.is_completed == status) # تنفيذ الاستعلام reminders = query.order_by(Reminder.reminder_date).all() # تحديث الجدول والملخص self.populate_table(reminders) self.update_summary(reminders) def add_reminder(self): """إضافة تنبيه جديد""" dialog = ReminderDialog(self, None, self.session) if dialog.exec_() == QDialog.Accepted: show_info_message("تم", "تمت إضافة التنبيه بنجاح") self.refresh_data() def edit_reminder(self): selected_row = self.reminders_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار تنبيه من القائمة") return reminder_id = int(self.reminders_table.item(selected_row, 0).text()) reminder = self.session.query(Reminder).get(reminder_id) if not reminder: show_error_message("خطأ", "لم يتم العثور على التنبيه") return dialog = ReminderDialog(self, reminder, self.session) if dialog.exec_() == QDialog.Accepted: show_info_message("تم", "تم تحديث التنبيه بنجاح") self.refresh_data() def delete_reminder(self): """حذف تنبيه""" selected_row = self.reminders_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار تنبيه من القائمة") return reminder_id = int(self.reminders_table.item(selected_row, 0).text()) reminder = self.session.query(Reminder).get(reminder_id) if not reminder: show_error_message("خطأ", "لم يتم العثور على التنبيه") return # طلب تأكيد الحذف if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف التنبيه '{reminder.title}'؟"): self.session.delete(reminder) self.session.commit() show_info_message("تم", "تم حذف التنبيه بنجاح") self.refresh_data() def mark_as_completed(self): selected_row = self.reminders_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار تنبيه من القائمة") return reminder_id = int(self.reminders_table.item(selected_row, 0).text()) reminder = self.session.query(Reminder).get(reminder_id) if not reminder: show_error_message("خطأ", "لم يتم العثور على التنبيه") return if reminder.is_completed: show_info_message("معلومات", "التنبيه مكتمل بالفعل") return reminder.is_completed = True self.session.commit() show_info_message("تم", "تم تعليم التنبيه كمكتمل") self.refresh_data() def view_reminder(self): """عرض تفاصيل التنبيه""" selected_row = self.reminders_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار تنبيه من القائمة") return reminder_id = int(self.reminders_table.item(selected_row, 0).text()) reminder = self.session.query(Reminder).get(reminder_id) if not reminder: show_error_message("خطأ", "لم يتم العثور على التنبيه") return # إنشاء نافذة حوار لعرض التفاصيل dialog = QDialog(self) dialog.setWindowTitle(f"تفاصيل التنبيه: {reminder.title}") dialog.setMinimumWidth(400) layout = QVBoxLayout() # عنوان التنبيه title_label = QLabel(f"<h2>{reminder.title}</h2>") layout.addWidget(title_label) # تاريخ التنبيه date_str = format_date(reminder.reminder_date) if reminder.reminder_date else "" date_label = QLabel(f"<b>تاريخ التنبيه:</b> {date_str}") layout.addWidget(date_label) # تاريخ الإنشاء created_date_str = format_date(reminder.created_date) if reminder.created_date else "" created_date_label = QLabel(f"<b>تاريخ الإنشاء:</b> {created_date_str}") layout.addWidget(created_date_label) # الأولوية priority_map = { 'high': 'عالية', 'medium': 'متوسطة', 'low': 'منخفضة' } priority_text = priority_map.get(reminder.priority, reminder.priority or "") priority_label = QLabel(f"<b>الأولوية:</b> {priority_text}") layout.addWidget(priority_label) # الحالة status_text = "مكتمل" if reminder.is_completed else "غير مكتمل" status_label = QLabel(f"<b>الحالة:</b> {status_text}") layout.addWidget(status_label) # الوصف if reminder.description: description_label = QLabel("<b>الوصف:</b>") layout.addWidget(description_label) description_text = QTextEdit() description_text.setReadOnly(True) description_text.setText(reminder.description) description_text.setMaximumHeight(100) layout.addWidget(description_text) # زر إغلاق close_button = StyledButton("إغلاق", "secondary", "normal") close_button.clicked.connect(dialog.accept) button_layout = QHBoxLayout() button_layout.addStretch() button_layout.addWidget(close_button.button) layout.addLayout(button_layout) dialog.setLayout(layout) dialog.exec_() def export_data(self): show_info_message("قريباً", "ستتم إضافة هذه الميزة قريباً") def show_statistics(self): """عرض إحصائيات التنبيهات""" show_info_message("إحصائيات التنبيهات", "سيتم إضافة ميزة الإحصائيات قريباً") def style_advanced_button(self, button, button_type, has_menu=False): try: # تحديد الألوان المتنوعة والمميزة حسب نوع الزر colors = { 'primary': { 'bg_start': '#1e3a8a', 'bg_mid': '#3b82f6', 'bg_end': '#1d4ed8', 'bg_bottom': '#1e40af', 'hover_start': '#3b82f6', 'hover_mid': '#60a5fa', 'hover_end': '#2563eb', 'hover_bottom': '#1d4ed8', 'hover_border': '#2563eb', 'pressed_start': '#1e40af', 'pressed_mid': '#1d4ed8', 'pressed_end': '#1e3a8a', 'pressed_bottom': '#172554', 'pressed_border': '#1e3a8a', 'border': '#1d4ed8', 'text': '#ffffff' }, 'emerald': { 'bg_start': '#064e3b', 'bg_mid': '#10b981', 'bg_end': '#059669', 'bg_bottom': '#022c22', 'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#059669', 'hover_bottom': '#064e3b', 'hover_border': '#059669', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b', 'pressed_end': '#014737', 'pressed_bottom': '#014737', 'pressed_border': '#064e3b', 'border': '#059669', 'text': '#ffffff' }, 'danger': { 'bg_start': '#991b1b', 'bg_mid': '#ef4444', 'bg_end': '#dc2626', 'bg_bottom': '#7f1d1d', 'hover_start': '#ef4444', 'hover_mid': '#f87171', 'hover_end': '#dc2626', 'hover_bottom': '#991b1b', 'hover_border': '#dc2626', 'pressed_start': '#7f1d1d', 'pressed_mid': '#991b1b', 'pressed_end': '#450a0a', 'pressed_bottom': '#450a0a', 'pressed_border': '#991b1b', 'border': '#dc2626', 'text': '#ffffff' }, 'info': { 'bg_start': '#0891b2', 'bg_mid': '#0ea5e9', 'bg_end': '#0284c7', 'bg_bottom': '#075985', 'hover_start': '#0ea5e9', 'hover_mid': '#38bdf8', 'hover_end': '#0891b2', 'hover_bottom': '#0284c7', 'hover_border': '#0891b2', 'pressed_start': '#0284c7', 'pressed_mid': '#0891b2', 'pressed_end': '#075985', 'pressed_bottom': '#0c4a6e', 'pressed_border': '#075985', 'border': '#0891b2', 'text': '#ffffff' }, 'modern_teal': { 'bg_start': '#0d9488', 'bg_mid': '#14b8a6', 'bg_end': '#0f766e', 'bg_bottom': '#134e4a', 'hover_start': '#14b8a6', 'hover_mid': '#2dd4bf', 'hover_end': '#0d9488', 'hover_bottom': '#0f766e', 'hover_border': '#14b8a6', 'pressed_start': '#0f766e', 'pressed_mid': '#0d9488', 'pressed_end': '#134e4a', 'pressed_bottom': '#042f2e', 'pressed_border': '#0f766e', 'border': '#0d9488', 'text': '#ffffff' }, 'cyan': { 'bg_start': '#0891b2', 'bg_mid': '#06b6d4', 'bg_end': '#0e7490', 'bg_bottom': '#164e63', 'hover_start': '#06b6d4', 'hover_mid': '#22d3ee', 'hover_end': '#0891b2', 'hover_bottom': '#0e7490', 'hover_border': '#06b6d4', 'pressed_start': '#0e7490', 'pressed_mid': '#0891b2', 'pressed_end': '#164e63', 'pressed_bottom': '#083344', 'pressed_border': '#0e7490', 'border': '#0891b2', 'text': '#ffffff' }, 'rose': { 'bg_start': '#be185d', 'bg_mid': '#ec4899', 'bg_end': '#be185d', 'bg_bottom': '#9d174d', 'hover_start': '#ec4899', 'hover_mid': '#f472b6', 'hover_end': '#be185d', 'hover_bottom': '#be185d', 'hover_border': '#ec4899', 'pressed_start': '#9d174d', 'pressed_mid': '#be185d', 'pressed_end': '#831843', 'pressed_bottom': '#500724', 'pressed_border': '#9d174d', 'border': '#be185d', 'text': '#ffffff' }, 'indigo': { 'bg_start': '#3730a3', 'bg_mid': '#6366f1', 'bg_end': '#4f46e5', 'bg_bottom': '#312e81', 'hover_start': '#6366f1', 'hover_mid': '#818cf8', 'hover_end': '#4f46e5', 'hover_bottom': '#3730a3', 'hover_border': '#6366f1', 'pressed_start': '#312e81', 'pressed_mid': '#3730a3', 'pressed_end': '#1e1b4b', 'pressed_bottom': '#1e1b4b', 'pressed_border': '#3730a3', 'border': '#4f46e5', 'text': '#ffffff' }, 'orange': { 'bg_start': '#c2410c', 'bg_mid': '#f97316', 'bg_end': '#ea580c', 'bg_bottom': '#9a3412', 'hover_start': '#f97316', 'hover_mid': '#fb923c', 'hover_end': '#ea580c', 'hover_bottom': '#c2410c', 'hover_border': '#f97316', 'pressed_start': '#9a3412', 'pressed_mid': '#c2410c', 'pressed_end': '#7c2d12', 'pressed_bottom': '#431407', 'pressed_border': '#9a3412', 'border': '#ea580c', 'text': '#ffffff' } } # الحصول على ألوان الزر المحدد color_scheme = colors.get(button_type, colors['primary']) # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else "" # تطبيق التصميم المتطور style = f""" QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['bg_start']}, stop:0.3 {color_scheme['bg_mid']}, stop:0.7 {color_scheme['bg_end']}, stop:1 {color_scheme['bg_bottom']}); color: {color_scheme['text']}; border: 2px solid {color_scheme['border']}; border-radius: 8px; padding: 8px 16px; font-weight: bold; font-size: 13px; min-height: 38px; max-height: 38px; min-width: 100px; text-align: center; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['hover_start']}, stop:0.3 {color_scheme['hover_mid']}, stop:0.7 {color_scheme['hover_end']}, stop:1 {color_scheme['hover_bottom']}); border: 2px solid {color_scheme['hover_border']}; }} QPushButton:pressed {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['pressed_start']}, stop:0.3 {color_scheme['pressed_mid']}, stop:0.7 {color_scheme['pressed_end']}, stop:1 {color_scheme['pressed_bottom']}); border: 2px solid {color_scheme['pressed_border']}; }} {menu_indicator} button.setStyleSheet(style) print(f" تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}") except Exception as e: print(f" خطأ في تطبيق التصميم على الزر: {str(e)}")