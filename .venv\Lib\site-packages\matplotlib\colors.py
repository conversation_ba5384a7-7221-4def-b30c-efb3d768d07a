""" A module for converting numbers or color arguments to *RGB* or *RGBA*. *RGB* and *RGBA* are sequences of, respectively, 3 or 4 floats in the range 0-1. This module includes functions and classes for color specification conversions, and for mapping numbers to colors in a 1-D array of colors called a colormap. Mapping data onto colors using a colormap typically involves two steps: a data array is first mapped onto the range 0-1 using a subclass of `Normalize`, then this number is mapped to a color using a subclass of `Colormap`. Two subclasses of `Colormap` provided here: `LinearSegmentedColormap`, which uses piecewise-linear interpolation to define colormaps, and `ListedColormap`, which makes a colormap from a list of colors. .. seealso:: :ref:`colormap-manipulation` for examples of how to make colormaps and :ref:`colormaps` for a list of built-in colormaps. :ref:`colormapnorms` for more details about data normalization More colormaps are available at palettable_. The module also provides functions for checking whether an object can be interpreted as a color (`is_color_like`), for converting such an object to an RGBA tuple (`to_rgba`) or to an HTML-like hex string in the "#rrggbb" format (`to_hex`), and a sequence of colors to an (n, 4) RGBA array (`to_rgba_array`). Caching is used for efficiency. Colors that Matplotlib recognizes are listed at :ref:`colors_def`. .. _palettable: https://jiffyclub.github.io/palettable/ .. _xkcd color survey: https://xkcd.com/color/rgb/ """ import base64 from collections.abc import Sized, Sequence, Mapping import functools import importlib import inspect import io import itertools from numbers import Real import re from PIL import Image from PIL.PngImagePlugin import PngInfo import matplotlib as mpl import numpy as np from matplotlib import _api, _cm, cbook, scale, _image from ._color_data import BASE_COLORS, TABLEAU_COLORS, CSS4_COLORS, XKCD_COLORS class _ColorMapping(dict): def __init__(self, mapping): super().__init__(mapping) self.cache = {} def __setitem__(self, key, value): super().__setitem__(key, value) self.cache.clear() def __delitem__(self, key): super().__delitem__(key) self.cache.clear() _colors_full_map = {} # Set by reverse priority order. _colors_full_map.update(XKCD_COLORS) _colors_full_map.update({k.replace('grey', 'gray'): v for k, v in XKCD_COLORS.items() if 'grey' in k}) _colors_full_map.update(CSS4_COLORS) _colors_full_map.update(TABLEAU_COLORS) _colors_full_map.update({k.replace('gray', 'grey'): v for k, v in TABLEAU_COLORS.items() if 'gray' in k}) _colors_full_map.update(BASE_COLORS) _colors_full_map = _ColorMapping(_colors_full_map) _REPR_PNG_SIZE = (512, 64) _BIVAR_REPR_PNG_SIZE = 256 def get_named_colors_mapping(): """Return the global mapping of names to named colors.""" return _colors_full_map class ColorSequenceRegistry(Mapping): r""" Container for sequences of colors that are known to Matplotlib by name. The universal registry instance is `matplotlib.color_sequences`. There should be no need for users to instantiate `.ColorSequenceRegistry` themselves. Read access uses a dict-like interface mapping names to lists of colors:: import matplotlib as mpl colors = mpl.color_sequences['tab10'] For a list of built in color sequences, see :doc:`/gallery/color/color_sequences`. The returned lists are copies, so that their modification does not change the global definition of the color sequence. Additional color sequences can be added via `.ColorSequenceRegistry.register`:: mpl.color_sequences.register('rgb', ['r', 'g', 'b']) """ _BUILTIN_COLOR_SEQUENCES = { 'tab10': _cm._tab10_data, 'tab20': _cm._tab20_data, 'tab20b': _cm._tab20b_data, 'tab20c': _cm._tab20c_data, 'Pastel1': _cm._Pastel1_data, 'Pastel2': _cm._Pastel2_data, 'Paired': _cm._Paired_data, 'Accent': _cm._Accent_data, 'Dark2': _cm._Dark2_data, 'Set1': _cm._Set1_data, 'Set2': _cm._Set2_data, 'Set3': _cm._Set3_data, 'petroff10': _cm._petroff10_data, } def __init__(self): self._color_sequences = {**self._BUILTIN_COLOR_SEQUENCES} def __getitem__(self, item): try: return list(self._color_sequences[item]) except KeyError: raise KeyError(f"{item!r} is not a known color sequence name") def __iter__(self): return iter(self._color_sequences) def __len__(self): return len(self._color_sequences) def __str__(self): return ('ColorSequenceRegistry; available colormaps:\n' + ', '.join(f"'{name}'" for name in self)) def register(self, name, color_list): """ Register a new color sequence. The color sequence registry stores a copy of the given *color_list*, so that future changes to the original list do not affect the registered color sequence. Think of this as the registry taking a snapshot of *color_list* at registration. Parameters ---------- name : str The name for the color sequence. color_list : list of :mpltype:`color` An iterable returning valid Matplotlib colors when iterating over. Note however that the returned color sequence will always be a list regardless of the input type. """ if name in self._BUILTIN_COLOR_SEQUENCES: raise ValueError(f"{name!r} is a reserved name for a builtin " "color sequence") color_list = list(color_list) # force copy and coerce type to list for color in color_list: try: to_rgba(color) except ValueError: raise ValueError( f"{color!r} is not a valid color specification") self._color_sequences[name] = color_list def unregister(self, name): """ Remove a sequence from the registry. You cannot remove built-in color sequences. If the name is not registered, returns with no error. """ if name in self._BUILTIN_COLOR_SEQUENCES: raise ValueError( f"Cannot unregister builtin color sequence {name!r}") self._color_sequences.pop(name, None) _color_sequences = ColorSequenceRegistry() def _sanitize_extrema(ex): if ex is None: return ex try: ret = ex.item() except AttributeError: ret = float(ex) return ret _nth_color_re = re.compile(r"\AC[0-9]+\Z") def _is_nth_color(c): """Return whether *c* can be interpreted as an item in the color cycle.""" return isinstance(c, str) and _nth_color_re.match(c) def is_color_like(c): """Return whether *c* can be interpreted as an RGB(A) color.""" # Special-case nth color syntax because it cannot be parsed during setup. if _is_nth_color(c): return True try: to_rgba(c) except (TypeError, ValueError): return False else: return True def _has_alpha_channel(c): """Return whether *c* is a color with an alpha channel.""" # 4-element sequences are interpreted as r, g, b, a return not isinstance(c, str) and len(c) == 4 def _check_color_like(**kwargs): """ For each *key, value* pair in *kwargs*, check that *value* is color-like. """ for k, v in kwargs.items(): if not is_color_like(v): raise ValueError( f"{v!r} is not a valid value for {k}: supported inputs are " f"(r, g, b) and (r, g, b, a) 0-1 float tuples; " f"'#rrggbb', '#rrggbbaa', '#rgb', '#rgba' strings; " f"named color strings; " f"string reprs of 0-1 floats for grayscale values; " f"'C0', 'C1', ... strings for colors of the color cycle; " f"and pairs combining one of the above with an alpha value") def same_color(c1, c2): """ Return whether the colors *c1* and *c2* are the same. *c1*, *c2* can be single colors or lists/arrays of colors. """ c1 = to_rgba_array(c1) c2 = to_rgba_array(c2) n1 = max(c1.shape[0], 1) # 'none' results in shape (0, 4), but is 1-elem n2 = max(c2.shape[0], 1) # 'none' results in shape (0, 4), but is 1-elem if n1 != n2: raise ValueError('Different number of elements passed.') # The following shape test is needed to correctly handle comparisons with # 'none', which results in a shape (0, 4) array and thus cannot be tested # via value comparison. return c1.shape == c2.shape and (c1 == c2).all() def to_rgba(c, alpha=None): """ Convert *c* to an RGBA color. Parameters ---------- c : Matplotlib color or ``np.ma.masked`` alpha : float, optional If *alpha* is given, force the alpha value of the returned RGBA tuple to *alpha*. If None, the alpha value from *c* is used. If *c* does not have an alpha channel, then alpha defaults to 1. *alpha* is ignored for the color value ``"none"`` (case-insensitive), which always maps to ``(0, 0, 0, 0)``. Returns ------- tuple Tuple of floats ``(r, g, b, a)``, where each channel (red, green, blue, alpha) can assume values between 0 and 1. """ if isinstance(c, tuple) and len(c) == 2: if alpha is None: c, alpha = c else: c = c[0] # Special-case nth color syntax because it should not be cached. if _is_nth_color(c): prop_cycler = mpl.rcParams['axes.prop_cycle'] colors = prop_cycler.by_key().get('color', ['k']) c = colors[int(c[1:]) % len(colors)] try: rgba = _colors_full_map.cache[c, alpha] except (KeyError, TypeError): # Not in cache, or unhashable. rgba = None if rgba is None: # Suppress exception chaining of cache lookup failure. rgba = _to_rgba_no_colorcycle(c, alpha) try: _colors_full_map.cache[c, alpha] = rgba except TypeError: pass return rgba def _to_rgba_no_colorcycle(c, alpha=None): """ Convert *c* to an RGBA color, with no support for color-cycle syntax. If *alpha* is given, force the alpha value of the returned RGBA tuple to *alpha*. Otherwise, the alpha value from *c* is used, if it has alpha information, or defaults to 1. *alpha* is ignored for the color value ``"none"`` (case-insensitive), which always maps to ``(0, 0, 0, 0)``. """ if alpha is not None and not 0 <= alpha <= 1: raise ValueError("'alpha' must be between 0 and 1, inclusive") orig_c = c if c is np.ma.masked: return (0., 0., 0., 0.) if isinstance(c, str): if c.lower() == "none": return (0., 0., 0., 0.) # Named color. try: # This may turn c into a non-string, so we check again below. c = _colors_full_map[c] except KeyError: if len(orig_c) != 1: try: c = _colors_full_map[c.lower()] except KeyError: pass if isinstance(c, str): # hex color in #rrggbb format. match = re.match(r"\A#[a-fA-F0-9]{6}\Z", c) if match: return (tuple(int(n, 16) / 255 for n in [c[1:3], c[3:5], c[5:7]]) + (alpha if alpha is not None else 1.,)) # hex color in #rgb format, shorthand for #rrggbb. match = re.match(r"\A#[a-fA-F0-9]{3}\Z", c) if match: return (tuple(int(n, 16) / 255 for n in [c[1]*2, c[2]*2, c[3]*2]) + (alpha if alpha is not None else 1.,)) # hex color with alpha in #rrggbbaa format. match = re.match(r"\A#[a-fA-F0-9]{8}\Z", c) if match: color = [int(n, 16) / 255 for n in [c[1:3], c[3:5], c[5:7], c[7:9]]] if alpha is not None: color[-1] = alpha return tuple(color) # hex color with alpha in #rgba format, shorthand for #rrggbbaa. match = re.match(r"\A#[a-fA-F0-9]{4}\Z", c) if match: color = [int(n, 16) / 255 for n in [c[1]*2, c[2]*2, c[3]*2, c[4]*2]] if alpha is not None: color[-1] = alpha return tuple(color) # string gray. try: c = float(c) except ValueError: pass else: if not (0 <= c <= 1): raise ValueError( f"Invalid string grayscale value {orig_c!r}. " f"Value must be within 0-1 range") return c, c, c, alpha if alpha is not None else 1. raise ValueError(f"Invalid RGBA argument: {orig_c!r}") # turn 2-D array into 1-D array if isinstance(c, np.ndarray): if c.ndim == 2 and c.shape[0] == 1: c = c.reshape(-1) # tuple color. if not np.iterable(c): raise ValueError(f"Invalid RGBA argument: {orig_c!r}") if len(c) not in [3, 4]: raise ValueError("RGBA sequence should have length 3 or 4") if not all(isinstance(x, Real) for x in c): # Checks that don't work: `map(float, ...)`, `np.array(..., float)` and # `np.array(...).astype(float)` would all convert "0.5" to 0.5. raise ValueError(f"Invalid RGBA argument: {orig_c!r}") # Return a tuple to prevent the cached value from being modified. c = tuple(map(float, c)) if len(c) == 3 and alpha is None: alpha = 1 if alpha is not None: c = c[:3] + (alpha,) if any(elem < 0 or elem > 1 for elem in c): raise ValueError("RGBA values should be within 0-1 range") return c def to_rgba_array(c, alpha=None): """ Convert *c* to a (n, 4) array of RGBA colors. Parameters ---------- c : Matplotlib color or array of colors If *c* is a masked array, an `~numpy.ndarray` is returned with a (0, 0, 0, 0) row for each masked value or row in *c*. alpha : float or sequence of floats, optional If *alpha* is given, force the alpha value of the returned RGBA tuple to *alpha*. If None, the alpha value from *c* is used. If *c* does not have an alpha channel, then alpha defaults to 1. *alpha* is ignored for the color value ``"none"`` (case-insensitive), which always maps to ``(0, 0, 0, 0)``. If *alpha* is a sequence and *c* is a single color, *c* will be repeated to match the length of *alpha*. Returns ------- array (n, 4) array of RGBA colors, where each channel (red, green, blue, alpha) can assume values between 0 and 1. """ if isinstance(c, tuple) and len(c) == 2 and isinstance(c[1], Real): if alpha is None: c, alpha = c else: c = c[0] # Special-case inputs that are already arrays, for performance. (If the # array has the wrong kind or shape, raise the error during one-at-a-time # conversion.) if np.iterable(alpha): alpha = np.asarray(alpha).ravel() if (isinstance(c, np.ndarray) and c.dtype.kind in "if" and c.ndim == 2 and c.shape[1] in [3, 4]): mask = c.mask.any(axis=1) if np.ma.is_masked(c) else None c = np.ma.getdata(c) if np.iterable(alpha): if c.shape[0] == 1 and alpha.shape[0] > 1: c = np.tile(c, (alpha.shape[0], 1)) elif c.shape[0] != alpha.shape[0]: raise ValueError("The number of colors must match the number" " of alpha values if there are more than one" " of each.") if c.shape[1] == 3: result = np.column_stack([c, np.zeros(len(c))]) result[:, -1] = alpha if alpha is not None else 1. elif c.shape[1] == 4: result = c.copy() if alpha is not None: result[:, -1] = alpha if mask is not None: result[mask] = 0 if np.any((result < 0) | (result > 1)): raise ValueError("RGBA values should be within 0-1 range") return result # Handle single values. # Note that this occurs *after* handling inputs that are already arrays, as # `to_rgba(c, alpha)` (below) is expensive for such inputs, due to the need # to format the array in the ValueError message(!). if cbook._str_lower_equal(c, "none"): return np.zeros((0, 4), float) try: if np.iterable(alpha): return np.array([to_rgba(c, a) for a in alpha], float) else: return np.array([to_rgba(c, alpha)], float) except TypeError: pass except ValueError as e: if e.args == ("'alpha' must be between 0 and 1, inclusive", ): # ValueError is from _to_rgba_no_colorcycle(). raise e if isinstance(c, str): raise ValueError(f"{c!r} is not a valid color value.") if len(c) == 0: return np.zeros((0, 4), float) # Quick path if the whole sequence can be directly converted to a numpy # array in one shot. if isinstance(c, Sequence): lens = {len(cc) if isinstance(cc, (list, tuple)) else -1 for cc in c} if lens == {3}: rgba = np.column_stack([c, np.ones(len(c))]) elif lens == {4}: rgba = np.array(c) else: rgba = np.array([to_rgba(cc) for cc in c]) else: rgba = np.array([to_rgba(cc) for cc in c]) if alpha is not None: rgba[:, 3] = alpha if isinstance(c, Sequence): # ensure that an explicit alpha does not overwrite full transparency # for "none" none_mask = [cbook._str_equal(cc, "none") for cc in c] rgba[:, 3][none_mask] = 0 return rgba def to_rgb(c): """Convert *c* to an RGB color, silently dropping the alpha channel.""" return to_rgba(c)[:3] def to_hex(c, keep_alpha=False): """ Convert *c* to a hex color. Parameters ---------- c : :ref:`color <colors_def>` or `numpy.ma.masked` keep_alpha : bool, default: False If False, use the ``#rrggbb`` format, otherwise use ``#rrggbbaa``. Returns ------- str ``#rrggbb`` or ``#rrggbbaa`` hex color string """ c = to_rgba(c) if not keep_alpha: c = c[:3] return "#" + "".join(format(round(val * 255), "02x") for val in c) ### Backwards-compatible color-conversion API cnames = CSS4_COLORS hexColorPattern = re.compile(r"\A#[a-fA-F0-9]{6}\Z") rgb2hex = to_hex hex2color = to_rgb class ColorConverter: """ A class only kept for backwards compatibility. Its functionality is entirely provided by module-level functions. """ colors = _colors_full_map cache = _colors_full_map.cache to_rgb = staticmethod(to_rgb) to_rgba = staticmethod(to_rgba) to_rgba_array = staticmethod(to_rgba_array) colorConverter = ColorConverter() ### End of backwards-compatible color-conversion API def _create_lookup_table(N, data, gamma=1.0): r""" Create an *N* -element 1D lookup table. This assumes a mapping :math:`f : [0, 1] \rightarrow [0, 1]`. The returned data is an array of N values :math:`y = f(x)` where x is sampled from [0, 1]. By default (*gamma* = 1) x is equidistantly sampled from [0, 1]. The *gamma* correction factor :math:`\gamma` distorts this equidistant sampling by :math:`x \rightarrow x^\gamma`. Parameters ---------- N : int The number of elements of the created lookup table; at least 1. data : (M, 3) array-like or callable Defines the mapping :math:`f`. If a (M, 3) array-like, the rows define values (x, y0, y1). The x values must start with x=0, end with x=1, and all x values be in increasing order. A value between :math:`x_i` and :math:`x_{i+1}` is mapped to the range :math:`y^1_{i-1} \ldots y^0_i` by linear interpolation. For the simple case of a y-continuous mapping, y0 and y1 are identical. The two values of y are to allow for discontinuous mapping functions. E.g. a sawtooth with a period of 0.2 and an amplitude of 1 would be:: [(0, 1, 0), (0.2, 1, 0), (0.4, 1, 0), ..., [(1, 1, 0)] In the special case of ``N == 1``, by convention the returned value is y0 for x == 1. If *data* is a callable, it must accept and return numpy arrays:: data(x : ndarray) -> ndarray and map values between 0 - 1 to 0 - 1. gamma : float Gamma correction factor for input distribution x of the mapping. See also https://en.wikipedia.org/wiki/Gamma_correction. Returns ------- array The lookup table where ``lut[x * (N-1)]`` gives the closest value for values of x between 0 and 1. Notes ----- This function is internally used for `.LinearSegmentedColormap`. """ if callable(data): xind = np.linspace(0, 1, N) ** gamma lut = np.clip(np.array(data(xind), dtype=float), 0, 1) return lut try: adata = np.array(data) except Exception as err: raise TypeError("data must be convertible to an array") from err _api.check_shape((None, 3), data=adata) x = adata[:, 0] y0 = adata[:, 1] y1 = adata[:, 2] if x[0] != 0. or x[-1] != 1.0: raise ValueError( "data mapping points must start with x=0 and end with x=1") if (np.diff(x) < 0).any(): raise ValueError("data mapping points must have x in increasing order") # begin generation of lookup table if N == 1: # convention: use the y = f(x=1) value for a 1-element lookup table lut = np.array(y0[-1]) else: x = x * (N - 1) xind = (N - 1) * np.linspace(0, 1, N) ** gamma ind = np.searchsorted(x, xind)[1:-1] distance = (xind[1:-1] - x[ind - 1]) / (x[ind] - x[ind - 1]) lut = np.concatenate([ [y1[0]], distance * (y0[ind] - y1[ind - 1]) + y1[ind - 1], [y0[-1]], ]) # ensure that the lut is confined to values between 0 and 1 by clipping it return np.clip(lut, 0.0, 1.0) class Colormap: """ Baseclass for all scalar to RGBA mappings. Typically, Colormap instances are used to convert data values (floats) from the interval ``[0, 1]`` to the RGBA color that the respective Colormap represents. For scaling of data into the ``[0, 1]`` interval see `matplotlib.colors.Normalize`. Subclasses of `matplotlib.cm.ScalarMappable` make heavy use of this ``data -> normalize -> map-to-color`` processing chain. """ def __init__(self, name, N=256): """ Parameters ---------- name : str The name of the colormap. N : int The number of RGB quantization levels. """ self.name = name self.N = int(N) # ensure that N is always int self._rgba_bad = (0.0, 0.0, 0.0, 0.0) # If bad, don't paint anything. self._rgba_under = None self._rgba_over = None self._i_under = self.N self._i_over = self.N + 1 self._i_bad = self.N + 2 self._isinit = False self.n_variates = 1 #: When this colormap exists on a scalar mappable and colorbar_extend #: is not False, colorbar creation will pick up ``colorbar_extend`` as #: the default value for the ``extend`` keyword in the #: `matplotlib.colorbar.Colorbar` constructor. self.colorbar_extend = False def __call__(self, X, alpha=None, bytes=False): r""" Parameters ---------- X : float or int or array-like The data value(s) to convert to RGBA. For floats, *X* should be in the interval ``[0.0, 1.0]`` to return the RGBA values ``X*100`` percent along the Colormap line. For integers, *X* should be in the interval ``[0, Colormap.N)`` to return RGBA values *indexed* from the Colormap with index ``X``. alpha : float or array-like or None Alpha must be a scalar between 0 and 1, a sequence of such floats with shape matching X, or None. bytes : bool, default: False If False (default), the returned RGBA values will be floats in the interval ``[0, 1]`` otherwise they will be `numpy.uint8`\s in the interval ``[0, 255]``. Returns ------- Tuple of RGBA values if X is scalar, otherwise an array of RGBA values with a shape of ``X.shape + (4, )``. """ rgba, mask = self._get_rgba_and_mask(X, alpha=alpha, bytes=bytes) if not np.iterable(X): rgba = tuple(rgba) return rgba def _get_rgba_and_mask(self, X, alpha=None, bytes=False): r""" Parameters ---------- X : float or int or array-like The data value(s) to convert to RGBA. For floats, *X* should be in the interval ``[0.0, 1.0]`` to return the RGBA values ``X*100`` percent along the Colormap line. For integers, *X* should be in the interval ``[0, Colormap.N)`` to return RGBA values *indexed* from the Colormap with index ``X``. alpha : float or array-like or None Alpha must be a scalar between 0 and 1, a sequence of such floats with shape matching X, or None. bytes : bool, default: False If False (default), the returned RGBA values will be floats in the interval ``[0, 1]`` otherwise they will be `numpy.uint8`\s in the interval ``[0, 255]``. Returns ------- colors : np.ndarray Array of RGBA values with a shape of ``X.shape + (4, )``. mask : np.ndarray Boolean array with True where the input is ``np.nan`` or masked. """ if not self._isinit: self._init() xa = np.array(X, copy=True) if not xa.dtype.isnative: # Native byteorder is faster. xa = xa.byteswap().view(xa.dtype.newbyteorder()) if xa.dtype.kind == "f": xa *= self.N # xa == 1 (== N after multiplication) is not out of range. xa[xa == self.N] = self.N - 1 # Pre-compute the masks before casting to int (which can truncate # negative values to zero or wrap large floats to negative ints). mask_under = xa < 0 mask_over = xa >= self.N # If input was masked, get the bad mask from it; else mask out nans. mask_bad = X.mask if np.ma.is_masked(X) else np.isnan(xa) with np.errstate(invalid="ignore"): # We need this cast for unsigned ints as well as floats xa = xa.astype(int) xa[mask_under] = self._i_under xa[mask_over] = self._i_over xa[mask_bad] = self._i_bad lut = self._lut if bytes: lut = (lut * 255).astype(np.uint8) rgba = lut.take(xa, axis=0, mode='clip') if alpha is not None: alpha = np.clip(alpha, 0, 1) if bytes: alpha *= 255 # Will be cast to uint8 upon assignment. if alpha.shape not in [(), xa.shape]: raise ValueError( f"alpha is array-like but its shape {alpha.shape} does " f"not match that of X {xa.shape}") rgba[..., -1] = alpha # If the "bad" color is all zeros, then ignore alpha input. if (lut[-1] == 0).all(): rgba[mask_bad] = (0, 0, 0, 0) return rgba, mask_bad def __copy__(self): cls = self.__class__ cmapobject = cls.__new__(cls) cmapobject.__dict__.update(self.__dict__) if self._isinit: cmapobject._lut = np.copy(self._lut) return cmapobject def __eq__(self, other): if (not isinstance(other, Colormap) or self.colorbar_extend != other.colorbar_extend): return False # To compare lookup tables the Colormaps have to be initialized if not self._isinit: self._init() if not other._isinit: other._init() return np.array_equal(self._lut, other._lut) def get_bad(self): """Get the color for masked values.""" if not self._isinit: self._init() return np.array(self._lut[self._i_bad]) def set_bad(self, color='k', alpha=None): """Set the color for masked values.""" self._rgba_bad = to_rgba(color, alpha) if self._isinit: self._set_extremes() def get_under(self): """Get the color for low out-of-range values.""" if not self._isinit: self._init() return np.array(self._lut[self._i_under]) def set_under(self, color='k', alpha=None): """Set the color for low out-of-range values.""" self._rgba_under = to_rgba(color, alpha) if self._isinit: self._set_extremes() def get_over(self): """Get the color for high out-of-range values.""" if not self._isinit: self._init() return np.array(self._lut[self._i_over]) def set_over(self, color='k', alpha=None): """Set the color for high out-of-range values.""" self._rgba_over = to_rgba(color, alpha) if self._isinit: self._set_extremes() def set_extremes(self, *, bad=None, under=None, over=None): """ Set the colors for masked (*bad*) values and, when ``norm.clip = False``, low (*under*) and high (*over*) out-of-range values. """ if bad is not None: self.set_bad(bad) if under is not None: self.set_under(under) if over is not None: self.set_over(over) def with_extremes(self, *, bad=None, under=None, over=None): """ Return a copy of the colormap, for which the colors for masked (*bad*) values and, when ``norm.clip = False``, low (*under*) and high (*over*) out-of-range values, have been set accordingly. """ new_cm = self.copy() new_cm.set_extremes(bad=bad, under=under, over=over) return new_cm def _set_extremes(self): if self._rgba_under: self._lut[self._i_under] = self._rgba_under else: self._lut[self._i_under] = self._lut[0] if self._rgba_over: self._lut[self._i_over] = self._rgba_over else: self._lut[self._i_over] = self._lut[self.N - 1] self._lut[self._i_bad] = self._rgba_bad def _init(self): """Generate the lookup table, ``self._lut``.""" raise NotImplementedError("Abstract class only") def is_gray(self): """Return whether the colormap is grayscale.""" if not self._isinit: self._init() return (np.all(self._lut[:, 0] == self._lut[:, 1]) and np.all(self._lut[:, 0] == self._lut[:, 2])) def resampled(self, lutsize): """Return a new colormap with *lutsize* entries.""" if hasattr(self, '_resample'): _api.warn_external( "The ability to resample a color map is now public API " f"However the class {type(self)} still only implements " "the previous private _resample method. Please update " "your class." ) return self._resample(lutsize) raise NotImplementedError() def reversed(self, name=None): """ Return a reversed instance of the Colormap. .. note:: This function is not implemented for the base class. Parameters ---------- name : str, optional The name for the reversed colormap. If None, the name is set to ``self.name + "_r"``. See Also -------- LinearSegmentedColormap.reversed ListedColormap.reversed """ raise NotImplementedError() def _repr_png_(self): """Generate a PNG representation of the Colormap.""" X = np.tile(np.linspace(0, 1, _REPR_PNG_SIZE[0]), (_REPR_PNG_SIZE[1], 1)) pixels = self(X, bytes=True) png_bytes = io.BytesIO() title = self.name + ' colormap' author = f'Matplotlib v{mpl.__version__}, https://matplotlib.org' pnginfo = PngInfo() pnginfo.add_text('Title', title) pnginfo.add_text('Description', title) pnginfo.add_text('Author', author) pnginfo.add_text('Software', author) Image.fromarray(pixels).save(png_bytes, format='png', pnginfo=pnginfo) return png_bytes.getvalue() def _repr_html_(self): """Generate an HTML representation of the Colormap.""" png_bytes = self._repr_png_() png_base64 = base64.b64encode(png_bytes).decode('ascii') def color_block(color): hex_color = to_hex(color, keep_alpha=True) return (f'<div title="{hex_color}" ' 'style="display: inline-block; ' 'width: 1em; height: 1em; ' 'margin: 0; ' 'vertical-align: middle; ' 'border: 1px solid #555; ' f'background-color: {hex_color};"></div>') return ('<div style="vertical-align: middle;">' f'<strong>{self.name}</strong> ' '</div>' '<div class="cmap"><img ' f'alt="{self.name} colormap" ' f'title="{self.name}" ' 'style="border: 1px solid #555;" ' f'src="data:image/png;base64,{png_base64}"></div>' '<div style="vertical-align: middle; ' f'max-width: {_REPR_PNG_SIZE[0]+2}px; ' 'display: flex; justify-content: space-between;">' '<div style="float: left;">' f'{color_block(self.get_under())} under' '</div>' '<div style="margin: 0 auto; display: inline-block;">' f'bad {color_block(self.get_bad())}' '</div>' '<div style="float: right;">' f'over {color_block(self.get_over())}' '</div>' '</div>') def copy(self): """Return a copy of the colormap.""" return self.__copy__() class LinearSegmentedColormap(Colormap): """ Colormap objects based on lookup tables using linear segments. The lookup table is generated using linear interpolation for each primary color, with the 0-1 domain divided into any number of segments. """ def __init__(self, name, segmentdata, N=256, gamma=1.0): """ Create colormap from linear mapping segments segmentdata argument is a dictionary with a red, green and blue entries. Each entry should be a list of *x*, *y0*, *y1* tuples, forming rows in a table. Entries for alpha are optional. Example: suppose you want red to increase from 0 to 1 over the bottom half, green to do the same over the middle half, and blue over the top half. Then you would use:: cdict = {'red': [(0.0, 0.0, 0.0), (0.5, 1.0, 1.0), (1.0, 1.0, 1.0)], 'green': [(0.0, 0.0, 0.0), (0.25, 0.0, 0.0), (0.75, 1.0, 1.0), (1.0, 1.0, 1.0)], 'blue': [(0.0, 0.0, 0.0), (0.5, 0.0, 0.0), (1.0, 1.0, 1.0)]} Each row in the table for a given color is a sequence of *x*, *y0*, *y1* tuples. In each sequence, *x* must increase monotonically from 0 to 1. For any input value *z* falling between *x[i]* and *x[i+1]*, the output value of a given color will be linearly interpolated between *y1[i]* and *y0[i+1]*:: row i: x y0 y1 / / row i+1: x y0 y1 Hence y0 in the first row and y1 in the last row are never used. See Also -------- LinearSegmentedColormap.from_list Static method; factory function for generating a smoothly-varying LinearSegmentedColormap. """ # True only if all colors in map are identical; needed for contouring. self.monochrome = False super().__init__(name, N) self._segmentdata = segmentdata self._gamma = gamma def _init(self): self._lut = np.ones((self.N + 3, 4), float) self._lut[:-3, 0] = _create_lookup_table( self.N, self._segmentdata['red'], self._gamma) self._lut[:-3, 1] = _create_lookup_table( self.N, self._segmentdata['green'], self._gamma) self._lut[:-3, 2] = _create_lookup_table( self.N, self._segmentdata['blue'], self._gamma) if 'alpha' in self._segmentdata: self._lut[:-3, 3] = _create_lookup_table( self.N, self._segmentdata['alpha'], 1) self._isinit = True self._set_extremes() def set_gamma(self, gamma): """Set a new gamma value and regenerate colormap.""" self._gamma = gamma self._init() @staticmethod def from_list(name, colors, N=256, gamma=1.0): """ Create a `LinearSegmentedColormap` from a list of colors. Parameters ---------- name : str The name of the colormap. colors : list of :mpltype:`color` or list of (value, color) If only colors are given, they are equidistantly mapped from the range :math:`[0, 1]`; i.e. 0 maps to ``colors[0]`` and 1 maps to ``colors[-1]``. If (value, color) pairs are given, the mapping is from *value* to *color*. This can be used to divide the range unevenly. N : int The number of RGB quantization levels. gamma : float """ if not np.iterable(colors): raise ValueError('colors must be iterable') if (isinstance(colors[0], Sized) and len(colors[0]) == 2 and not isinstance(colors[0], str)): # List of value, color pairs vals, colors = zip(*colors) else: vals = np.linspace(0, 1, len(colors)) r, g, b, a = to_rgba_array(colors).T cdict = { "red": np.column_stack([vals, r, r]), "green": np.column_stack([vals, g, g]), "blue": np.column_stack([vals, b, b]), "alpha": np.column_stack([vals, a, a]), } return LinearSegmentedColormap(name, cdict, N, gamma) def resampled(self, lutsize): """Return a new colormap with *lutsize* entries.""" new_cmap = LinearSegmentedColormap(self.name, self._segmentdata, lutsize) new_cmap._rgba_over = self._rgba_over new_cmap._rgba_under = self._rgba_under new_cmap._rgba_bad = self._rgba_bad return new_cmap # Helper ensuring picklability of the reversed cmap. @staticmethod def _reverser(func, x): return func(1 - x) def reversed(self, name=None): """ Return a reversed instance of the Colormap. Parameters ---------- name : str, optional The name for the reversed colormap. If None, the name is set to ``self.name + "_r"``. Returns ------- LinearSegmentedColormap The reversed colormap. """ if name is None: name = self.name + "_r" # Using a partial object keeps the cmap picklable. data_r = {key: (functools.partial(self._reverser, data) if callable(data) else [(1.0 - x, y1, y0) for x, y0, y1 in reversed(data)]) for key, data in self._segmentdata.items()} new_cmap = LinearSegmentedColormap(name, data_r, self.N, self._gamma) # Reverse the over/under values too new_cmap._rgba_over = self._rgba_under new_cmap._rgba_under = self._rgba_over new_cmap._rgba_bad = self._rgba_bad return new_cmap class ListedColormap(Colormap): """ Colormap object generated from a list of colors. This may be most useful when indexing directly into a colormap, but it can also be used to generate special colormaps for ordinary mapping. Parameters ---------- colors : list, array Sequence of Matplotlib color specifications (color names or RGB(A) values). name : str, optional String to identify the colormap. N : int, optional Number of entries in the map. The default is *None*, in which case there is one colormap entry for each element in the list of colors. If :: N < len(colors) the list will be truncated at *N*. If :: N > len(colors) the list will be extended by repetition. """ def __init__(self, colors, name='from_list', N=None): self.monochrome = False # Are all colors identical? (for contour.py) if N is None: self.colors = colors N = len(colors) else: if isinstance(colors, str): self.colors = [colors] * N self.monochrome = True elif np.iterable(colors): if len(colors) == 1: self.monochrome = True self.colors = list( itertools.islice(itertools.cycle(colors), N)) else: try: gray = float(colors) except TypeError: pass else: self.colors = [gray] * N self.monochrome = True super().__init__(name, N) def _init(self): self._lut = np.zeros((self.N + 3, 4), float) self._lut[:-3] = to_rgba_array(self.colors) self._isinit = True self._set_extremes() def resampled(self, lutsize): """Return a new colormap with *lutsize* entries.""" colors = self(np.linspace(0, 1, lutsize)) new_cmap = ListedColormap(colors, name=self.name) # Keep the over/under values too new_cmap._rgba_over = self._rgba_over new_cmap._rgba_under = self._rgba_under new_cmap._rgba_bad = self._rgba_bad return new_cmap def reversed(self, name=None): """ Return a reversed instance of the Colormap. Parameters ---------- name : str, optional The name for the reversed colormap. If None, the name is set to ``self.name + "_r"``. Returns ------- ListedColormap A reversed instance of the colormap. """ if name is None: name = self.name + "_r" colors_r = list(reversed(self.colors)) new_cmap = ListedColormap(colors_r, name=name, N=self.N) # Reverse the over/under values too new_cmap._rgba_over = self._rgba_under new_cmap._rgba_under = self._rgba_over new_cmap._rgba_bad = self._rgba_bad return new_cmap class MultivarColormap: """ Class for holding multiple `~matplotlib.colors.Colormap` for use in a `~matplotlib.cm.ScalarMappable` object """ def __init__(self, colormaps, combination_mode, name='multivariate colormap'): """ Parameters ---------- colormaps: list or tuple of `~matplotlib.colors.Colormap` objects The individual colormaps that are combined combination_mode: str, 'sRGB_add' or 'sRGB_sub' Describe how colormaps are combined in sRGB space - If 'sRGB_add' -> Mixing produces brighter colors `sRGB = sum(colors)` - If 'sRGB_sub' -> Mixing produces darker colors `sRGB = 1 - sum(1 - colors)` name : str, optional The name of the colormap family. """ self.name = name if not np.iterable(colormaps) \ or len(colormaps) == 1 \ or isinstance(colormaps, str): raise ValueError("A MultivarColormap must have more than one colormap.") colormaps = list(colormaps) # ensure cmaps is a list, i.e. not a tuple for i, cmap in enumerate(colormaps): if isinstance(cmap, str): colormaps[i] = mpl.colormaps[cmap] elif not isinstance(cmap, Colormap): raise ValueError("colormaps must be a list of objects that subclass" " Colormap or a name found in the colormap registry.") self._colormaps = colormaps _api.check_in_list(['sRGB_add', 'sRGB_sub'], combination_mode=combination_mode) self._combination_mode = combination_mode self.n_variates = len(colormaps) self._rgba_bad = (0.0, 0.0, 0.0, 0.0) # If bad, don't paint anything. def __call__(self, X, alpha=None, bytes=False, clip=True): r""" Parameters ---------- X : tuple (X0, X1, ...) of length equal to the number of colormaps X0, X1 ...: float or int, `~numpy.ndarray` or scalar The data value(s) to convert to RGBA. For floats, *Xi...* should be in the interval ``[0.0, 1.0]`` to return the RGBA values ``X*100`` percent along the Colormap line. For integers, *Xi...* should be in the interval ``[0, self[i].N)`` to return RGBA values *indexed* from colormap [i] with index ``Xi``, where self[i] is colormap i. alpha : float or array-like or None Alpha must be a scalar between 0 and 1, a sequence of such floats with shape matching *Xi*, or None. bytes : bool, default: False If False (default), the returned RGBA values will be floats in the interval ``[0, 1]`` otherwise they will be `numpy.uint8`\s in the interval ``[0, 255]``. clip : bool, default: True If True, clip output to 0 to 1 Returns ------- Tuple of RGBA values if X[0] is scalar, otherwise an array of RGBA values with a shape of ``X.shape + (4, )``. """ if len(X) != len(self): raise ValueError( f'For the selected colormap the data must have a first dimension ' f'{len(self)}, not {len(X)}') rgba, mask_bad = self[0]._get_rgba_and_mask(X[0], bytes=False) for c, xx in zip(self[1:], X[1:]): sub_rgba, sub_mask_bad = c._get_rgba_and_mask(xx, bytes=False) rgba[..., :3] += sub_rgba[..., :3] # add colors rgba[..., 3] *= sub_rgba[..., 3] # multiply alpha mask_bad |= sub_mask_bad if self.combination_mode == 'sRGB_sub': rgba[..., :3] -= len(self) - 1 rgba[mask_bad] = self.get_bad() if clip: rgba = np.clip(rgba, 0, 1) if alpha is not None: if clip: alpha = np.clip(alpha, 0, 1) if np.shape(alpha) not in [(), np.shape(X[0])]: raise ValueError( f"alpha is array-like but its shape {np.shape(alpha)} does " f"not match that of X[0] {np.shape(X[0])}") rgba[..., -1] *= alpha if bytes: if not clip: raise ValueError( "clip cannot be false while bytes is true" " as uint8 does not support values below 0" " or above 255.") rgba = (rgba * 255).astype('uint8') if not np.iterable(X[0]): rgba = tuple(rgba) return rgba def copy(self): """Return a copy of the multivarcolormap.""" return self.__copy__() def __copy__(self): cls = self.__class__ cmapobject = cls.__new__(cls) cmapobject.__dict__.update(self.__dict__) cmapobject._colormaps = [cm.copy() for cm in self._colormaps] cmapobject._rgba_bad = np.copy(self._rgba_bad) return cmapobject def __eq__(self, other): if not isinstance(other, MultivarColormap): return False if len(self) != len(other): return False for c0, c1 in zip(self, other): if c0 != c1: return False if not all(self._rgba_bad == other._rgba_bad): return False if self.combination_mode != other.combination_mode: return False return True def __getitem__(self, item): return self._colormaps[item] def __iter__(self): for c in self._colormaps: yield c def __len__(self): return len(self._colormaps) def __str__(self): return self.name def get_bad(self): """Get the color for masked values.""" return np.array(self._rgba_bad) def resampled(self, lutshape): """ Return a new colormap with *lutshape* entries. Parameters ---------- lutshape : tuple of (`int`, `None`) The tuple must have a length matching the number of variates. For each element in the tuple, if `int`, the corresponding colorbar is resampled, if `None`, the corresponding colorbar is not resampled. Returns ------- MultivarColormap """ if not np.iterable(lutshape) or len(lutshape) != len(self): raise ValueError(f"lutshape must be of length {len(self)}") new_cmap = self.copy() for i, s in enumerate(lutshape): if s is not None: new_cmap._colormaps[i] = self[i].resampled(s) return new_cmap def with_extremes(self, *, bad=None, under=None, over=None): """ Return a copy of the `MultivarColormap` with modified out-of-range attributes. The *bad* keyword modifies the copied `MultivarColormap` while *under* and *over* modifies the attributes of the copied component colormaps. Note that *under* and *over* colors are subject to the mixing rules determined by the *combination_mode*. Parameters ---------- bad: :mpltype:`color`, default: None If Matplotlib color, the bad value is set accordingly in the copy under tuple of :mpltype:`color`, default: None If tuple, the `under` value of each component is set with the values from the tuple. over tuple of :mpltype:`color`, default: None If tuple, the `over` value of each component is set with the values from the tuple. Returns ------- MultivarColormap copy of self with attributes set """ new_cm = self.copy() if bad is not None: new_cm._rgba_bad = to_rgba(bad) if under is not None: if not np.iterable(under) or len(under) != len(new_cm): raise ValueError("*under* must contain a color for each scalar colormap" f" i.e. be of length {len(new_cm)}.") else: for c, b in zip(new_cm, under): c.set_under(b) if over is not None: if not np.iterable(over) or len(over) != len(new_cm): raise ValueError("*over* must contain a color for each scalar colormap" f" i.e. be of length {len(new_cm)}.") else: for c, b in zip(new_cm, over): c.set_over(b) return new_cm @property def combination_mode(self): return self._combination_mode def _repr_png_(self): """Generate a PNG representation of the Colormap.""" X = np.tile(np.linspace(0, 1, _REPR_PNG_SIZE[0]), (_REPR_PNG_SIZE[1], 1)) pixels = np.zeros((_REPR_PNG_SIZE[1]*len(self), _REPR_PNG_SIZE[0], 4), dtype=np.uint8) for i, c in enumerate(self): pixels[i*_REPR_PNG_SIZE[1]:(i+1)*_REPR_PNG_SIZE[1], :] = c(X, bytes=True) png_bytes = io.BytesIO() title = self.name + ' multivariate colormap' author = f'Matplotlib v{mpl.__version__}, https://matplotlib.org' pnginfo = PngInfo() pnginfo.add_text('Title', title) pnginfo.add_text('Description', title) pnginfo.add_text('Author', author) pnginfo.add_text('Software', author) Image.fromarray(pixels).save(png_bytes, format='png', pnginfo=pnginfo) return png_bytes.getvalue() def _repr_html_(self): """Generate an HTML representation of the MultivarColormap.""" return ''.join([c._repr_html_() for c in self._colormaps]) class BivarColormap: """ Base class for all bivariate to RGBA mappings. Designed as a drop-in replacement for Colormap when using a 2D lookup table. To be used with `~matplotlib.cm.ScalarMappable`. """ def __init__(self, N=256, M=256, shape='square', origin=(0, 0), name='bivariate colormap'): """ Parameters ---------- N : int, default: 256 The number of RGB quantization levels along the first axis. M : int, default: 256 The number of RGB quantization levels along the second axis. shape : {'square', 'circle', 'ignore', 'circleignore'} - 'square' each variate is clipped to [0,1] independently - 'circle' the variates are clipped radially to the center of the colormap, and a circular mask is applied when the colormap is displayed - 'ignore' the variates are not clipped, but instead assigned the 'outside' color - 'circleignore' a circular mask is applied, but the data is not clipped and instead assigned the 'outside' color origin : (float, float), default: (0,0) The relative origin of the colormap. Typically (0, 0), for colormaps that are linear on both axis, and (.5, .5) for circular colormaps. Used when getting 1D colormaps from 2D colormaps. name : str, optional The name of the colormap. """ self.name = name self.N = int(N) # ensure that N is always int self.M = int(M) _api.check_in_list(['square', 'circle', 'ignore', 'circleignore'], shape=shape) self._shape = shape self._rgba_bad = (0.0, 0.0, 0.0, 0.0) # If bad, don't paint anything. self._rgba_outside = (1.0, 0.0, 1.0, 1.0) self._isinit = False self.n_variates = 2 self._origin = (float(origin[0]), float(origin[1])) '''#: When this colormap exists on a scalar mappable and colorbar_extend #: is not False, colorbar creation will pick up ``colorbar_extend`` as #: the default value for the ``extend`` keyword in the #: `matplotlib.colorbar.Colorbar` constructor. self.colorbar_extend = False''' def __call__(self, X, alpha=None, bytes=False): r""" Parameters ---------- X : tuple (X0, X1), X0 and X1: float or int or array-like The data value(s) to convert to RGBA. - For floats, *X* should be in the interval ``[0.0, 1.0]`` to return the RGBA values ``X*100`` percent along the Colormap. - For integers, *X* should be in the interval ``[0, Colormap.N)`` to return RGBA values *indexed* from the Colormap with index ``X``. alpha : float or array-like or None, default: None Alpha must be a scalar between 0 and 1, a sequence of such floats with shape matching X0, or None. bytes : bool, default: False If False (default), the returned RGBA values will be floats in the interval ``[0, 1]`` otherwise they will be `numpy.uint8`\s in the interval ``[0, 255]``. Returns ------- Tuple of RGBA values if X is scalar, otherwise an array of RGBA values with a shape of ``X.shape + (4, )``. """ if len(X) != 2: raise ValueError( f'For a `BivarColormap` the data must have a first dimension ' f'2, not {len(X)}') if not self._isinit: self._init() X0 = np.ma.array(X[0], copy=True) X1 = np.ma.array(X[1], copy=True) # clip to shape of colormap, circle square, etc. self._clip((X0, X1)) # Native byteorder is faster. if not X0.dtype.isnative: X0 = X0.byteswap().view(X0.dtype.newbyteorder()) if not X1.dtype.isnative: X1 = X1.byteswap().view(X1.dtype.newbyteorder()) if X0.dtype.kind == "f": X0 *= self.N # xa == 1 (== N after multiplication) is not out of range. X0[X0 == self.N] = self.N - 1 if X1.dtype.kind == "f": X1 *= self.M # xa == 1 (== N after multiplication) is not out of range. X1[X1 == self.M] = self.M - 1 # Pre-compute the masks before casting to int (which can truncate) mask_outside = (X0 < 0) | (X1 < 0) | (X0 >= self.N) | (X1 >= self.M) # If input was masked, get the bad mask from it; else mask out nans. mask_bad_0 = X0.mask if np.ma.is_masked(X0) else np.isnan(X0) mask_bad_1 = X1.mask if np.ma.is_masked(X1) else np.isnan(X1) mask_bad = mask_bad_0 | mask_bad_1 with np.errstate(invalid="ignore"): # We need this cast for unsigned ints as well as floats X0 = X0.astype(int) X1 = X1.astype(int) # Set masked values to zero # The corresponding rgb values will be replaced later for X_part in [X0, X1]: X_part[mask_outside] = 0 X_part[mask_bad] = 0 rgba = self._lut[X0, X1] if np.isscalar(X[0]): rgba = np.copy(rgba) rgba[mask_outside] = self._rgba_outside rgba[mask_bad] = self._rgba_bad if bytes: rgba = (rgba * 255).astype(np.uint8) if alpha is not None: alpha = np.clip(alpha, 0, 1) if bytes: alpha *= 255 # Will be cast to uint8 upon assignment. if np.shape(alpha) not in [(), np.shape(X0)]: raise ValueError( f"alpha is array-like but its shape {np.shape(alpha)} does " f"not match that of X[0] {np.shape(X0)}") rgba[..., -1] = alpha # If the "bad" color is all zeros, then ignore alpha input. if (np.array(self._rgba_bad) == 0).all(): rgba[mask_bad] = (0, 0, 0, 0) if not np.iterable(X[0]): rgba = tuple(rgba) return rgba @property def lut(self): """ For external access to the lut, i.e. for displaying the cmap. For circular colormaps this returns a lut with a circular mask. Internal functions (such as to_rgb()) should use _lut which stores the lut without a circular mask A lut without the circular mask is needed in to_rgb() because the conversion from floats to ints results in some some pixel-requests just outside of the circular mask """ if not self._isinit: self._init() lut = np.copy(self._lut) if self.shape == 'circle' or self.shape == 'circleignore': n = np.linspace(-1, 1, self.N) m = np.linspace(-1, 1, self.M) radii_sqr = (n**2)[:, np.newaxis] + (m**2)[np.newaxis, :] mask_outside = radii_sqr > 1 lut[mask_outside, 3] = 0 return lut def __copy__(self): cls = self.__class__ cmapobject = cls.__new__(cls) cmapobject.__dict__.update(self.__dict__) cmapobject._rgba_outside = np.copy(self._rgba_outside) cmapobject._rgba_bad = np.copy(self._rgba_bad) cmapobject._shape = self.shape if self._isinit: cmapobject._lut = np.copy(self._lut) return cmapobject def __eq__(self, other): if not isinstance(other, BivarColormap): return False # To compare lookup tables the Colormaps have to be initialized if not self._isinit: self._init() if not other._isinit: other._init() if not np.array_equal(self._lut, other._lut): return False if not np.array_equal(self._rgba_bad, other._rgba_bad): return False if not np.array_equal(self._rgba_outside, other._rgba_outside): return False if self.shape != other.shape: return False return True def get_bad(self): """Get the color for masked values.""" return self._rgba_bad def get_outside(self): """Get the color for out-of-range values.""" return self._rgba_outside def resampled(self, lutshape, transposed=False): """ Return a new colormap with *lutshape* entries. Note that this function does not move the origin. Parameters ---------- lutshape : tuple of ints or None The tuple must be of length 2, and each entry is either an int or None. - If an int, the corresponding axis is resampled. - If negative the corresponding axis is resampled in reverse - If -1, the axis is inverted - If 1 or None, the corresponding axis is not resampled. transposed : bool, default: False if True, the axes are swapped after resampling Returns ------- BivarColormap """ if not np.iterable(lutshape) or len(lutshape) != 2: raise ValueError("lutshape must be of length 2") lutshape = [lutshape[0], lutshape[1]] if lutshape[0] is None or lutshape[0] == 1: lutshape[0] = self.N if lutshape[1] is None or lutshape[1] == 1: lutshape[1] = self.M inverted = [False, False] if lutshape[0] < 0: inverted[0] = True lutshape[0] = -lutshape[0] if lutshape[0] == 1: lutshape[0] = self.N if lutshape[1] < 0: inverted[1] = True lutshape[1] = -lutshape[1] if lutshape[1] == 1: lutshape[1] = self.M x_0, x_1 = np.mgrid[0:1:(lutshape[0] * 1j), 0:1:(lutshape[1] * 1j)] if inverted[0]: x_0 = x_0[::-1, :] if inverted[1]: x_1 = x_1[:, ::-1] # we need to use shape = 'square' while resampling the colormap. # if the colormap has shape = 'circle' we would otherwise get *outside* in the # resampled colormap shape_memory = self._shape self._shape = 'square' if transposed: new_lut = self((x_1, x_0)) new_cmap = BivarColormapFromImage(new_lut, name=self.name, shape=shape_memory, origin=self.origin[::-1]) else: new_lut = self((x_0, x_1)) new_cmap = BivarColormapFromImage(new_lut, name=self.name, shape=shape_memory, origin=self.origin) self._shape = shape_memory new_cmap._rgba_bad = self._rgba_bad new_cmap._rgba_outside = self._rgba_outside return new_cmap def reversed(self, axis_0=True, axis_1=True): """ Reverses both or one of the axis. """ r_0 = -1 if axis_0 else 1 r_1 = -1 if axis_1 else 1 return self.resampled((r_0, r_1)) def transposed(self): """ Transposes the colormap by swapping the order of the axis """ return self.resampled((None, None), transposed=True) def with_extremes(self, *, bad=None, outside=None, shape=None, origin=None): """ Return a copy of the `BivarColormap` with modified attributes. Note that the *outside* color is only relevant if `shape` = 'ignore' or 'circleignore'. Parameters ---------- bad : None or :mpltype:`color` If Matplotlib color, the *bad* value is set accordingly in the copy outside : None or :mpltype:`color` If Matplotlib color and shape is 'ignore' or 'circleignore', values *outside* the colormap are colored accordingly in the copy shape : {'square', 'circle', 'ignore', 'circleignore'} - If 'square' each variate is clipped to [0,1] independently - If 'circle' the variates are clipped radially to the center of the colormap, and a circular mask is applied when the colormap is displayed - If 'ignore' the variates are not clipped, but instead assigned the *outside* color - If 'circleignore' a circular mask is applied, but the data is not clipped and instead assigned the *outside* color origin : (float, float) The relative origin of the colormap. Typically (0, 0), for colormaps that are linear on both axis, and (.5, .5) for circular colormaps. Used when getting 1D colormaps from 2D colormaps. Returns ------- BivarColormap copy of self with attributes set """ new_cm = self.copy() if bad is not None: new_cm._rgba_bad = to_rgba(bad) if outside is not None: new_cm._rgba_outside = to_rgba(outside) if shape is not None: _api.check_in_list(['square', 'circle', 'ignore', 'circleignore'], shape=shape) new_cm._shape = shape if origin is not None: new_cm._origin = (float(origin[0]), float(origin[1])) return new_cm def _init(self): """Generate the lookup table, ``self._lut``.""" raise NotImplementedError("Abstract class only") @property def shape(self): return self._shape @property def origin(self): return self._origin def _clip(self, X): """ For internal use when applying a BivarColormap to data. i.e. cm.ScalarMappable().to_rgba() Clips X[0] and X[1] according to 'self.shape'. X is modified in-place. Parameters ---------- X: np.array array of floats or ints to be clipped shape : {'square', 'circle', 'ignore', 'circleignore'} - If 'square' each variate is clipped to [0,1] independently - If 'circle' the variates are clipped radially to the center of the colormap. It is assumed that a circular mask is applied when the colormap is displayed - If 'ignore' the variates are not clipped, but instead assigned the 'outside' color - If 'circleignore' a circular mask is applied, but the data is not clipped and instead assigned the 'outside' color """ if self.shape == 'square': for X_part, mx in zip(X, (self.N, self.M)): X_part[X_part < 0] = 0 if X_part.dtype.kind == "f": X_part[X_part > 1] = 1 else: X_part[X_part >= mx] = mx - 1 elif self.shape == 'ignore': for X_part, mx in zip(X, (self.N, self.M)): X_part[X_part < 0] = -1 if X_part.dtype.kind == "f": X_part[X_part > 1] = -1 else: X_part[X_part >= mx] = -1 elif self.shape == 'circle' or self.shape == 'circleignore': for X_part in X: if X_part.dtype.kind != "f": raise NotImplementedError( "Circular bivariate colormaps are only" " implemented for use with with floats") radii_sqr = (X[0] - 0.5)**2 + (X[1] - 0.5)**2 mask_outside = radii_sqr > 0.25 if self.shape == 'circle': overextend = 2 * np.sqrt(radii_sqr[mask_outside]) X[0][mask_outside] = (X[0][mask_outside] - 0.5) / overextend + 0.5 X[1][mask_outside] = (X[1][mask_outside] - 0.5) / overextend + 0.5 else: X[0][mask_outside] = -1 X[1][mask_outside] = -1 def __getitem__(self, item): """Creates and returns a colorbar along the selected axis""" if not self._isinit: self._init() if item == 0: origin_1_as_int = int(self._origin[1]*self.M) if origin_1_as_int > self.M-1: origin_1_as_int = self.M-1 one_d_lut = self._lut[:, origin_1_as_int] new_cmap = ListedColormap(one_d_lut, name=f'{self.name}_0', N=self.N) elif item == 1: origin_0_as_int = int(self._origin[0]*self.N) if origin_0_as_int > self.N-1: origin_0_as_int = self.N-1 one_d_lut = self._lut[origin_0_as_int, :] new_cmap = ListedColormap(one_d_lut, name=f'{self.name}_1', N=self.M) else: raise KeyError(f"only 0 or 1 are" f" valid keys for BivarColormap, not {item!r}") new_cmap._rgba_bad = self._rgba_bad if self.shape in ['ignore', 'circleignore']: new_cmap.set_over(self._rgba_outside) new_cmap.set_under(self._rgba_outside) return new_cmap def _repr_png_(self): """Generate a PNG representation of the BivarColormap.""" if not self._isinit: self._init() pixels = self.lut if pixels.shape[0] < _BIVAR_REPR_PNG_SIZE: pixels = np.repeat(pixels, repeats=_BIVAR_REPR_PNG_SIZE//pixels.shape[0], axis=0)[:256, :] if pixels.shape[1] < _BIVAR_REPR_PNG_SIZE: pixels = np.repeat(pixels, repeats=_BIVAR_REPR_PNG_SIZE//pixels.shape[1], axis=1)[:, :256] pixels = (pixels[::-1, :, :] * 255).astype(np.uint8) png_bytes = io.BytesIO() title = self.name + ' BivarColormap' author = f'Matplotlib v{mpl.__version__}, https://matplotlib.org' pnginfo = PngInfo() pnginfo.add_text('Title', title) pnginfo.add_text('Description', title) pnginfo.add_text('Author', author) pnginfo.add_text('Software', author) Image.fromarray(pixels).save(png_bytes, format='png', pnginfo=pnginfo) return png_bytes.getvalue() def _repr_html_(self): """Generate an HTML representation of the Colormap.""" png_bytes = self._repr_png_() png_base64 = base64.b64encode(png_bytes).decode('ascii') def color_block(color): hex_color = to_hex(color, keep_alpha=True) return (f'<div title="{hex_color}" ' 'style="display: inline-block; ' 'width: 1em; height: 1em; ' 'margin: 0; ' 'vertical-align: middle; ' 'border: 1px solid #555; ' f'background-color: {hex_color};"></div>') return ('<div style="vertical-align: middle;">' f'<strong>{self.name}</strong> ' '</div>' '<div class="cmap"><img ' f'alt="{self.name} BivarColormap" ' f'title="{self.name}" ' 'style="border: 1px solid #555;" ' f'src="data:image/png;base64,{png_base64}"></div>' '<div style="vertical-align: middle; ' f'max-width: {_BIVAR_REPR_PNG_SIZE+2}px; ' 'display: flex; justify-content: space-between;">' '<div style="float: left;">' f'{color_block(self.get_outside())} outside' '</div>' '<div style="float: right;">' f'bad {color_block(self.get_bad())}' '</div></div>') def copy(self): """Return a copy of the colormap.""" return self.__copy__() class SegmentedBivarColormap(BivarColormap): """ BivarColormap object generated by supersampling a regular grid. Parameters ---------- patch : np.array Patch is required to have a shape (k, l, 3), and will get supersampled to a lut of shape (N, N, 4). N : int The number of RGB quantization levels along each axis. shape : {'square', 'circle', 'ignore', 'circleignore'} - If 'square' each variate is clipped to [0,1] independently - If 'circle' the variates are clipped radially to the center of the colormap, and a circular mask is applied when the colormap is displayed - If 'ignore' the variates are not clipped, but instead assigned the 'outside' color - If 'circleignore' a circular mask is applied, but the data is not clipped origin : (float, float) The relative origin of the colormap. Typically (0, 0), for colormaps that are linear on both axis, and (.5, .5) for circular colormaps. Used when getting 1D colormaps from 2D colormaps. name : str, optional The name of the colormap. """ def __init__(self, patch, N=256, shape='square', origin=(0, 0), name='segmented bivariate colormap'): _api.check_shape((None, None, 3), patch=patch) self.patch = patch super().__init__(N, N, shape, origin, name=name) def _init(self): s = self.patch.shape _patch = np.empty((s[0], s[1], 4)) _patch[:, :, :3] = self.patch _patch[:, :, 3] = 1 transform = mpl.transforms.Affine2D().translate(-0.5, -0.5)\ .scale(self.N / (s[1] - 1), self.N / (s[0] - 1)) self._lut = np.empty((self.N, self.N, 4)) _image.resample(_patch, self._lut, transform, _image.BILINEAR, resample=False, alpha=1) self._isinit = True class BivarColormapFromImage(BivarColormap): """ BivarColormap object generated by supersampling a regular grid. Parameters ---------- lut : nparray of shape (N, M, 3) or (N, M, 4) The look-up-table shape: {'square', 'circle', 'ignore', 'circleignore'} - If 'square' each variate is clipped to [0,1] independently - If 'circle' the variates are clipped radially to the center of the colormap, and a circular mask is applied when the colormap is displayed - If 'ignore' the variates are not clipped, but instead assigned the 'outside' color - If 'circleignore' a circular mask is applied, but the data is not clipped origin: (float, float) The relative origin of the colormap. Typically (0, 0), for colormaps that are linear on both axis, and (.5, .5) for circular colormaps. Used when getting 1D colormaps from 2D colormaps. name : str, optional The name of the colormap. """ def __init__(self, lut, shape='square', origin=(0, 0), name='from image'): # We can allow for a PIL.Image as input in the following way, but importing # matplotlib.image.pil_to_array() results in a circular import # For now, this function only accepts numpy arrays. # i.e.: # if isinstance(Image, lut): # lut = image.pil_to_array(lut) lut = np.array(lut, copy=True) if lut.ndim != 3 or lut.shape[2] not in (3, 4): raise ValueError("The lut must be an array of shape (n, m, 3) or (n, m, 4)", " or a PIL.image encoded as RGB or RGBA") if lut.dtype == np.uint8: lut = lut.astype(np.float32)/255 if lut.shape[2] == 3: new_lut = np.empty((lut.shape[0], lut.shape[1], 4), dtype=lut.dtype) new_lut[:, :, :3] = lut new_lut[:, :, 3] = 1. lut = new_lut self._lut = lut super().__init__(lut.shape[0], lut.shape[1], shape, origin, name=name) def _init(self): self._isinit = True class Normalize: """ A class which, when called, maps values within the interval ``[vmin, vmax]`` linearly to the interval ``[0.0, 1.0]``. The mapping of values outside ``[vmin, vmax]`` depends on *clip*. Examples -------- :: x = [-2, -1, 0, 1, 2] norm = mpl.colors.Normalize(vmin=-1, vmax=1, clip=False) norm(x) # [-0.5, 0., 0.5, 1., 1.5] norm = mpl.colors.Normalize(vmin=-1, vmax=1, clip=True) norm(x) # [0., 0., 0.5, 1., 1.] See Also -------- :ref:`colormapnorms` """ def __init__(self, vmin=None, vmax=None, clip=False): """ Parameters ---------- vmin, vmax : float or None Values within the range ``[vmin, vmax]`` from the input data will be linearly mapped to ``[0, 1]``. If either *vmin* or *vmax* is not provided, they default to the minimum and maximum values of the input, respectively. clip : bool, default: False Determines the behavior for mapping values outside the range ``[vmin, vmax]``. If clipping is off, values outside the range ``[vmin, vmax]`` are also transformed, resulting in values outside ``[0, 1]``. This behavior is usually desirable, as colormaps can mark these *under* and *over* values with specific colors. If clipping is on, values below *vmin* are mapped to 0 and values above *vmax* are mapped to 1. Such values become indistinguishable from regular boundary values, which may cause misinterpretation of the data. Notes ----- If ``vmin == vmax``, input data will be mapped to 0. """ self._vmin = _sanitize_extrema(vmin) self._vmax = _sanitize_extrema(vmax) self._clip = clip self._scale = None self.callbacks = cbook.CallbackRegistry(signals=["changed"]) @property def vmin(self): return self._vmin @vmin.setter def vmin(self, value): value = _sanitize_extrema(value) if value != self._vmin: self._vmin = value self._changed() @property def vmax(self): return self._vmax @vmax.setter def vmax(self, value): value = _sanitize_extrema(value) if value != self._vmax: self._vmax = value self._changed() @property def clip(self): return self._clip @clip.setter def clip(self, value): if value != self._clip: self._clip = value self._changed() def _changed(self): """ Call this whenever the norm is changed to notify all the callback listeners to the 'changed' signal. """ self.callbacks.process('changed') @staticmethod def process_value(value): """ Homogenize the input *value* for easy and efficient normalization. *value* can be a scalar or sequence. Parameters ---------- value Data to normalize. Returns ------- result : masked array Masked array with the same shape as *value*. is_scalar : bool Whether *value* is a scalar. Notes ----- Float dtypes are preserved; integer types with two bytes or smaller are converted to np.float32, and larger types are converted to np.float64. Preserving float32 when possible, and using in-place operations, greatly improves speed for large arrays. """ is_scalar = not np.iterable(value) if is_scalar: value = [value] dtype = np.min_scalar_type(value) if np.issubdtype(dtype, np.integer) or dtype.type is np.bool_: # bool_/int8/int16 -> float32; int32/int64 -> float64 dtype = np.promote_types(dtype, np.float32) # ensure data passed in as an ndarray subclass are interpreted as # an ndarray. See issue #6622. mask = np.ma.getmask(value) data = np.asarray(value) result = np.ma.array(data, mask=mask, dtype=dtype, copy=True) return result, is_scalar def __call__(self, value, clip=None): """ Normalize the data and return the normalized data. Parameters ---------- value Data to normalize. clip : bool, optional See the description of the parameter *clip* in `.Normalize`. If ``None``, defaults to ``self.clip`` (which defaults to ``False``). Notes ----- If not already initialized, ``self.vmin`` and ``self.vmax`` are initialized using ``self.autoscale_None(value)``. """ if clip is None: clip = self.clip result, is_scalar = self.process_value(value) if self.vmin is None or self.vmax is None: self.autoscale_None(result) # Convert at least to float, without losing precision. (vmin,), _ = self.process_value(self.vmin) (vmax,), _ = self.process_value(self.vmax) if vmin == vmax: result.fill(0) # Or should it be all masked? Or 0.5? elif vmin > vmax: raise ValueError("minvalue must be less than or equal to maxvalue") else: if clip: mask = np.ma.getmask(result) result = np.ma.array(np.clip(result.filled(vmax), vmin, vmax), mask=mask) # ma division is very slow; we can take a shortcut resdat = result.data resdat -= vmin resdat /= (vmax - vmin) result = np.ma.array(resdat, mask=result.mask, copy=False) if is_scalar: result = result[0] return result def inverse(self, value): """ Maps the normalized value (i.e., index in the colormap) back to image data value. Parameters ---------- value Normalized value. """ if not self.scaled(): raise ValueError("Not invertible until both vmin and vmax are set") (vmin,), _ = self.process_value(self.vmin) (vmax,), _ = self.process_value(self.vmax) if np.iterable(value): val = np.ma.asarray(value) return vmin + val * (vmax - vmin) else: return vmin + value * (vmax - vmin) def autoscale(self, A): """Set *vmin*, *vmax* to min, max of *A*.""" with self.callbacks.blocked(): # Pause callbacks while we are updating so we only get # a single update signal at the end self.vmin = self.vmax = None self.autoscale_None(A) self._changed() def autoscale_None(self, A): """If *vmin* or *vmax* are not set, use the min/max of *A* to set them.""" A = np.asanyarray(A) if isinstance(A, np.ma.MaskedArray): # we need to make the distinction between an array, False, np.bool_(False) if A.mask is False or not A.mask.shape: A = A.data if self.vmin is None and A.size: self.vmin = A.min() if self.vmax is None and A.size: self.vmax = A.max() def scaled(self): """Return whether *vmin* and *vmax* are both set.""" return self.vmin is not None and self.vmax is not None class TwoSlopeNorm(Normalize): def __init__(self, vcenter, vmin=None, vmax=None): """ Normalize data with a set center. Useful when mapping data with an unequal rates of change around a conceptual center, e.g., data that range from -2 to 4, with 0 as the midpoint. Parameters ---------- vcenter : float The data value that defines ``0.5`` in the normalization. vmin : float, optional The data value that defines ``0.0`` in the normalization. Defaults to the min value of the dataset. vmax : float, optional The data value that defines ``1.0`` in the normalization. Defaults to the max value of the dataset. Examples -------- This maps data value -4000 to 0., 0 to 0.5, and +10000 to 1.0; data between is linearly interpolated:: >>> import matplotlib.colors as mcolors >>> offset = mcolors.TwoSlopeNorm(vmin=-4000., ... vcenter=0., vmax=10000) >>> data = [-4000., -2000., 0., 2500., 5000., 7500., 10000.] >>> offset(data) array([0., 0.25, 0.5, 0.625, 0.75, 0.875, 1.0]) """ super().__init__(vmin=vmin, vmax=vmax) self._vcenter = vcenter if vcenter is not None and vmax is not None and vcenter >= vmax: raise ValueError('vmin, vcenter, and vmax must be in ' 'ascending order') if vcenter is not None and vmin is not None and vcenter <= vmin: raise ValueError('vmin, vcenter, and vmax must be in ' 'ascending order') @property def vcenter(self): return self._vcenter @vcenter.setter def vcenter(self, value): if value != self._vcenter: self._vcenter = value self._changed() def autoscale_None(self, A): """ Get vmin and vmax. If vcenter isn't in the range [vmin, vmax], either vmin or vmax is expanded so that vcenter lies in the middle of the modified range [vmin, vmax]. """ super().autoscale_None(A) if self.vmin >= self.vcenter: self.vmin = self.vcenter - (self.vmax - self.vcenter) if self.vmax <= self.vcenter: self.vmax = self.vcenter + (self.vcenter - self.vmin) def __call__(self, value, clip=None): """ Map value to the interval [0, 1]. The *clip* argument is unused. """ result, is_scalar = self.process_value(value) self.autoscale_None(result) # sets self.vmin, self.vmax if None if not self.vmin <= self.vcenter <= self.vmax: raise ValueError("vmin, vcenter, vmax must increase monotonically") # note that we must extrapolate for tick locators: result = np.ma.masked_array( np.interp(result, [self.vmin, self.vcenter, self.vmax], [0, 0.5, 1], left=-np.inf, right=np.inf), mask=np.ma.getmask(result)) if is_scalar: result = np.atleast_1d(result)[0] return result def inverse(self, value): if not self.scaled(): raise ValueError("Not invertible until both vmin and vmax are set") (vmin,), _ = self.process_value(self.vmin) (vmax,), _ = self.process_value(self.vmax) (vcenter,), _ = self.process_value(self.vcenter) result = np.interp(value, [0, 0.5, 1], [vmin, vcenter, vmax], left=-np.inf, right=np.inf) return result class CenteredNorm(Normalize): def __init__(self, vcenter=0, halfrange=None, clip=False): """ Normalize symmetrical data around a center (0 by default). Unlike `TwoSlopeNorm`, `CenteredNorm` applies an equal rate of change around the center. Useful when mapping symmetrical data around a conceptual center e.g., data that range from -2 to 4, with 0 as the midpoint, and with equal rates of change around that midpoint. Parameters ---------- vcenter : float, default: 0 The data value that defines ``0.5`` in the normalization. halfrange : float, optional The range of data values that defines a range of ``0.5`` in the normalization, so that *vcenter* - *halfrange* is ``0.0`` and *vcenter* + *halfrange* is ``1.0`` in the normalization. Defaults to the largest absolute difference to *vcenter* for the values in the dataset. clip : bool, default: False Determines the behavior for mapping values outside the range ``[vmin, vmax]``. If clipping is off, values outside the range ``[vmin, vmax]`` are also transformed, resulting in values outside ``[0, 1]``. This behavior is usually desirable, as colormaps can mark these *under* and *over* values with specific colors. If clipping is on, values below *vmin* are mapped to 0 and values above *vmax* are mapped to 1. Such values become indistinguishable from regular boundary values, which may cause misinterpretation of the data. Examples -------- This maps data values -2 to 0.25, 0 to 0.5, and 4 to 1.0 (assuming equal rates of change above and below 0.0): >>> import matplotlib.colors as mcolors >>> norm = mcolors.CenteredNorm(halfrange=4.0) >>> data = [-2., 0., 4.] >>> norm(data) array([0.25, 0.5 , 1. ]) """ super().__init__(vmin=None, vmax=None, clip=clip) self._vcenter = vcenter # calling the halfrange setter to set vmin and vmax self.halfrange = halfrange def autoscale(self, A): """ Set *halfrange* to ``max(abs(A-vcenter))``, then set *vmin* and *vmax*. """ A = np.asanyarray(A) self.halfrange = max(self._vcenter-A.min(), A.max()-self._vcenter) def autoscale_None(self, A): """Set *vmin* and *vmax*.""" A = np.asanyarray(A) if self.halfrange is None and A.size: self.autoscale(A) @property def vmin(self): return self._vmin @vmin.setter def vmin(self, value): value = _sanitize_extrema(value) if value != self._vmin: self._vmin = value self._vmax = 2*self.vcenter - value self._changed() @property def vmax(self): return self._vmax @vmax.setter def vmax(self, value): value = _sanitize_extrema(value) if value != self._vmax: self._vmax = value self._vmin = 2*self.vcenter - value self._changed() @property def vcenter(self): return self._vcenter @vcenter.setter def vcenter(self, vcenter): if vcenter != self._vcenter: self._vcenter = vcenter # Trigger an update of the vmin/vmax values through the setter self.halfrange = self.halfrange self._changed() @property def halfrange(self): if self.vmin is None or self.vmax is None: return None return (self.vmax - self.vmin) / 2 @halfrange.setter def halfrange(self, halfrange): if halfrange is None: self.vmin = None self.vmax = None else: self.vmin = self.vcenter - abs(halfrange) self.vmax = self.vcenter + abs(halfrange) def make_norm_from_scale(scale_cls, base_norm_cls=None, *, init=None): """ Decorator for building a `.Normalize` subclass from a `~.scale.ScaleBase` subclass. After :: @make_norm_from_scale(scale_cls) class norm_cls(Normalize): ... *norm_cls* is filled with methods so that normalization computations are forwarded to *scale_cls* (i.e., *scale_cls* is the scale that would be used for the colorbar of a mappable normalized with *norm_cls*). If *init* is not passed, then the constructor signature of *norm_cls* will be ``norm_cls(vmin=None, vmax=None, clip=False)``; these three parameters will be forwarded to the base class (``Normalize.__init__``), and a *scale_cls* object will be initialized with no arguments (other than a dummy axis). If the *scale_cls* constructor takes additional parameters, then *init* should be passed to `make_norm_from_scale`. It is a callable which is *only* used for its signature. First, this signature will become the signature of *norm_cls*. Second, the *norm_cls* constructor will bind the parameters passed to it using this signature, extract the bound *vmin*, *vmax*, and *clip* values, pass those to ``Normalize.__init__``, and forward the remaining bound values (including any defaults defined by the signature) to the *scale_cls* constructor. """ if base_norm_cls is None: return functools.partial(make_norm_from_scale, scale_cls, init=init) if isinstance(scale_cls, functools.partial): scale_args = scale_cls.args scale_kwargs_items = tuple(scale_cls.keywords.items()) scale_cls = scale_cls.func else: scale_args = scale_kwargs_items = () if init is None: def init(vmin=None, vmax=None, clip=False): pass return _make_norm_from_scale( scale_cls, scale_args, scale_kwargs_items, base_norm_cls, inspect.signature(init)) @functools.cache def _make_norm_from_scale( scale_cls, scale_args, scale_kwargs_items, base_norm_cls, bound_init_signature, ): """ Helper for `make_norm_from_scale`. This function is split out to enable caching (in particular so that different unpickles reuse the same class). In order to do so, - ``functools.partial`` *scale_cls* is expanded into ``func, args, kwargs`` to allow memoizing returned norms (partial instances always compare unequal, but we can check identity based on ``func, args, kwargs``; - *init* is replaced by *init_signature*, as signatures are picklable, unlike to arbitrary lambdas. """ class Norm(base_norm_cls): def __reduce__(self): cls = type(self) # If the class is toplevel-accessible, it is possible to directly # pickle it "by name". This is required to support norm classes # defined at a module's toplevel, as the inner base_norm_cls is # otherwise unpicklable (as it gets shadowed by the generated norm # class). If either import or attribute access fails, fall back to # the general path. try: if cls is getattr(importlib.import_module(cls.__module__), cls.__qualname__): return (_create_empty_object_of_class, (cls,), vars(self)) except (ImportError, AttributeError): pass return (_picklable_norm_constructor, (scale_cls, scale_args, scale_kwargs_items, base_norm_cls, bound_init_signature), vars(self)) def __init__(self, *args, **kwargs): ba = bound_init_signature.bind(*args, **kwargs) ba.apply_defaults() super().__init__( **{k: ba.arguments.pop(k) for k in ["vmin", "vmax", "clip"]}) self._scale = functools.partial( scale_cls, *scale_args, **dict(scale_kwargs_items))( axis=None, **ba.arguments) self._trf = self._scale.get_transform() __init__.__signature__ = bound_init_signature.replace(parameters=[ inspect.Parameter("self", inspect.Parameter.POSITIONAL_OR_KEYWORD), *bound_init_signature.parameters.values()]) def __call__(self, value, clip=None): value, is_scalar = self.process_value(value) if self.vmin is None or self.vmax is None: self.autoscale_None(value) if self.vmin > self.vmax: raise ValueError("vmin must be less or equal to vmax") if self.vmin == self.vmax: return np.full_like(value, 0) if clip is None: clip = self.clip if clip: value = np.clip(value, self.vmin, self.vmax) t_value = self._trf.transform(value).reshape(np.shape(value)) t_vmin, t_vmax = self._trf.transform([self.vmin, self.vmax]) if not np.isfinite([t_vmin, t_vmax]).all(): raise ValueError("Invalid vmin or vmax") t_value -= t_vmin t_value /= (t_vmax - t_vmin) t_value = np.ma.masked_invalid(t_value, copy=False) return t_value[0] if is_scalar else t_value def inverse(self, value): if not self.scaled(): raise ValueError("Not invertible until scaled") if self.vmin > self.vmax: raise ValueError("vmin must be less or equal to vmax") t_vmin, t_vmax = self._trf.transform([self.vmin, self.vmax]) if not np.isfinite([t_vmin, t_vmax]).all(): raise ValueError("Invalid vmin or vmax") value, is_scalar = self.process_value(value) rescaled = value * (t_vmax - t_vmin) rescaled += t_vmin value = (self._trf .inverted() .transform(rescaled) .reshape(np.shape(value))) return value[0] if is_scalar else value def autoscale_None(self, A): # i.e. A[np.isfinite(...)], but also for non-array A's in_trf_domain = np.extract(np.isfinite(self._trf.transform(A)), A) if in_trf_domain.size == 0: in_trf_domain = np.ma.masked return super().autoscale_None(in_trf_domain) if base_norm_cls is Normalize: Norm.__name__ = f"{scale_cls.__name__}Norm" Norm.__qualname__ = f"{scale_cls.__qualname__}Norm" else: Norm.__name__ = base_norm_cls.__name__ Norm.__qualname__ = base_norm_cls.__qualname__ Norm.__module__ = base_norm_cls.__module__ Norm.__doc__ = base_norm_cls.__doc__ return Norm def _create_empty_object_of_class(cls): return cls.__new__(cls) def _picklable_norm_constructor(*args): return _create_empty_object_of_class(_make_norm_from_scale(*args)) @make_norm_from_scale( scale.FuncScale, init=lambda functions, vmin=None, vmax=None, clip=False: None) class FuncNorm(Normalize): """ Arbitrary normalization using functions for the forward and inverse. Parameters ---------- functions : (callable, callable) two-tuple of the forward and inverse functions for the normalization. The forward function must be monotonic. Both functions must have the signature :: def forward(values: array-like) -> array-like vmin, vmax : float or None If *vmin* and/or *vmax* is not given, they are initialized from the minimum and maximum value, respectively, of the first input processed; i.e., ``__call__(A)`` calls ``autoscale_None(A)``. clip : bool, default: False Determines the behavior for mapping values outside the range ``[vmin, vmax]``. If clipping is off, values outside the range ``[vmin, vmax]`` are also transformed by the function, resulting in values outside ``[0, 1]``. This behavior is usually desirable, as colormaps can mark these *under* and *over* values with specific colors. If clipping is on, values below *vmin* are mapped to 0 and values above *vmax* are mapped to 1. Such values become indistinguishable from regular boundary values, which may cause misinterpretation of the data. """ LogNorm = make_norm_from_scale( functools.partial(scale.LogScale, nonpositive="mask"))(Normalize) LogNorm.__name__ = LogNorm.__qualname__ = "LogNorm" LogNorm.__doc__ = "Normalize a given value to the 0-1 range on a log scale." @make_norm_from_scale( scale.SymmetricalLogScale, init=lambda linthresh, linscale=1., vmin=None, vmax=None, clip=False, *, base=10: None) class SymLogNorm(Normalize): """ The symmetrical logarithmic scale is logarithmic in both the positive and negative directions from the origin. Since the values close to zero tend toward infinity, there is a need to have a range around zero that is linear. The parameter *linthresh* allows the user to specify the size of this range (-*linthresh*, *linthresh*). Parameters ---------- linthresh : float The range within which the plot is linear (to avoid having the plot go to infinity around zero). linscale : float, default: 1 This allows the linear range (-*linthresh* to *linthresh*) to be stretched relative to the logarithmic range. Its value is the number of decades to use for each half of the linear range. For example, when *linscale* == 1.0 (the default), the space used for the positive and negative halves of the linear range will be equal to one decade in the logarithmic range. base : float, default: 10 """ @property def linthresh(self): return self._scale.linthresh @linthresh.setter def linthresh(self, value): self._scale.linthresh = value @make_norm_from_scale( scale.AsinhScale, init=lambda linear_width=1, vmin=None, vmax=None, clip=False: None) class AsinhNorm(Normalize): """ The inverse hyperbolic sine scale is approximately linear near the origin, but becomes logarithmic for larger positive or negative values. Unlike the `SymLogNorm`, the transition between these linear and logarithmic regions is smooth, which may reduce the risk of visual artifacts. .. note:: This API is provisional and may be revised in the future based on early user feedback. Parameters ---------- linear_width : float, default: 1 The effective width of the linear region, beyond which the transformation becomes asymptotically logarithmic """ @property def linear_width(self): return self._scale.linear_width @linear_width.setter def linear_width(self, value): self._scale.linear_width = value class PowerNorm(Normalize): r""" Linearly map a given value to the 0-1 range and then apply a power-law normalization over that range. Parameters ---------- gamma : float Power law exponent. vmin, vmax : float or None If *vmin* and/or *vmax* is not given, they are initialized from the minimum and maximum value, respectively, of the first input processed; i.e., ``__call__(A)`` calls ``autoscale_None(A)``. clip : bool, default: False Determines the behavior for mapping values outside the range ``[vmin, vmax]``. If clipping is off, values above *vmax* are transformed by the power function, resulting in values above 1, and values below *vmin* are linearly transformed resulting in values below 0. This behavior is usually desirable, as colormaps can mark these *under* and *over* values with specific colors. If clipping is on, values below *vmin* are mapped to 0 and values above *vmax* are mapped to 1. Such values become indistinguishable from regular boundary values, which may cause misinterpretation of the data. Notes ----- The normalization formula is .. math:: \left ( \frac{x - v_{min}}{v_{max} - v_{min}} \right )^{\gamma} For input values below *vmin*, gamma is set to one. """ def __init__(self, gamma, vmin=None, vmax=None, clip=False): super().__init__(vmin, vmax, clip) self.gamma = gamma def __call__(self, value, clip=None): if clip is None: clip = self.clip result, is_scalar = self.process_value(value) self.autoscale_None(result) gamma = self.gamma vmin, vmax = self.vmin, self.vmax if vmin > vmax: raise ValueError("minvalue must be less than or equal to maxvalue") elif vmin == vmax: result.fill(0) else: if clip: mask = np.ma.getmask(result) result = np.ma.array(np.clip(result.filled(vmax), vmin, vmax), mask=mask) resdat = result.data resdat -= vmin resdat /= (vmax - vmin) resdat[resdat > 0] = np.power(resdat[resdat > 0], gamma) result = np.ma.array(resdat, mask=result.mask, copy=False) if is_scalar: result = result[0] return result def inverse(self, value): if not self.scaled(): raise ValueError("Not invertible until scaled") result, is_scalar = self.process_value(value) gamma = self.gamma vmin, vmax = self.vmin, self.vmax resdat = result.data resdat[resdat > 0] = np.power(resdat[resdat > 0], 1 / gamma) resdat *= (vmax - vmin) resdat += vmin result = np.ma.array(resdat, mask=result.mask, copy=False) if is_scalar: result = result[0] return result class BoundaryNorm(Normalize): """ Generate a colormap index based on discrete intervals. Unlike `Normalize` or `LogNorm`, `BoundaryNorm` maps values to integers instead of to the interval 0-1. """ # Mapping to the 0-1 interval could have been done via piece-wise linear # interpolation, but using integers seems simpler, and reduces the number # of conversions back and forth between int and float. def __init__(self, boundaries, ncolors, clip=False, *, extend='neither'): """ Parameters ---------- boundaries : array-like Monotonically increasing sequence of at least 2 bin edges: data falling in the n-th bin will be mapped to the n-th color. ncolors : int Number of colors in the colormap to be used. clip : bool, optional If clip is ``True``, out of range values are mapped to 0 if they are below ``boundaries[0]`` or mapped to ``ncolors - 1`` if they are above ``boundaries[-1]``. If clip is ``False``, out of range values are mapped to -1 if they are below ``boundaries[0]`` or mapped to *ncolors* if they are above ``boundaries[-1]``. These are then converted to valid indices by `Colormap.__call__`. extend : {'neither', 'both', 'min', 'max'}, default: 'neither' Extend the number of bins to include one or both of the regions beyond the boundaries. For example, if ``extend`` is 'min', then the color to which the region between the first pair of boundaries is mapped will be distinct from the first color in the colormap, and by default a `~matplotlib.colorbar.Colorbar` will be drawn with the triangle extension on the left or lower end. Notes ----- If there are fewer bins (including extensions) than colors, then the color index is chosen by linearly interpolating the ``[0, nbins - 1]`` range onto the ``[0, ncolors - 1]`` range, effectively skipping some colors in the middle of the colormap. """ if clip and extend != 'neither': raise ValueError("'clip=True' is not compatible with 'extend'") super().__init__(vmin=boundaries[0], vmax=boundaries[-1], clip=clip) self.boundaries = np.asarray(boundaries) self.N = len(self.boundaries) if self.N < 2: raise ValueError("You must provide at least 2 boundaries " f"(1 region) but you passed in {boundaries!r}") self.Ncmap = ncolors self.extend = extend self._scale = None # don't use the default scale. self._n_regions = self.N - 1 # number of colors needed self._offset = 0 if extend in ('min', 'both'): self._n_regions += 1 self._offset = 1 if extend in ('max', 'both'): self._n_regions += 1 if self._n_regions > self.Ncmap: raise ValueError(f"There are {self._n_regions} color bins " "including extensions, but ncolors = " f"{ncolors}; ncolors must equal or exceed the " "number of bins") def __call__(self, value, clip=None): """ This method behaves similarly to `.Normalize.__call__`, except that it returns integers or arrays of int16. """ if clip is None: clip = self.clip xx, is_scalar = self.process_value(value) mask = np.ma.getmaskarray(xx) # Fill masked values a value above the upper boundary xx = np.atleast_1d(xx.filled(self.vmax + 1)) if clip: np.clip(xx, self.vmin, self.vmax, out=xx) max_col = self.Ncmap - 1 else: max_col = self.Ncmap # this gives us the bins in the lookup table in the range # [0, _n_regions - 1] (the offset is set in the init) iret = np.digitize(xx, self.boundaries) - 1 + self._offset # if we have more colors than regions, stretch the region # index computed above to full range of the color bins. This # will make use of the full range (but skip some of the colors # in the middle) such that the first region is mapped to the # first color and the last region is mapped to the last color. if self.Ncmap > self._n_regions: if self._n_regions == 1: # special case the 1 region case, pick the middle color iret[iret == 0] = (self.Ncmap - 1) // 2 else: # otherwise linearly remap the values from the region index # to the color index spaces iret = (self.Ncmap - 1) / (self._n_regions - 1) * iret # cast to 16bit integers in all cases iret = iret.astype(np.int16) iret[xx < self.vmin] = -1 iret[xx >= self.vmax] = max_col ret = np.ma.array(iret, mask=mask) if is_scalar: ret = int(ret[0]) # assume python scalar return ret def inverse(self, value): """ Raises ------ ValueError BoundaryNorm is not invertible, so calling this method will always raise an error """ raise ValueError("BoundaryNorm is not invertible") class NoNorm(Normalize): """ Dummy replacement for `Normalize`, for the case where we want to use indices directly in a `~matplotlib.cm.ScalarMappable`. """ def __call__(self, value, clip=None): if np.iterable(value): return np.ma.array(value) return value def inverse(self, value): if np.iterable(value): return np.ma.array(value) return value def rgb_to_hsv(arr): """ Convert an array of float RGB values (in the range [0, 1]) to HSV values. Parameters ---------- arr : (..., 3) array-like All values must be in the range [0, 1] Returns ------- (..., 3) `~numpy.ndarray` Colors converted to HSV values in range [0, 1] """ arr = np.asarray(arr) # check length of the last dimension, should be _some_ sort of rgb if arr.shape[-1] != 3: raise ValueError("Last dimension of input array must be 3; " f"shape {arr.shape} was found.") in_shape = arr.shape arr = np.array( arr, copy=False, dtype=np.promote_types(arr.dtype, np.float32), # Don't work on ints. ndmin=2, # In case input was 1D. ) out = np.zeros_like(arr) arr_max = arr.max(-1) # Check if input is in the expected range if np.any(arr_max > 1): raise ValueError( "Input array must be in the range [0, 1]. " f"Found a maximum value of {arr_max.max()}" ) if arr.min() < 0: raise ValueError( "Input array must be in the range [0, 1]. " f"Found a minimum value of {arr.min()}" ) ipos = arr_max > 0 delta = np.ptp(arr, -1) s = np.zeros_like(delta) s[ipos] = delta[ipos] / arr_max[ipos] ipos = delta > 0 # red is max idx = (arr[..., 0] == arr_max) & ipos out[idx, 0] = (arr[idx, 1] - arr[idx, 2]) / delta[idx] # green is max idx = (arr[..., 1] == arr_max) & ipos out[idx, 0] = 2. + (arr[idx, 2] - arr[idx, 0]) / delta[idx] # blue is max idx = (arr[..., 2] == arr_max) & ipos out[idx, 0] = 4. + (arr[idx, 0] - arr[idx, 1]) / delta[idx] out[..., 0] = (out[..., 0] / 6.0) % 1.0 out[..., 1] = s out[..., 2] = arr_max return out.reshape(in_shape) def hsv_to_rgb(hsv): """ Convert HSV values to RGB. Parameters ---------- hsv : (..., 3) array-like All values assumed to be in range [0, 1] Returns ------- (..., 3) `~numpy.ndarray` Colors converted to RGB values in range [0, 1] """ hsv = np.asarray(hsv) # check length of the last dimension, should be _some_ sort of rgb if hsv.shape[-1] != 3: raise ValueError("Last dimension of input array must be 3; " f"shape {hsv.shape} was found.") in_shape = hsv.shape hsv = np.array( hsv, copy=False, dtype=np.promote_types(hsv.dtype, np.float32), # Don't work on ints. ndmin=2, # In case input was 1D. ) h = hsv[..., 0] s = hsv[..., 1] v = hsv[..., 2] r = np.empty_like(h) g = np.empty_like(h) b = np.empty_like(h) i = (h * 6.0).astype(int) f = (h * 6.0) - i p = v * (1.0 - s) q = v * (1.0 - s * f) t = v * (1.0 - s * (1.0 - f)) idx = i % 6 == 0 r[idx] = v[idx] g[idx] = t[idx] b[idx] = p[idx] idx = i == 1 r[idx] = q[idx] g[idx] = v[idx] b[idx] = p[idx] idx = i == 2 r[idx] = p[idx] g[idx] = v[idx] b[idx] = t[idx] idx = i == 3 r[idx] = p[idx] g[idx] = q[idx] b[idx] = v[idx] idx = i == 4 r[idx] = t[idx] g[idx] = p[idx] b[idx] = v[idx] idx = i == 5 r[idx] = v[idx] g[idx] = p[idx] b[idx] = q[idx] idx = s == 0 r[idx] = v[idx] g[idx] = v[idx] b[idx] = v[idx] rgb = np.stack([r, g, b], axis=-1) return rgb.reshape(in_shape) def _vector_magnitude(arr): # things that don't work here: # * np.linalg.norm: drops mask from ma.array # * np.sum: drops mask from ma.array unless entire vector is masked sum_sq = 0 for i in range(arr.shape[-1]): sum_sq += arr[..., i, np.newaxis] ** 2 return np.sqrt(sum_sq) class LightSource: """ Create a light source coming from the specified azimuth and elevation. Angles are in degrees, with the azimuth measured clockwise from north and elevation up from the zero plane of the surface. `shade` is used to produce "shaded" RGB values for a data array. `shade_rgb` can be used to combine an RGB image with an elevation map. `hillshade` produces an illumination map of a surface. """ def __init__(self, azdeg=315, altdeg=45, hsv_min_val=0, hsv_max_val=1, hsv_min_sat=1, hsv_max_sat=0): """ Specify the azimuth (measured clockwise from south) and altitude (measured up from the plane of the surface) of the light source in degrees. Parameters ---------- azdeg : float, default: 315 degrees (from the northwest) The azimuth (0-360, degrees clockwise from North) of the light source. altdeg : float, default: 45 degrees The altitude (0-90, degrees up from horizontal) of the light source. hsv_min_val : number, default: 0 The minimum value ("v" in "hsv") that the *intensity* map can shift the output image to. hsv_max_val : number, default: 1 The maximum value ("v" in "hsv") that the *intensity* map can shift the output image to. hsv_min_sat : number, default: 1 The minimum saturation value that the *intensity* map can shift the output image to. hsv_max_sat : number, default: 0 The maximum saturation value that the *intensity* map can shift the output image to. Notes ----- For backwards compatibility, the parameters *hsv_min_val*, *hsv_max_val*, *hsv_min_sat*, and *hsv_max_sat* may be supplied at initialization as well. However, these parameters will only be used if "blend_mode='hsv'" is passed into `shade` or `shade_rgb`. See the documentation for `blend_hsv` for more details. """ self.azdeg = azdeg self.altdeg = altdeg self.hsv_min_val = hsv_min_val self.hsv_max_val = hsv_max_val self.hsv_min_sat = hsv_min_sat self.hsv_max_sat = hsv_max_sat @property def direction(self): """The unit vector direction towards the light source.""" # Azimuth is in degrees clockwise from North. Convert to radians # counterclockwise from East (mathematical notation). az = np.radians(90 - self.azdeg) alt = np.radians(self.altdeg) return np.array([ np.cos(az) * np.cos(alt), np.sin(az) * np.cos(alt), np.sin(alt) ]) def hillshade(self, elevation, vert_exag=1, dx=1, dy=1, fraction=1.): """ Calculate the illumination intensity for a surface using the defined azimuth and elevation for the light source. This computes the normal vectors for the surface, and then passes them on to `shade_normals` Parameters ---------- elevation : 2D array-like The height values used to generate an illumination map vert_exag : number, optional The amount to exaggerate the elevation values by when calculating illumination. This can be used either to correct for differences in units between the x-y coordinate system and the elevation coordinate system (e.g. decimal degrees vs. meters) or to exaggerate or de-emphasize topographic effects. dx : number, optional The x-spacing (columns) of the input *elevation* grid. dy : number, optional The y-spacing (rows) of the input *elevation* grid. fraction : number, optional Increases or decreases the contrast of the hillshade. Values greater than one will cause intermediate values to move closer to full illumination or shadow (and clipping any values that move beyond 0 or 1). Note that this is not visually or mathematically the same as vertical exaggeration. Returns ------- `~numpy.ndarray` A 2D array of illumination values between 0-1, where 0 is completely in shadow and 1 is completely illuminated. """ # Because most image and raster GIS data has the first row in the array # as the "top" of the image, dy is implicitly negative. This is # consistent to what `imshow` assumes, as well. dy = -dy # compute the normal vectors from the partial derivatives e_dy, e_dx = np.gradient(vert_exag * elevation, dy, dx) # .view is to keep subclasses normal = np.empty(elevation.shape + (3,)).view(type(elevation)) normal[..., 0] = -e_dx normal[..., 1] = -e_dy normal[..., 2] = 1 normal /= _vector_magnitude(normal) return self.shade_normals(normal, fraction) def shade_normals(self, normals, fraction=1.): """ Calculate the illumination intensity for the normal vectors of a surface using the defined azimuth and elevation for the light source. Imagine an artificial sun placed at infinity in some azimuth and elevation position illuminating our surface. The parts of the surface that slope toward the sun should brighten while those sides facing away should become darker. Parameters ---------- fraction : number, optional Increases or decreases the contrast of the hillshade. Values greater than one will cause intermediate values to move closer to full illumination or shadow (and clipping any values that move beyond 0 or 1). Note that this is not visually or mathematically the same as vertical exaggeration. Returns ------- `~numpy.ndarray` A 2D array of illumination values between 0-1, where 0 is completely in shadow and 1 is completely illuminated. """ intensity = normals.dot(self.direction) # Apply contrast stretch imin, imax = intensity.min(), intensity.max() intensity *= fraction # Rescale to 0-1, keeping range before contrast stretch # If constant slope, keep relative scaling (i.e. flat should be 0.5, # fully occluded 0, etc.) if (imax - imin) > 1e-6: # Strictly speaking, this is incorrect. Negative values should be # clipped to 0 because they're fully occluded. However, rescaling # in this manner is consistent with the previous implementation and # visually appears better than a "hard" clip. intensity -= imin intensity /= (imax - imin) intensity = np.clip(intensity, 0, 1) return intensity def shade(self, data, cmap, norm=None, blend_mode='overlay', vmin=None, vmax=None, vert_exag=1, dx=1, dy=1, fraction=1, **kwargs): """ Combine colormapped data values with an illumination intensity map (a.k.a. "hillshade") of the values. Parameters ---------- data : 2D array-like The height values used to generate a shaded map. cmap : `~matplotlib.colors.Colormap` The colormap used to color the *data* array. Note that this must be a `~matplotlib.colors.Colormap` instance. For example, rather than passing in ``cmap='gist_earth'``, use ``cmap=plt.get_cmap('gist_earth')`` instead. norm : `~matplotlib.colors.Normalize` instance, optional The normalization used to scale values before colormapping. If None, the input will be linearly scaled between its min and max. blend_mode : {'hsv', 'overlay', 'soft'} or callable, optional The type of blending used to combine the colormapped data values with the illumination intensity. Default is "overlay". Note that for most topographic surfaces, "overlay" or "soft" appear more visually realistic. If a user-defined function is supplied, it is expected to combine an (M, N, 3) RGB array of floats (ranging 0 to 1) with an (M, N, 1) hillshade array (also 0 to 1). (Call signature ``func(rgb, illum, **kwargs)``) Additional kwargs supplied to this function will be passed on to the *blend_mode* function. vmin : float or None, optional The minimum value used in colormapping *data*. If *None* the minimum value in *data* is used. If *norm* is specified, then this argument will be ignored. vmax : float or None, optional The maximum value used in colormapping *data*. If *None* the maximum value in *data* is used. If *norm* is specified, then this argument will be ignored. vert_exag : number, optional The amount to exaggerate the elevation values by when calculating illumination. This can be used either to correct for differences in units between the x-y coordinate system and the elevation coordinate system (e.g. decimal degrees vs. meters) or to exaggerate or de-emphasize topography. dx : number, optional The x-spacing (columns) of the input *elevation* grid. dy : number, optional The y-spacing (rows) of the input *elevation* grid. fraction : number, optional Increases or decreases the contrast of the hillshade. Values greater than one will cause intermediate values to move closer to full illumination or shadow (and clipping any values that move beyond 0 or 1). Note that this is not visually or mathematically the same as vertical exaggeration. **kwargs Additional kwargs are passed on to the *blend_mode* function. Returns ------- `~numpy.ndarray` An (M, N, 4) array of floats ranging between 0-1. """ if vmin is None: vmin = data.min() if vmax is None: vmax = data.max() if norm is None: norm = Normalize(vmin=vmin, vmax=vmax) rgb0 = cmap(norm(data)) rgb1 = self.shade_rgb(rgb0, elevation=data, blend_mode=blend_mode, vert_exag=vert_exag, dx=dx, dy=dy, fraction=fraction, **kwargs) # Don't overwrite the alpha channel, if present. rgb0[..., :3] = rgb1[..., :3] return rgb0 def shade_rgb(self, rgb, elevation, fraction=1., blend_mode='hsv', vert_exag=1, dx=1, dy=1, **kwargs): """ Use this light source to adjust the colors of the *rgb* input array to give the impression of a shaded relief map with the given *elevation*. Parameters ---------- rgb : array-like An (M, N, 3) RGB array, assumed to be in the range of 0 to 1. elevation : array-like An (M, N) array of the height values used to generate a shaded map. fraction : number Increases or decreases the contrast of the hillshade. Values greater than one will cause intermediate values to move closer to full illumination or shadow (and clipping any values that move beyond 0 or 1). Note that this is not visually or mathematically the same as vertical exaggeration. blend_mode : {'hsv', 'overlay', 'soft'} or callable, optional The type of blending used to combine the colormapped data values with the illumination intensity. For backwards compatibility, this defaults to "hsv". Note that for most topographic surfaces, "overlay" or "soft" appear more visually realistic. If a user-defined function is supplied, it is expected to combine an (M, N, 3) RGB array of floats (ranging 0 to 1) with an (M, N, 1) hillshade array (also 0 to 1). (Call signature ``func(rgb, illum, **kwargs)``) Additional kwargs supplied to this function will be passed on to the *blend_mode* function. vert_exag : number, optional The amount to exaggerate the elevation values by when calculating illumination. This can be used either to correct for differences in units between the x-y coordinate system and the elevation coordinate system (e.g. decimal degrees vs. meters) or to exaggerate or de-emphasize topography. dx : number, optional The x-spacing (columns) of the input *elevation* grid. dy : number, optional The y-spacing (rows) of the input *elevation* grid. **kwargs Additional kwargs are passed on to the *blend_mode* function. Returns ------- `~numpy.ndarray` An (m, n, 3) array of floats ranging between 0-1. """ # Calculate the "hillshade" intensity. intensity = self.hillshade(elevation, vert_exag, dx, dy, fraction) intensity = intensity[..., np.newaxis] # Blend the hillshade and rgb data using the specified mode lookup = { 'hsv': self.blend_hsv, 'soft': self.blend_soft_light, 'overlay': self.blend_overlay, } if blend_mode in lookup: blend = lookup[blend_mode](rgb, intensity, **kwargs) else: try: blend = blend_mode(rgb, intensity, **kwargs) except TypeError as err: raise ValueError('"blend_mode" must be callable or one of ' f'{lookup.keys}') from err # Only apply result where hillshade intensity isn't masked if np.ma.is_masked(intensity): mask = intensity.mask[..., 0] for i in range(3): blend[..., i][mask] = rgb[..., i][mask] return blend def blend_hsv(self, rgb, intensity, hsv_max_sat=None, hsv_max_val=None, hsv_min_val=None, hsv_min_sat=None): """ Take the input data array, convert to HSV values in the given colormap, then adjust those color values to give the impression of a shaded relief map with a specified light source. RGBA values are returned, which can then be used to plot the shaded image with imshow. The color of the resulting image will be darkened by moving the (s, v) values (in HSV colorspace) toward (hsv_min_sat, hsv_min_val) in the shaded regions, or lightened by sliding (s, v) toward (hsv_max_sat, hsv_max_val) in regions that are illuminated. The default extremes are chose so that completely shaded points are nearly black (s = 1, v = 0) and completely illuminated points are nearly white (s = 0, v = 1). Parameters ---------- rgb : `~numpy.ndarray` An (M, N, 3) RGB array of floats ranging from 0 to 1 (color image). intensity : `~numpy.ndarray` An (M, N, 1) array of floats ranging from 0 to 1 (grayscale image). hsv_max_sat : number, optional The maximum saturation value that the *intensity* map can shift the output image to. If not provided, use the value provided upon initialization. hsv_min_sat : number, optional The minimum saturation value that the *intensity* map can shift the output image to. If not provided, use the value provided upon initialization. hsv_max_val : number, optional The maximum value ("v" in "hsv") that the *intensity* map can shift the output image to. If not provided, use the value provided upon initialization. hsv_min_val : number, optional The minimum value ("v" in "hsv") that the *intensity* map can shift the output image to. If not provided, use the value provided upon initialization. Returns ------- `~numpy.ndarray` An (M, N, 3) RGB array representing the combined images. """ # Backward compatibility... if hsv_max_sat is None: hsv_max_sat = self.hsv_max_sat if hsv_max_val is None: hsv_max_val = self.hsv_max_val if hsv_min_sat is None: hsv_min_sat = self.hsv_min_sat if hsv_min_val is None: hsv_min_val = self.hsv_min_val # Expects a 2D intensity array scaled between -1 to 1... intensity = intensity[..., 0] intensity = 2 * intensity - 1 # Convert to rgb, then rgb to hsv hsv = rgb_to_hsv(rgb[:, :, 0:3]) hue, sat, val = np.moveaxis(hsv, -1, 0) # Modify hsv values (in place) to simulate illumination. # putmask(A, mask, B) <=> A[mask] = B[mask] np.putmask(sat, (np.abs(sat) > 1.e-10) & (intensity > 0), (1 - intensity) * sat + intensity * hsv_max_sat) np.putmask(sat, (np.abs(sat) > 1.e-10) & (intensity < 0), (1 + intensity) * sat - intensity * hsv_min_sat) np.putmask(val, intensity > 0, (1 - intensity) * val + intensity * hsv_max_val) np.putmask(val, intensity < 0, (1 + intensity) * val - intensity * hsv_min_val) np.clip(hsv[:, :, 1:], 0, 1, out=hsv[:, :, 1:]) # Convert modified hsv back to rgb. return hsv_to_rgb(hsv) def blend_soft_light(self, rgb, intensity): """ Combine an RGB image with an intensity map using "soft light" blending, using the "pegtop" formula. Parameters ---------- rgb : `~numpy.ndarray` An (M, N, 3) RGB array of floats ranging from 0 to 1 (color image). intensity : `~numpy.ndarray` An (M, N, 1) array of floats ranging from 0 to 1 (grayscale image). Returns ------- `~numpy.ndarray` An (M, N, 3) RGB array representing the combined images. """ return 2 * intensity * rgb + (1 - 2 * intensity) * rgb**2 def blend_overlay(self, rgb, intensity): """ Combine an RGB image with an intensity map using "overlay" blending. Parameters ---------- rgb : `~numpy.ndarray` An (M, N, 3) RGB array of floats ranging from 0 to 1 (color image). intensity : `~numpy.ndarray` An (M, N, 1) array of floats ranging from 0 to 1 (grayscale image). Returns ------- ndarray An (M, N, 3) RGB array representing the combined images. """ low = 2 * intensity * rgb high = 1 - 2 * (1 - intensity) * (1 - rgb) return np.where(rgb <= 0.5, low, high) def from_levels_and_colors(levels, colors, extend='neither'): """ A helper routine to generate a cmap and a norm instance which behave similar to contourf's levels and colors arguments. Parameters ---------- levels : sequence of numbers The quantization levels used to construct the `BoundaryNorm`. Value ``v`` is quantized to level ``i`` if ``lev[i] <= v < lev[i+1]``. colors : sequence of colors The fill color to use for each level. If *extend* is "neither" there must be ``n_level - 1`` colors. For an *extend* of "min" or "max" add one extra color, and for an *extend* of "both" add two colors. extend : {'neither', 'min', 'max', 'both'}, optional The behaviour when a value falls out of range of the given levels. See `~.Axes.contourf` for details. Returns ------- cmap : `~matplotlib.colors.Colormap` norm : `~matplotlib.colors.Normalize` """ slice_map = { 'both': slice(1, -1), 'min': slice(1, None), 'max': slice(0, -1), 'neither': slice(0, None), } _api.check_in_list(slice_map, extend=extend) color_slice = slice_map[extend] n_data_colors = len(levels) - 1 n_expected = n_data_colors + color_slice.start - (color_slice.stop or 0) if len(colors) != n_expected: raise ValueError( f'With extend == {extend!r} and {len(levels)} levels, ' f'expected {n_expected} colors, but got {len(colors)}') cmap = ListedColormap(colors[color_slice], N=n_data_colors) if extend in ['min', 'both']: cmap.set_under(colors[0]) else: cmap.set_under('none') if extend in ['max', 'both']: cmap.set_over(colors[-1]) else: cmap.set_over('none') cmap.colorbar_extend = extend norm = BoundaryNorm(levels, ncolors=n_data_colors) return cmap, norm 