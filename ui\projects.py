from PyQt5.QtCore import Qt, QDate import datetime from PyQt5.QtGui import QIcon, QFont, QColor from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, from utils import (show_error_message, show_info_message, show_confirmation_message, from database import (Project, Client, Document, Property, PropertyDocument, get_session) from ui.properties import PropertiesWidget from ui.unified_styles import UnifiedStyles, StyledButton, StyledTable, StyledTabWidget, StyledGroupBox QLabel, QLineEdit, QTableWidget, QTableWidgetItem, QFormLayout, QTextEdit, QHeaderView, QMessageBox, QDialog, QComboBox, QGroupBox, QDateEdit, QDoubleSpinBox, QTabWidget, QSplitter, QFrame, QFileDialog, QProgressBar, QMenu, QAction, QSizePolicy) qdate_to_datetime, datetime_to_qdate, format_currency) class ProjectDialog(QDialog): def __init__(self, parent=None, project=None, session=None): super().__init__(parent) self.project = project self.session = session self.init_ui() def init_ui(self): # إعداد نافذة الحوار if self.project: self.setWindowTitle("تعديل مشروع") else: self.setWindowTitle("إضافة مشروع جديد") self.setMinimumWidth(600) self.setMinimumHeight(500) # إنشاء التخطيط الرئيسي main_layout = QVBoxLayout() # إنشاء نموذج معلومات المشروع form_group = StyledGroupBox("معلومات المشروع", "primary") form_layout = QFormLayout() # حقل اسم المشروع self.name_edit = QLineEdit() self.name_edit.setStyleSheet("QLineEdit { padding: 8px; border: 2px solid #E2E8F0; border-radius: 6px; }") if self.project: self.name_edit.setText(self.project.name) form_layout.addRow("اسم المشروع:", self.name_edit) # حقل العميل self.client_combo = QComboBox() self.client_combo.addItem("-- اختر عميل --", None) # إضافة العملاء من قاعدة البيانات if self.session: clients = self.session.query(Client).all() for client in clients: self.client_combo.addItem(client.name, client.id) # تحديد العميل الحالي إذا كان موجودًا if self.project and self.project.client_id: index = self.client_combo.findData(self.project.client_id) if index >= 0: self.client_combo.setCurrentIndex(index) form_layout.addRow("العميل:", self.client_combo) # حقل الموقع self.location_edit = QLineEdit() if self.project: self.location_edit.setText(self.project.location) form_layout.addRow("الموقع:", self.location_edit) # حقل المساحة self.area_edit = QDoubleSpinBox() self.area_edit.setRange(0, 10000) self.area_edit.setDecimals(0) # بدون كسور عشرية self.area_edit.setSuffix(" م²") if self.project: self.area_edit.setValue(self.project.area or 0) form_layout.addRow("المساحة:", self.area_edit) # حقل تاريخ البدء self.start_date_edit = QDateEdit() self.start_date_edit.setCalendarPopup(True) self.start_date_edit.setDate(QDate.currentDate()) if self.project and self.project.start_date: self.start_date_edit.setDate(datetime_to_qdate(self.project.start_date)) form_layout.addRow("تاريخ البدء:", self.start_date_edit) # حقل تاريخ الانتهاء المتوقع self.expected_end_date_edit = QDateEdit() self.expected_end_date_edit.setCalendarPopup(True) self.expected_end_date_edit.setDate(QDate.currentDate().addMonths(1)) if self.project and self.project.expected_end_date: self.expected_end_date_edit.setDate(datetime_to_qdate(self.project.expected_end_date)) form_layout.addRow("تاريخ الانتهاء المتوقع:", self.expected_end_date_edit) # حقل تاريخ الانتهاء الفعلي self.actual_end_date_edit = QDateEdit() self.actual_end_date_edit.setCalendarPopup(True) self.actual_end_date_edit.setDate(QDate.currentDate()) if self.project and self.project.actual_end_date: self.actual_end_date_edit.setDate(datetime_to_qdate(self.project.actual_end_date)) form_layout.addRow("تاريخ الانتهاء الفعلي:", self.actual_end_date_edit) # حقل الحالة self.status_combo = QComboBox() statuses = ["planning", "in_progress", "completed", "cancelled"] status_labels = ["قيد التخطيط", "قيد التنفيذ", "مكتمل", "ملغى"] for i, status in enumerate(statuses): self.status_combo.addItem(status_labels[i], status) if self.project and self.project.status: index = self.status_combo.findData(self.project.status) if index >= 0: self.status_combo.setCurrentIndex(index) form_layout.addRow("الحالة:", self.status_combo) # حقل الميزانية self.budget_edit = QDoubleSpinBox() self.budget_edit.setRange(0, 10000000) self.budget_edit.setDecimals(0) # بدون كسور عشرية self.budget_edit.setSingleStep(1000) if self.project: self.budget_edit.setValue(self.project.budget or 0) form_layout.addRow("الميزانية:", self.budget_edit) form_group.setLayout(form_layout) # حقل الوصف description_group = StyledGroupBox("وصف المشروع", "primary") description_layout = QVBoxLayout() self.description_edit = QTextEdit() if self.project and self.project.description: self.description_edit.setText(self.project.description) description_layout.addWidget(self.description_edit) description_group.setLayout(description_layout) # حقل الملاحظات notes_group = StyledGroupBox("ملاحظات", "primary") notes_layout = QVBoxLayout() self.notes_edit = QTextEdit() if self.project and self.project.notes: self.notes_edit.setText(self.project.notes) notes_layout.addWidget(self.notes_edit) notes_group.setLayout(notes_layout) # أزرار الحفظ والإلغاء button_layout = QHBoxLayout() self.save_button = StyledButton(" حفظ", "success", "normal") self.save_button.clicked.connect(self.accept) self.cancel_button = StyledButton(" إلغاء", "secondary", "normal") self.cancel_button.clicked.connect(self.reject) button_layout.addWidget(self.save_button.button) button_layout.addWidget(self.cancel_button.button) # تجميع التخطيط النهائي main_layout.addWidget(form_group.group_box) main_layout.addWidget(description_group.group_box) main_layout.addWidget(notes_group.group_box) main_layout.addLayout(button_layout) self.setLayout(main_layout) def get_data(self): """الحصول على بيانات المشروع من النموذج""" name = self.name_edit.text().strip() client_id = self.client_combo.currentData() location = self.location_edit.text().strip() area = self.area_edit.value() start_date = qdate_to_datetime(self.start_date_edit.date()) expected_end_date = qdate_to_datetime(self.expected_end_date_edit.date()) # تعيين تاريخ الانتهاء الفعلي بغض النظر عن حالة المشروع actual_end_date = qdate_to_datetime(self.actual_end_date_edit.date()) status = self.status_combo.currentData() budget = self.budget_edit.value() description = self.description_edit.toPlainText().strip() notes = self.notes_edit.toPlainText().strip() # التحقق من صحة البيانات if not name: show_error_message("خطأ", "يجب إدخال اسم المشروع") return None if not client_id: show_error_message("خطأ", "يجب اختيار عميل") return None if expected_end_date <= start_date: show_error_message("خطأ", "يجب أن يكون تاريخ الانتهاء المتوقع بعد تاريخ البدء") return None return { 'name': name, 'client_id': client_id, 'location': location, 'area': area, 'start_date': start_date, 'expected_end_date': expected_end_date, 'actual_end_date': actual_end_date, 'status': status, 'budget': budget, 'description': description, 'notes': notes } class ProjectsWidget(QWidget): def __init__(self, session): super().__init__() self.session = session self.init_ui() self.refresh_data() def init_ui(self): # إنشاء التخطيط الرئيسي مطابق للعمال main_layout = QVBoxLayout() main_layout.setContentsMargins(10, 10, 10, 10) main_layout.setSpacing(8) # إنشاء تبويبات للإنشاء والإستثمار والبيانات العامة للعقارات مع تنسيق مطابق للعمال self.tabs = QTabWidget() self.tabs.setStyleSheet(""" QTabWidget::pane { border: 3px solid #000000; border-radius: 8px; background: #ffffff; margin-top: -1px; } QTabBar::tab { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e2e8f0, stop:0.5 #cbd5e1, stop:1 #94a3b8); color: #1e293b; border: 3px solid #000000; border-bottom: none; border-radius: 8px 8px 0 0; padding: 8px 32px; margin: 2px; font-size: 16px; font-weight: bold; min-width: 400px; max-width: 600px; min-height: 35px; } QTabBar::tab:selected { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4285f4, stop:0.5 #1a73e8, stop:1 #1557b0); color: white; border: 3px solid #000000; border-bottom: none; margin-top: -1px; padding: 9px 32px; font-size: 17px; min-height: 35px; max-height: 40px; } QTabBar::tab:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc); border: 2px solid #000000; } # تبويب الإنشاء والإستثمار projects_tab = QWidget() projects_layout = QVBoxLayout() projects_layout.setContentsMargins(5, 5, 5, 5) projects_layout.setSpacing(8) # إضافة العنوان الرئيسي المطور والمحسن مطابق للعمال (تحت التبويبات) title_label = QLabel(" إدارة المشاريع المتطورة - نظام شامل ومتقدم لإدارة المشاريع مع أدوات احترافية للبحث والتحليل والتقارير") title_label.setFont(QFont("Segoe UI", 18, QFont.Bold)) # خط أكبر وأوضح title_label.setAlignment(Qt.AlignCenter) # توسيط النص في المنتصف title_label.setStyleSheet(""" QLabel { color: #ffffff; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e40af, stop:0.2 #3b82f6, stop:0.4 #6366f1, stop:0.6 #8b5cf6, stop:0.8 #a855f7, stop:1 #c084fc); border: 3px solid #000000; border-radius: 10px; padding: 4px 10px; margin: 2px; font-weight: bold; max-height: 40px; min-height: 40px; } projects_layout.addWidget(title_label) # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للموردين top_frame = QFrame() top_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } # تخطيط أفقي واحد محسن (الطريقة القديمة) search_layout = QHBoxLayout() search_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق search_layout.setSpacing(4) # مسافات متوازنة # إنشاء حاوي عمودي للتوسيط الحقيقي top_container = QVBoxLayout() top_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط top_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط top_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف top_container.addLayout(search_layout) # إضافة مساحة فارغة أسفل للتوسيط top_container.addStretch(1) # تسمية البحث محسنة مع ألوان موحدة مطابقة للموردين search_label = QLabel(" بحث:") search_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b); border: 3px solid #7f1d1d; border-radius: 12px; min-width: 70px; max-width: 70px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c); border: 3px solid #ef4444; } search_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية self.search_edit = QLineEdit() self.search_edit.setPlaceholderText(" ابحث بالاسم، الموقع، العميل أو الوصف...") self.search_edit.textChanged.connect(self.filter_projects) self.search_edit.setStyleSheet(""" QLineEdit { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; selection-background-color: #4f46e5; } QLineEdit:focus { border: 3px solid #3730a3; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f0f9ff, stop:1 #e0f2fe); } QLineEdit:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } search_button = QPushButton("") search_button.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0891b2, stop:0.5 #0e7490, stop:1 #155e75); color: #ffffff; border: 3px solid #164e63; border-radius: 12px; padding: 8px; font-size: 20px; font-weight: bold; min-width: 50px; max-width: 50px; max-height: 38px; min-height: 34px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #22d3ee, stop:0.5 #0891b2, stop:1 #0e7490); border: 3px solid #22d3ee; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75); border: 3px solid #0e7490; } search_button.clicked.connect(self.filter_projects) search_button.setToolTip("بحث متقدم") search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed) search_button.setContentsMargins(0, 0, 0, 0) # تسمية التصفية مطورة بألوان احترافية filter_label = QLabel(" حالة:") filter_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309); border: 3px solid #92400e; border-radius: 12px; min-width: 65px; max-width: 65px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706); border: 3px solid #fbbf24; } filter_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية # إضافة حقل تصفية حسب الحالة محسن self.status_filter = QComboBox() self.status_filter.addItem("جميع الحالات", None) self.status_filter.addItem("قيد التخطيط", "planning") self.status_filter.addItem("قيد التنفيذ", "in_progress") self.status_filter.addItem("مكتملة", "completed") self.status_filter.addItem("ملغاة", "cancelled") self.status_filter.currentIndexChanged.connect(self.filter_projects) self.status_filter.setStyleSheet(""" QComboBox { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; min-width: 120px; } QComboBox:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } QComboBox::drop-down { border: none; width: 30px; } QComboBox::down-arrow { image: none; border: 2px solid #4f46e5; width: 8px; height: 8px; border-radius: 4px; background: #4f46e5; } # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار search_layout.addWidget(search_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter) # يأخذ مساحة أكبر search_layout.addWidget(search_button, 0, Qt.AlignVCenter) search_layout.addWidget(filter_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.status_filter, 1, Qt.AlignVCenter) # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط top_frame.setLayout(top_container) # إنشاء جدول المشاريع المتطور والمحسن self.create_advanced_projects_table() projects_layout.addWidget(top_frame) projects_layout.addWidget(self.projects_table, 1) # إعطاء الجدول أولوية في التمدد # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للموردين bottom_frame = QFrame() bottom_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } actions_layout = QHBoxLayout() actions_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق actions_layout.setSpacing(4) # مسافة أكبر بين الأزرار لتوزيع أفضل # إنشاء حاوي عمودي للتوسيط الحقيقي bottom_container = QVBoxLayout() bottom_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط bottom_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط bottom_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف bottom_container.addLayout(actions_layout) # إضافة مساحة فارغة أسفل للتوسيط bottom_container.addStretch(1) # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد مطابق للموردين # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة self.add_button = QPushButton(" إضافة مشروع ") self.style_advanced_button(self.add_button, 'emerald', has_menu=True) # أخضر زمردي مميز مع قائمة self.add_button.clicked.connect(self.add_project) self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.edit_button = QPushButton(" تعديل") self.style_advanced_button(self.edit_button, 'primary') # أزرق كلاسيكي self.edit_button.clicked.connect(self.edit_project) self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.delete_button = QPushButton(" حذف") self.style_advanced_button(self.delete_button, 'danger') # أحمر تحذيري self.delete_button.clicked.connect(self.delete_project) self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.refresh_button = QPushButton(" تحديث") self.style_advanced_button(self.refresh_button, 'modern_teal') # تصميم حديث ومتطور self.refresh_button.clicked.connect(self.refresh_data) self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # المجموعة الثانية - العمليات المتقدمة self.view_button = QPushButton(" عرض التفاصيل ") self.style_advanced_button(self.view_button, 'indigo', has_menu=True) # بنفسجي للتفاصيل self.view_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة لعرض التفاصيل view_menu = QMenu(self) view_details_action = QAction(" عرض التفاصيل", self) view_details_action.triggered.connect(self.view_project) view_menu.addAction(view_details_action) timeline_action = QAction(" الجدول الزمني", self) timeline_action.triggered.connect(self.view_project_timeline) view_menu.addAction(timeline_action) budget_action = QAction(" تفاصيل الميزانية", self) budget_action.triggered.connect(self.view_project_budget) view_menu.addAction(budget_action) progress_action = QAction(" تقرير التقدم", self) progress_action.triggered.connect(self.view_project_progress) view_menu.addAction(progress_action) self.view_button.setMenu(view_menu) self.documents_button = QPushButton(" الوثائق ") self.style_advanced_button(self.documents_button, 'orange', has_menu=True) # برتقالي للوثائق self.documents_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة للوثائق docs_menu = QMenu(self) manage_docs_action = QAction(" إدارة الوثائق", self) manage_docs_action.triggered.connect(self.manage_documents) docs_menu.addAction(manage_docs_action) add_doc_action = QAction(" إضافة وثيقة", self) add_doc_action.triggered.connect(self.add_document) docs_menu.addAction(add_doc_action) add_image_action = QAction(" إضافة صورة", self) add_image_action.triggered.connect(self.add_image) docs_menu.addAction(add_image_action) view_gallery_action = QAction(" معرض الصور", self) view_gallery_action.triggered.connect(self.view_image_gallery) docs_menu.addAction(view_gallery_action) self.documents_button.setMenu(docs_menu) self.report_button = QPushButton(" التقارير") self.style_advanced_button(self.report_button, 'cyan') # سيان مميز للتقارير self.report_button.clicked.connect(self.generate_projects_report) self.report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.export_button = QPushButton(" تصدير ") self.style_advanced_button(self.export_button, 'info', has_menu=True) # لون متسق مع نظام الألوان self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.statistics_button = QPushButton(" الإحصائيات") self.style_advanced_button(self.statistics_button, 'rose') # وردي للإحصائيات self.statistics_button.clicked.connect(self.show_statistics) self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إضافة الأزرار للتخطيط actions_layout.addWidget(self.add_button) actions_layout.addWidget(self.edit_button) actions_layout.addWidget(self.delete_button) actions_layout.addWidget(self.refresh_button) actions_layout.addWidget(self.view_button) actions_layout.addWidget(self.documents_button) actions_layout.addWidget(self.report_button) actions_layout.addWidget(self.export_button) actions_layout.addWidget(self.statistics_button) # تعيين التخطيط للإطار السفلي bottom_frame.setLayout(bottom_container) # تجميع تخطيط تبويب المشاريع projects_layout.addWidget(bottom_frame) projects_tab.setLayout(projects_layout) # تبويب البيانات العامة للعقارات self.properties_widget = PropertiesWidget(self.session) # إضافة التبويبات مع أيقونات مثل العمال self.tabs.addTab(projects_tab, " بيانات المشاريع") self.tabs.addTab(self.properties_widget, " إدارة العقارات") # إضافة التبويبات إلى التخطيط الرئيسي main_layout.addWidget(self.tabs) self.setLayout(main_layout) def create_advanced_projects_table(self): """إنشاء جدول المشاريع المتطور والمحسن مطابق للموردين""" styled_table = StyledTable() self.projects_table = styled_table.table self.projects_table.setColumnCount(8) self.projects_table.setHorizontalHeaderLabels(["الرقم", "اسم المشروع", "العميل", "الموقع", "تاريخ البدء", "تاريخ الانتهاء المتوقع", "الحالة", "التقدم"]) # تحسين عرض الأعمدة header = self.projects_table.horizontalHeader() header.setSectionResizeMode(0, QHeaderView.Fixed) # الرقم header.setSectionResizeMode(1, QHeaderView.Stretch) # اسم المشروع header.setSectionResizeMode(2, QHeaderView.Stretch) # العميل header.setSectionResizeMode(3, QHeaderView.Stretch) # الموقع header.setSectionResizeMode(4, QHeaderView.Fixed) # تاريخ البدء header.setSectionResizeMode(5, QHeaderView.Fixed) # تاريخ الانتهاء المتوقع header.setSectionResizeMode(6, QHeaderView.Fixed) # الحالة header.setSectionResizeMode(7, QHeaderView.Fixed) # التقدم # تحديد عرض الأعمدة الثابتة self.projects_table.setColumnWidth(0, 80) # الرقم self.projects_table.setColumnWidth(4, 120) # تاريخ البدء self.projects_table.setColumnWidth(5, 150) # تاريخ الانتهاء المتوقع self.projects_table.setColumnWidth(6, 120) # الحالة self.projects_table.setColumnWidth(7, 100) # التقدم self.projects_table.setSelectionBehavior(QTableWidget.SelectRows) self.projects_table.setSelectionMode(QTableWidget.SingleSelection) self.projects_table.setEditTriggers(QTableWidget.NoEditTriggers) self.projects_table.setAlternatingRowColors(True) # تحسين تصميم الجدول QTableWidget { background-color: #ffffff; border: 3px solid #000000; border-radius: 8px; gridline-color: #e5e7eb; font-size: 13px; font-weight: bold; } QTableWidget::item { padding: 8px; border-bottom: 1px solid #e5e7eb; color: #000000; font-weight: bold; } QTableWidget::item:selected { background-color: #3b82f6; color: white; } QTableWidget::item:hover { background-color: #f3f4f6; } QHeaderView::section { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4f46e5, stop:1 #3730a3); color: white; padding: 8px; border: 1px solid #3730a3; font-weight: bold; font-size: 14px; } """) def refresh_data(self): # الحصول على جميع المشاريع من قاعدة البيانات projects = self.session.query(Project).order_by(Project.start_date.desc()).all() self.populate_table(projects) def populate_table(self, projects): """ملء جدول المشاريع بالبيانات""" self.projects_table.setRowCount(0) for row, project in enumerate(projects): self.projects_table.insertRow(row) # الرقم self.projects_table.setItem(row, 0, QTableWidgetItem(str(project.id))) # اسم المشروع self.projects_table.setItem(row, 1, QTableWidgetItem(project.name)) # اسم العميل client_name = project.client.name if project.client else "غير محدد" self.projects_table.setItem(row, 2, QTableWidgetItem(client_name)) # الموقع self.projects_table.setItem(row, 3, QTableWidgetItem(project.location or "")) # تاريخ البدء start_date = project.start_date.strftime("%Y-%m-%d") if project.start_date else "" self.projects_table.setItem(row, 4, QTableWidgetItem(start_date)) # تاريخ الانتهاء المتوقع expected_end_date = project.expected_end_date.strftime("%Y-%m-%d") if project.expected_end_date else "" self.projects_table.setItem(row, 5, QTableWidgetItem(expected_end_date)) # حالة المشروع status_map = { 'planning': 'قيد التخطيط', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتمل', 'cancelled': 'ملغى' } status_text = status_map.get(project.status, project.status or "") status_item = QTableWidgetItem(status_text) # تلوين خلية الحالة حسب الحالة if project.status == 'planning': status_item.setBackground(QColor(200, 200, 255)) # أزرق فاتح elif project.status == 'in_progress': status_item.setBackground(QColor(255, 200, 100)) # برتقالي فاتح elif project.status == 'completed': status_item.setBackground(QColor(200, 255, 200)) # أخضر فاتح elif project.status == 'cancelled': status_item.setBackground(QColor(255, 200, 200)) # أحمر فاتح self.projects_table.setItem(row, 6, status_item) # شريط التقدم progress = QProgressBar() if project.status == 'planning': progress.setValue(10) elif project.status == 'in_progress': progress.setValue(50) elif project.status == 'completed': progress.setValue(100) else: progress.setValue(0) self.projects_table.setCellWidget(row, 7, progress) def filter_projects(self): search_text = self.search_edit.text().strip().lower() status = self.status_filter.currentData() # بناء الاستعلام query = self.session.query(Project) # تطبيق تصفية النص if search_text: query = query.filter( Project.name.like(f"%{search_text}%") | Project.location.like(f"%{search_text}%") | Project.description.like(f"%{search_text}%") ) # تطبيق تصفية الحالة if status: query = query.filter(Project.status == status) # تنفيذ الاستعلام projects = query.order_by(Project.start_date.desc()).all() # تحديث الجدول self.populate_table(projects) def add_project(self): """إضافة مشروع جديد""" dialog = ProjectDialog(self, session=self.session) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # إنشاء مشروع جديد project = Project(**data) self.session.add(project) self.session.commit() show_info_message("تم", "تم إضافة المشروع بنجاح") self.refresh_data() def edit_project(self): selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return project_id = int(self.projects_table.item(selected_row, 0).text()) project = self.session.query(Project).get(project_id) if not project: show_error_message("خطأ", "لم يتم العثور على المشروع") return dialog = ProjectDialog(self, project, self.session) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # تحديث بيانات المشروع for key, value in data.items(): setattr(project, key, value) self.session.commit() show_info_message("تم", "تم تحديث المشروع بنجاح") self.refresh_data() def delete_project(self): """حذف مشروع""" selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return project_id = int(self.projects_table.item(selected_row, 0).text()) project = self.session.query(Project).get(project_id) if not project: show_error_message("خطأ", "لم يتم العثور على المشروع") return # طلب تأكيد الحذف if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف المشروع '{project.name}'؟"): self.session.delete(project) self.session.commit() show_info_message("تم", "تم حذف المشروع بنجاح") self.refresh_data() def view_project(self): selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return project_id = int(self.projects_table.item(selected_row, 0).text()) project = self.session.query(Project).get(project_id) if not project: show_error_message("خطأ", "لم يتم العثور على المشروع") return # إنشاء نافذة لعرض تفاصيل المشروع dialog = QDialog(self) dialog.setWindowTitle(f"تفاصيل المشروع: {project.name}") dialog.setMinimumSize(600, 400) layout = QVBoxLayout() # عنوان المشروع title_label = QLabel(project.name) title_label.setFont(QFont("Arial", 16, QFont.Bold)) layout.addWidget(title_label) # معلومات المشروع info_layout = QFormLayout() client_name = project.client.name if project.client else "غير محدد" info_layout.addRow("العميل:", QLabel(client_name)) info_layout.addRow("الموقع:", QLabel(project.location or "")) area_text = f"{project.area} م²" if project.area else "" info_layout.addRow("المساحة:", QLabel(area_text)) start_date = project.start_date.strftime("%Y-%m-%d") if project.start_date else "" info_layout.addRow("تاريخ البدء:", QLabel(start_date)) expected_end_date = project.expected_end_date.strftime("%Y-%m-%d") if project.expected_end_date else "" info_layout.addRow("تاريخ الانتهاء المتوقع:", QLabel(expected_end_date)) actual_end_date = project.actual_end_date.strftime("%Y-%m-%d") if project.actual_end_date else "" info_layout.addRow("تاريخ الانتهاء الفعلي:", QLabel(actual_end_date)) status_map = { 'planning': 'قيد التخطيط', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتمل', 'cancelled': 'ملغى' } status_text = status_map.get(project.status, project.status or "") info_layout.addRow("الحالة:", QLabel(status_text)) budget_text = format_currency(project.budget) if project.budget else "" info_layout.addRow("الميزانية:", QLabel(budget_text)) layout.addLayout(info_layout) # وصف المشروع if project.description: description_group = StyledGroupBox("وصف المشروع", "primary") description_layout = QVBoxLayout() description_label = QLabel(project.description) description_label.setWordWrap(True) description_layout.addWidget(description_label) description_group.setLayout(description_layout) layout.addWidget(description_group) # ملاحظات المشروع if project.notes: notes_group = StyledGroupBox("ملاحظات", "primary") notes_layout = QVBoxLayout() notes_label = QLabel(project.notes) notes_label.setWordWrap(True) notes_layout.addWidget(notes_label) notes_group.setLayout(notes_layout) layout.addWidget(notes_group) # زر إغلاق close_button = QPushButton("إغلاق") close_button.clicked.connect(dialog.accept) button_layout = QHBoxLayout() button_layout.addStretch() button_layout.addWidget(close_button) layout.addLayout(button_layout) dialog.setLayout(layout) dialog.exec_() def manage_documents(self): """إدارة وثائق وصور المشروع""" selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return project_id = int(self.projects_table.item(selected_row, 0).text()) project = self.session.query(Project).get(project_id) if not project: show_error_message("خطأ", "لم يتم العثور على المشروع") return # إنشاء نافذة لإدارة الوثائق # TODO: إضافة نافذة إدارة الوثائق لاحقاً show_info_message("قريباً", "ستتم إضافة ميزة إدارة الوثائق قريباً") def view_project_timeline(self): selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return project_id = int(self.projects_table.item(selected_row, 0).text()) project = self.session.query(Project).get(project_id) if not project: show_error_message("خطأ", "لم يتم العثور على المشروع") return # إنشاء نافذة لعرض الجدول الزمني dialog = QDialog(self) dialog.setWindowTitle(f"الجدول الزمني - {project.name}") dialog.setMinimumSize(700, 500) layout = QVBoxLayout() # معلومات الجدول الزمني status_map = {'planning': 'قيد التخطيط', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتمل', 'cancelled': 'ملغى'} progress_map = {'planning': '10%', 'in_progress': '50%', 'completed': '100%', 'cancelled': '0%'} timeline_text = f""" الجدول الزمني للمشروع - {project.name} التواريخ المهمة: • تاريخ البدء: {project.start_date.strftime('%Y-%m-%d') if project.start_date else 'غير محدد'} • تاريخ الانتهاء المتوقع: {project.expected_end_date.strftime('%Y-%m-%d') if project.expected_end_date else 'غير محدد'} • تاريخ الانتهاء الفعلي: {project.actual_end_date.strftime('%Y-%m-%d') if project.actual_end_date else 'لم ينته بعد'} ⏱ المدة الزمنية: • المدة المخططة: {(project.expected_end_date - project.start_date).days if project.start_date and project.expected_end_date else 'غير محسوبة'} يوم • المدة الفعلية: {(project.actual_end_date - project.start_date).days if project.start_date and project.actual_end_date else 'قيد التنفيذ'} يوم حالة المشروع: • الحالة الحالية: {status_map.get(project.status, project.status or '')} • نسبة الإنجاز: {progress_map.get(project.status, '0%')} ملاحظات الجدولة: • تحقق من التقدم بانتظام • راجع المواعيد النهائية • تواصل مع العميل عند التأخير timeline_label = QLabel(timeline_text) timeline_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;") layout.addWidget(timeline_label) # زر إغلاق close_button = StyledButton("إغلاق", "secondary") close_button.clicked.connect(dialog.accept) layout.addWidget(close_button.button) dialog.setLayout(layout) dialog.exec_() def view_project_budget(self): """عرض تفاصيل الميزانية للمشروع""" selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return project_id = int(self.projects_table.item(selected_row, 0).text()) project = self.session.query(Project).get(project_id) if not project: show_error_message("خطأ", "لم يتم العثور على المشروع") return # إنشاء نافذة لعرض تفاصيل الميزانية dialog = QDialog(self) dialog.setWindowTitle(f"تفاصيل الميزانية - {project.name}") dialog.setMinimumSize(600, 400) layout = QVBoxLayout() # معلومات الميزانية تفاصيل الميزانية - {project.name} الميزانية المخططة: • إجمالي الميزانية: {format_currency(project.budget or 0)} • الميزانية لكل متر مربع: {format_currency((project.budget or 0) / (project.area or 1)) if project.area else 'غير محسوبة'} تحليل التكاليف: • تكلفة المواد (تقديرية): {format_currency((project.budget or 0) * 0.6)} • تكلفة العمالة (تقديرية): {format_currency((project.budget or 0) * 0.3)} • تكاليف أخرى (تقديرية): {format_currency((project.budget or 0) * 0.1)} مؤشرات مالية: • حالة الميزانية: {'مناسبة' if (project.budget or 0) > 0 else 'غير محددة'} • نوع المشروع: {'اقتصادي' if (project.budget or 0) < 100000 else 'متوسط' if (project.budget or 0) < 500000 else 'كبير'} ملاحظات مالية: • راجع الميزانية بانتظام • احتفظ بهامش للطوارئ (10-15%) • وثق جميع المصروفات """ budget_label = QLabel(budget_text) budget_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;") layout.addWidget(budget_label.label) # زر إغلاق close_button = StyledButton("إغلاق", "secondary") close_button.clicked.connect(dialog.accept) layout.addWidget(close_button.button) dialog.setLayout(layout) dialog.exec_() def view_project_progress(self): selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return project_id = int(self.projects_table.item(selected_row, 0).text()) project = self.session.query(Project).get(project_id) if not project: show_error_message("خطأ", "لم يتم العثور على المشروع") return # إنشاء نافذة لعرض تقرير التقدم dialog = QDialog(self) dialog.setWindowTitle(f"تقرير التقدم - {project.name}") dialog.setMinimumSize(650, 450) layout = QVBoxLayout() # معلومات التقدم progress_percentage = {'planning': 10, 'in_progress': 50, 'completed': 100, 'cancelled': 0}.get(project.status, 0) status_map = {'planning': 'قيد التخطيط', 'in_progress': 'قيد التنفيذ', 'completed': 'مكتمل', 'cancelled': 'ملغى'} progress_text = f""" تقرير التقدم - {project.name} حالة المشروع: • الحالة: {status_map.get(project.status, project.status or '')} • نسبة الإنجاز: {progress_percentage}% • العميل: {project.client.name if project.client else 'غير محدد'} التقدم الزمني: • أيام منذ البدء: {(datetime.datetime.now().date() - project.start_date.date()).days if project.start_date else 'غير محسوب'} • أيام متبقية: {(project.expected_end_date.date() - datetime.datetime.now().date()).days if project.expected_end_date and project.expected_end_date > datetime.datetime.now() else 'انتهت المدة' if project.expected_end_date else 'غير محسوب'} معلومات الموقع: • الموقع: {project.location or 'غير محدد'} • المساحة: {project.area or 0} متر مربع المعلومات المالية: • الميزانية: {format_currency(project.budget or 0)} • التكلفة المتوقعة حتى الآن: {format_currency((project.budget or 0) * (progress_percentage / 100))} مؤشرات الأداء: • سرعة التنفيذ: {'بطيئة' if progress_percentage < 30 else 'متوسطة' if progress_percentage < 70 else 'سريعة'} • حالة الجدولة: {'متأخر' if project.expected_end_date and project.expected_end_date < datetime.datetime.now() and project.status != 'completed' else 'في الموعد'} التوصيات: • {'تسريع وتيرة العمل' if progress_percentage < 50 else 'الحفاظ على الوتيرة الحالية'} • {'مراجعة الجدولة' if project.expected_end_date and project.expected_end_date < datetime.datetime.now() else 'متابعة الخطة'} progress_label = QLabel(progress_text) progress_label.setStyleSheet("padding: 15px; background-color: #f8f9fa; border-radius: 8px; font-size: 12px;") layout.addWidget(progress_label.label) # شريط التقدم البصري progress_bar = QProgressBar() progress_bar.setValue(progress_percentage) progress_bar.setStyleSheet(""" QProgressBar { border: 2px solid #bdc3c7; border-radius: 8px; text-align: center; font-weight: bold; font-size: 14px; height: 25px; } QProgressBar::chunk { background-color: #3498db; border-radius: 6px; } layout.addWidget(progress_bar) # زر إغلاق close_button = StyledButton("إغلاق", "secondary", "normal") close_button.clicked.connect(dialog.accept) layout.addWidget(close_button.button) dialog.setLayout(layout) dialog.exec_() def add_document(self): """إضافة وثيقة للمشروع""" selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return show_info_message("قريباً", "ستتم إضافة هذه الميزة قريباً") def add_image(self): selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return show_info_message("قريباً", "ستتم إضافة هذه الميزة قريباً") def view_image_gallery(self): """عرض معرض صور المشروع""" selected_row = self.projects_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار مشروع من القائمة") return show_info_message("قريباً", "ستتم إضافة هذه الميزة قريباً") def generate_projects_report(self): show_info_message("تقرير المشاريع", "سيتم إضافة ميزة التقارير قريباً") def show_statistics(self): """عرض إحصائيات المشاريع""" show_info_message("إحصائيات المشاريع", "سيتم إضافة ميزة الإحصائيات قريباً") def style_advanced_button(self, button, button_type, has_menu=False): try: # تحديد الألوان المتنوعة والمميزة حسب نوع الزر colors = { 'primary': { 'bg_start': '#1e3a8a', 'bg_mid': '#3b82f6', 'bg_end': '#1d4ed8', 'bg_bottom': '#1e40af', 'hover_start': '#3b82f6', 'hover_mid': '#60a5fa', 'hover_end': '#2563eb', 'hover_bottom': '#1d4ed8', 'hover_border': '#2563eb', 'pressed_start': '#1e40af', 'pressed_mid': '#1d4ed8', 'pressed_end': '#1e3a8a', 'pressed_bottom': '#172554', 'pressed_border': '#1e3a8a', 'border': '#1d4ed8', 'text': '#ffffff' }, 'emerald': { 'bg_start': '#064e3b', 'bg_mid': '#10b981', 'bg_end': '#059669', 'bg_bottom': '#022c22', 'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#059669', 'hover_bottom': '#064e3b', 'hover_border': '#059669', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b', 'pressed_end': '#014737', 'pressed_bottom': '#014737', 'pressed_border': '#064e3b', 'border': '#059669', 'text': '#ffffff' }, 'danger': { 'bg_start': '#991b1b', 'bg_mid': '#ef4444', 'bg_end': '#dc2626', 'bg_bottom': '#7f1d1d', 'hover_start': '#ef4444', 'hover_mid': '#f87171', 'hover_end': '#dc2626', 'hover_bottom': '#991b1b', 'hover_border': '#dc2626', 'pressed_start': '#7f1d1d', 'pressed_mid': '#991b1b', 'pressed_end': '#450a0a', 'pressed_bottom': '#450a0a', 'pressed_border': '#991b1b', 'border': '#dc2626', 'text': '#ffffff' }, 'info': { 'bg_start': '#0891b2', 'bg_mid': '#0ea5e9', 'bg_end': '#0284c7', 'bg_bottom': '#075985', 'hover_start': '#0ea5e9', 'hover_mid': '#38bdf8', 'hover_end': '#0891b2', 'hover_bottom': '#0284c7', 'hover_border': '#0891b2', 'pressed_start': '#0284c7', 'pressed_mid': '#0891b2', 'pressed_end': '#075985', 'pressed_bottom': '#0c4a6e', 'pressed_border': '#075985', 'border': '#0891b2', 'text': '#ffffff' }, 'modern_teal': { 'bg_start': '#0d9488', 'bg_mid': '#14b8a6', 'bg_end': '#0f766e', 'bg_bottom': '#134e4a', 'hover_start': '#14b8a6', 'hover_mid': '#2dd4bf', 'hover_end': '#0d9488', 'hover_bottom': '#0f766e', 'hover_border': '#14b8a6', 'pressed_start': '#0f766e', 'pressed_mid': '#0d9488', 'pressed_end': '#134e4a', 'pressed_bottom': '#042f2e', 'pressed_border': '#0f766e', 'border': '#0d9488', 'text': '#ffffff' }, 'cyan': { 'bg_start': '#0891b2', 'bg_mid': '#06b6d4', 'bg_end': '#0e7490', 'bg_bottom': '#164e63', 'hover_start': '#06b6d4', 'hover_mid': '#22d3ee', 'hover_end': '#0891b2', 'hover_bottom': '#0e7490', 'hover_border': '#06b6d4', 'pressed_start': '#0e7490', 'pressed_mid': '#0891b2', 'pressed_end': '#164e63', 'pressed_bottom': '#083344', 'pressed_border': '#0e7490', 'border': '#0891b2', 'text': '#ffffff' }, 'rose': { 'bg_start': '#be185d', 'bg_mid': '#ec4899', 'bg_end': '#be185d', 'bg_bottom': '#9d174d', 'hover_start': '#ec4899', 'hover_mid': '#f472b6', 'hover_end': '#be185d', 'hover_bottom': '#be185d', 'hover_border': '#ec4899', 'pressed_start': '#9d174d', 'pressed_mid': '#be185d', 'pressed_end': '#831843', 'pressed_bottom': '#500724', 'pressed_border': '#9d174d', 'border': '#be185d', 'text': '#ffffff' }, 'indigo': { 'bg_start': '#3730a3', 'bg_mid': '#6366f1', 'bg_end': '#4f46e5', 'bg_bottom': '#312e81', 'hover_start': '#6366f1', 'hover_mid': '#818cf8', 'hover_end': '#4f46e5', 'hover_bottom': '#3730a3', 'hover_border': '#6366f1', 'pressed_start': '#312e81', 'pressed_mid': '#3730a3', 'pressed_end': '#1e1b4b', 'pressed_bottom': '#1e1b4b', 'pressed_border': '#3730a3', 'border': '#4f46e5', 'text': '#ffffff' }, 'orange': { 'bg_start': '#c2410c', 'bg_mid': '#f97316', 'bg_end': '#ea580c', 'bg_bottom': '#9a3412', 'hover_start': '#f97316', 'hover_mid': '#fb923c', 'hover_end': '#ea580c', 'hover_bottom': '#c2410c', 'hover_border': '#f97316', 'pressed_start': '#9a3412', 'pressed_mid': '#c2410c', 'pressed_end': '#7c2d12', 'pressed_bottom': '#431407', 'pressed_border': '#9a3412', 'border': '#ea580c', 'text': '#ffffff' } } # الحصول على ألوان الزر المحدد color_scheme = colors.get(button_type, colors['primary']) # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else "" # تطبيق التصميم المتطور style = f""" QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['bg_start']}, stop:0.3 {color_scheme['bg_mid']}, stop:0.7 {color_scheme['bg_end']}, stop:1 {color_scheme['bg_bottom']}); color: {color_scheme['text']}; border: 2px solid {color_scheme['border']}; border-radius: 8px; padding: 8px 16px; font-weight: bold; font-size: 13px; min-height: 38px; max-height: 38px; min-width: 100px; text-align: center; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['hover_start']}, stop:0.3 {color_scheme['hover_mid']}, stop:0.7 {color_scheme['hover_end']}, stop:1 {color_scheme['hover_bottom']}); border: 2px solid {color_scheme['hover_border']}; }} QPushButton:pressed {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['pressed_start']}, stop:0.3 {color_scheme['pressed_mid']}, stop:0.7 {color_scheme['pressed_end']}, stop:1 {color_scheme['pressed_bottom']}); border: 2px solid {color_scheme['pressed_border']}; }} {menu_indicator} button.setStyleSheet(style) print(f" تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}") except Exception as e: print(f" خطأ في تطبيق التصميم على الزر: {str(e)}")