import re from PyQt5.QtCore import QPropertyAnimation from datetime import datetime from PyQt5.QtCore import QDate, <PERSON>t, QTimer from PyQt5.QtCore import QTimer from PyQt5.QtCore import QTimer from PyQt5.QtCore import Qt, QTimer from PyQt5.QtCore import Qt, QTimer, QRect, pyqtSignal, QEasingCurve from datetime import datetime from datetime import datetime from datetime import datetime from datetime import datetime from datetime import datetime from datetime import datetime from datetime import datetime, timedelta import json import json import json import json import json import json import os import os import re import re import re from PyQt5.QtCore import Qt import re import re import re import re import re from PyQt5.QtCore import Qt, QTimer, QDate from datetime import datetime from utils import show_error_message, show_info_message, show_confirmation_message, is_valid_email, is_valid_phone, format_currency, format_date from PyQt5.QtWidgets import QApplication from PyQt5.QtWidgets import QApplication from PyQt5.QtWidgets import QApplication from utils import show_info_message from PyQt5.QtWidgets import QMessageBox import pandas as pd import subprocess import subprocess import webbrowser import webbrowser from PyQt5.QtGui import QColor from PyQt5.QtGui import QColor from PyQt5.QtGui import QFont, QPalette, QColor, QPainter, QLinearGradient from PyQt5.QtGui import QTextDocument from PyQt5.QtPrintSupport import QPrinter from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QCheckBox, QDialogButtonBox, from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QPushButton, QLabel, from PyQt5.QtWidgets import QApplication from PyQt5.QtWidgets import QApplication from PyQt5.QtWidgets import QApplication from PyQt5.QtWidgets import QApplication from PyQt5.QtWidgets import QDialog, QVBoxLayout, QListWidget, QLabel, QPushButton, QHBoxLayout from PyQt5.QtWidgets import QFileDialog from PyQt5.QtWidgets import QFileDialog from PyQt5.QtWidgets import QFileDialog from PyQt5.QtWidgets import QInputDialog from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from PyQt5.QtWidgets import QMessageBox from utils import show_error_message from utils import show_error_message from utils import show_error_message from utils import show_info_message from utils import show_info_message import csv import platform import platform import platform import subprocess import subprocess import subprocess import traceback import traceback import traceback import traceback import traceback import traceback import traceback import webbrowser import webbrowser from PyQt5.QtGui import QPainter, QFont, QColor from PyQt5.QtWidgets import QApplication from PyQt5.QtWidgets import QApplication from PyQt5.QtWidgets import QFileDialog from PyQt5.QtWidgets import QTableWidget from PyQt5.QtWidgets import QTableWidget from PyQt5.QtGui import QFont, QColor, QIcon, QPainter from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, from database import Sale, Invoice from database import Sale, Invoice from database import ClientPhone from ui.unified_styles import UnifiedStyles from database import Client, ClientPhone, update_client_balance, Invoice from ui.unified_styles import (UnifiedStyles, StyledButton, QLabel, QLineEdit, QTableWidget, QTableWidgetItem, QFormLayout, QTextEdit, QDialog, QRadioButton, QMenu, QAction, QFrame, QSizePolicy, QSplitter, QListWidget, QListWidgetItem, QGroupBox, QButtonGroup, QFileDialog, QMessageBox, QHeaderView, QAbstractItemView) StyledTable, StyledLabel, BaseDialog) class PhoneDialog(QDialog): def __init__(self, parent=None, phone=None): super().__init__(parent) self.phone = phone # إزالة شريط العنوان الافتراضي self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint) self.setAttribute(Qt.WA_TranslucentBackground, False) # تحسين حجم النافذة - ارتفاع أكبر لإظهار الجزء السفلي كاملاً self.setMinimumSize(550, 600) self.setMaximumSize(650, 700) self.resize(600, 650) # متغيرات لتتبع السحب self.dragging = False self.drag_position = None self.init_ui() self.center_on_screen() def center_on_screen(self): """توسيط النافذة على الشاشة""" screen = QApplication.desktop().screenGeometry() size = self.geometry() self.move( (screen.width() - size.width()) // 2, (screen.height() - size.height()) // 2 ) def init_ui(self): # تعيين خلفية النافذة مع إطار مربع بسيط بألوان شريط العنوان self.setStyleSheet(""" QDialog { background: #f5f5f5; border: 4px solid #4facfe; border-radius: 0px; } # إنشاء التخطيط الرئيسي main_layout = QVBoxLayout() main_layout.setContentsMargins(0, 0, 0, 0) main_layout.setSpacing(0) # إضافة شريط العنوان المتقدم self.create_custom_title_bar(main_layout) # إضافة المحتوى الرئيسي content_widget = QWidget() content_layout = QVBoxLayout(content_widget) content_layout.setContentsMargins(25, 25, 25, 25) content_layout.setSpacing(20) # إضافة عنوان النافذة المتطور self.create_header_section(content_layout) # إنشاء النموذج المتطور self.create_form_section(content_layout) # أزرار الحفظ والإلغاء المحسنة self.create_action_buttons(content_layout) # إضافة المحتوى للتخطيط الرئيسي main_layout.addWidget(content_widget) self.setLayout(main_layout) def create_custom_title_bar(self, main_layout): """إنشاء شريط عنوان مخصص متطور""" try: # إنشاء شريط العنوان المخصص self.custom_title_bar = QWidget() self.custom_title_bar.setObjectName("customTitleBar") self.custom_title_bar.setFixedHeight(30) # تقليل الارتفاع # تطبيق النمط الموحد للشريط QWidget#customTitleBar { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #1a1a2e, stop:0.1 #16213e, stop:0.15 #0f3460, stop:0.25 #533483, stop:0.35 #7209b7, stop:0.45 #a663cc, stop:0.55 #4facfe, stop:0.65 #00f2fe, stop:0.75 #43e97b, stop:0.85 #38f9d7, stop:0.95 #667eea, stop:1 #764ba2) !important; border: none !important; padding: 4px 12px !important; margin: 0px !important; min-height: 35px !important; max-height: 35px !important; border-radius: 0px !important; } QWidget#customTitleBar QLabel { color: white !important; font-family: 'Segoe UI', 'Arial', sans-serif !important; font-weight: bold !important; background: transparent !important; border: none !important; padding: 2px 8px !important; margin: 0px !important; } QWidget#customTitleBar QLabel#centerTitle { font-size: 12px !important; font-weight: 900 !important; text-align: center !important; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 215, 0, 0.5), stop:0.2 rgba(255, 193, 7, 0.4), stop:0.4 rgba(255, 255, 255, 0.3), stop:0.6 rgba(255, 193, 7, 0.4), stop:0.8 rgba(255, 215, 0, 0.5), stop:1 rgba(255, 235, 59, 0.3)) !important; border: none !important; border-radius: 15px !important; } QWidget#customTitleBar QPushButton { background: rgba(255, 255, 255, 0.1) !important; border: 1px solid rgba(255, 255, 255, 0.2) !important; border-radius: 8px !important; color: white !important; font-weight: bold !important; font-size: 12px !important; padding: 4px 8px !important; margin: 2px !important; min-width: 25px !important; max-width: 25px !important; min-height: 20px !important; max-height: 20px !important; } QWidget#customTitleBar QPushButton:hover { background: rgba(255, 255, 255, 0.2) !important; border: 1px solid rgba(255, 255, 255, 0.4) !important; } QWidget#customTitleBar QPushButton:pressed { background: rgba(255, 255, 255, 0.3) !important; } QWidget#customTitleBar QPushButton#closeButton:hover { background: rgba(244, 67, 54, 0.8) !important; border: 1px solid rgba(244, 67, 54, 1) !important; } """) # إنشاء التخطيط الأفقي للشريط title_layout = QHBoxLayout(self.custom_title_bar) title_layout.setContentsMargins(8, 2, 8, 2) title_layout.setSpacing(8) # أزرار التحكم في النافذة (في أقصى اليسار) self.create_window_control_buttons(title_layout) # مساحة مرنة title_layout.addStretch() # العنوان المركزي title_text = " إضافة رقم هاتف جديد" if not self.phone else " تعديل رقم الهاتف" self.center_title_label = QLabel(title_text) self.center_title_label.setObjectName("centerTitle") self.center_title_label.setAlignment(Qt.AlignCenter) self.center_title_label.setMinimumWidth(200) # تقليل العرض أكثر title_layout.addWidget(self.center_title_label) # مساحة مرنة title_layout.addStretch() # إضافة وظائف السحب للنافذة self.setup_window_dragging() # إضافة الشريط للتخطيط الرئيسي main_layout.addWidget(self.custom_title_bar) except Exception as e: print(f" خطأ في إنشاء شريط العنوان المخصص: {e}") def create_window_control_buttons(self, layout): try: # زر الإغلاق (أولاً) self.close_btn = QPushButton("") self.close_btn.setObjectName("closeButton") self.close_btn.setToolTip("إغلاق النافذة") self.close_btn.clicked.connect(self.reject) layout.addWidget(self.close_btn) # زر التصغير (ثانياً) self.minimize_btn = QPushButton("") self.minimize_btn.setToolTip("تصغير النافذة") self.minimize_btn.clicked.connect(self.showMinimized) layout.addWidget(self.minimize_btn) except Exception as e: print(f" خطأ في إنشاء أزرار التحكم: {e}") def setup_window_dragging(self): """إعداد وظيفة سحب النافذة من شريط العنوان""" try: self.custom_title_bar.mousePressEvent = self.title_bar_mouse_press self.custom_title_bar.mouseMoveEvent = self.title_bar_mouse_move self.custom_title_bar.mouseReleaseEvent = self.title_bar_mouse_release except Exception as e: print(f" خطأ في إعداد وظيفة السحب: {e}") def title_bar_mouse_press(self, event): try: if event.button() == Qt.LeftButton: self.dragging = True self.drag_position = event.globalPos() - self.frameGeometry().topLeft() event.accept() except Exception as e: print(f" خطأ في بداية السحب: {e}") def title_bar_mouse_move(self, event): """سحب النافذة""" try: if event.buttons() == Qt.LeftButton and self.dragging and self.drag_position: self.move(event.globalPos() - self.drag_position) event.accept() except Exception as e: print(f" خطأ في السحب: {e}") def title_bar_mouse_release(self, event): try: if event.button() == Qt.LeftButton: self.dragging = False self.drag_position = None event.accept() except Exception as e: print(f" خطأ في انتهاء السحب: {e}") def create_header_section(self, main_layout): """إنشاء قسم العنوان المتطور""" header_widget = QWidget() header_widget.setFixedHeight(50) # تقليل الارتفاع أكثر لرفع العنوان QWidget { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4a90e2, stop:0.1 #5ba3f5, stop:0.2 #6bb6ff, stop:0.3 #7bc9ff, stop:0.4 #8bdcff, stop:0.5 #9befff, stop:0.6 #a8e6cf, stop:0.7 #b5f3e0, stop:0.8 #c2fff1, stop:0.9 #d0ffff, stop:1 #e0f7fa); border-radius: 20px; margin-bottom: 10px; /* تقليل المسافة السفلية */ border: 3px solid rgba(255, 255, 255, 0.9); box-shadow: 0 15px 40px rgba(74, 144, 226, 0.2); } """) header_layout = QVBoxLayout(header_widget) header_layout.setContentsMargins(15, 10, 15, 10) # تقليل المسافات لرفع العنوان header_layout.setSpacing(5) # تقليل المسافة بين العناصر # العنوان الرئيسي title = " إضافة رقم هاتف جديد" if not self.phone else " تعديل رقم الهاتف" title_label = QLabel(title) QLabel { color: white; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; /* تقليل حجم الخط */ font-weight: bold; background: transparent; border: none; padding: 2px; /* تقليل الحشو */ min-height: 20px; /* تقليل الارتفاع الأدنى */ } """) title_label.setAlignment(Qt.AlignCenter) header_layout.addWidget(title_label) # العنوان الفرعي subtitle = "أدخل بيانات رقم الهاتف بعناية" if not self.phone else f"تعديل رقم الهاتف: {self.phone.phone_number if self.phone else ''}" subtitle_label = QLabel(subtitle) QLabel { color: rgba(255, 255, 255, 0.9); font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 11px; /* تقليل حجم الخط */ font-weight: normal; background: transparent; border: none; padding: 1px; /* تقليل الحشو */ min-height: 15px; /* تقليل الارتفاع الأدنى */ } """) subtitle_label.setAlignment(Qt.AlignCenter) header_layout.addWidget(subtitle_label) main_layout.addWidget(header_widget) def create_form_section(self, main_layout): form_widget = QWidget() form_widget.setStyleSheet(""" QWidget { background: #ffffff; border: none; padding: 20px; } form_layout = QFormLayout() form_layout.setContentsMargins(25, 25, 25, 25) # زيادة المسافات form_layout.setSpacing(30) # زيادة المسافة بين الحقول أكثر form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter) form_layout.setHorizontalSpacing(35) # زيادة المسافة الأفقية form_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow) # خط العناوين المحسن label_font = QFont('Segoe UI', 14, QFont.Bold) # زيادة حجم الخط # نمط الحقول المتطور input_style = """ QLineEdit { padding: 18px 20px; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; border: 2px solid #e1e8ed; border-radius: 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa); min-height: 35px; max-height: 50px; min-width: 280px; selection-background-color: #4285f4; selection-color: white; } QLineEdit:focus { border: 2px solid #4285f4; background: white; } QLineEdit:hover { border: 2px solid #90caf9; background: #fafafa; } QRadioButton { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; font-weight: bold; color: #2c3e50; padding: 8px; spacing: 10px; } QRadioButton::indicator { width: 18px; height: 18px; border-radius: 9px; border: 2px solid #4285f4; background: white; } QRadioButton::indicator:checked { background: qradialgradient(cx:0.5, cy:0.5, radius:0.5, stop:0 #4285f4, stop:0.7 #4285f4, stop:1 white); border: 2px solid #1976d2; } QRadioButton::indicator:hover { border: 2px solid #90caf9; } # حقل رقم الهاتف مع أيقونة phone_container = QWidget() phone_layout = QHBoxLayout(phone_container) phone_layout.setContentsMargins(0, 0, 0, 0) phone_layout.setSpacing(10) phone_icon = QLabel("") phone_icon.setStyleSheet(""" QLabel { font-size: 18px; color: #4285f4; margin-right: 5px; font-family: 'Segoe UI Emoji', 'Noto Color Emoji', 'Apple Color Emoji', sans-serif; background: transparent; border: none; padding: 2px; min-width: 25px; max-width: 25px; } phone_icon.setAlignment(Qt.AlignCenter) phone_icon.setFixedWidth(25) phone_layout.addWidget(phone_icon) self.phone_edit = QLineEdit() if self.phone: self.phone_edit.setText(self.phone.phone_number) self.phone_edit.setStyleSheet(input_style) self.phone_edit.setPlaceholderText("أدخل رقم الهاتف (مثال: 0501234567)") phone_layout.addWidget(self.phone_edit) phone_label = QLabel("رقم الهاتف:") phone_label.setFont(label_font) phone_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; font-weight: bold; min-width: 120px; padding: 5px;") phone_label.setMinimumWidth(120) # تقليل عرض العنوان form_layout.addRow(phone_label, phone_container) # حقل الوصف مع أيقونة label_container = QWidget() label_layout = QHBoxLayout(label_container) label_layout.setContentsMargins(0, 0, 0, 0) label_layout.setSpacing(10) label_icon = QLabel("") label_icon.setStyleSheet(""" QLabel { font-size: 18px; color: #4285f4; margin-right: 5px; font-family: 'Segoe UI Emoji', 'Noto Color Emoji', 'Apple Color Emoji', sans-serif; background: transparent; border: none; padding: 2px; min-width: 25px; max-width: 25px; } label_icon.setAlignment(Qt.AlignCenter) label_icon.setFixedWidth(25) label_layout.addWidget(label_icon) self.label_edit = QLineEdit() if self.phone and self.phone.label: self.label_edit.setText(self.phone.label) self.label_edit.setStyleSheet(input_style) self.label_edit.setPlaceholderText("أدخل وصف الرقم (مثال: شخصي، عمل، منزل)") label_layout.addWidget(self.label_edit) label_label = QLabel("الوصف:") label_label.setFont(label_font) label_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; font-weight: bold; min-width: 120px; padding: 5px;") label_label.setMinimumWidth(120) # تقليل عرض العنوان form_layout.addRow(label_label, label_container) # خيار الرقم الرئيسي primary_container = QWidget() primary_layout = QHBoxLayout(primary_container) primary_layout.setContentsMargins(0, 0, 0, 0) primary_layout.setSpacing(10) primary_icon = QLabel("") primary_icon.setStyleSheet(""" QLabel { font-size: 18px; color: #4285f4; margin-right: 5px; font-family: 'Segoe UI Emoji', 'Noto Color Emoji', 'Apple Color Emoji', sans-serif; background: transparent; border: none; padding: 2px; min-width: 25px; max-width: 25px; } primary_icon.setAlignment(Qt.AlignCenter) primary_icon.setFixedWidth(25) primary_layout.addWidget(primary_icon) self.is_primary = QRadioButton("رقم رئيسي") if self.phone and self.phone.is_primary: self.is_primary.setChecked(True) self.is_primary.setStyleSheet(input_style) primary_layout.addWidget(self.is_primary) # إضافة مساحة مرنة primary_layout.addStretch() primary_label = QLabel("النوع:") primary_label.setFont(label_font) primary_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; font-weight: bold; min-width: 120px; padding: 5px;") primary_label.setMinimumWidth(120) # تقليل عرض العنوان form_layout.addRow(primary_label, primary_container) form_widget.setLayout(form_layout) main_layout.addWidget(form_widget) def create_action_buttons(self, main_layout): """إنشاء أزرار الحفظ والإلغاء المحسنة""" button_widget = QWidget() button_layout = QHBoxLayout(button_widget) button_layout.setContentsMargins(0, 25, 0, 25) # زيادة المسافة العلوية والسفلية button_layout.setSpacing(20) # زيادة المسافة بين الأزرار # زر الحفظ save_button = QPushButton(" حفظ") save_button.clicked.connect(self.accept) QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #28a745, stop:1 #1e7e34); color: white; border: none; border-radius: 12px; padding: 15px 40px; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 17px; font-weight: bold; min-width: 150px; min-height: 50px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #34ce57, stop:1 #28a745); transform: translateY(-2px); } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1e7e34, stop:1 #155724); transform: translateY(0px); } """) button_layout.addWidget(save_button) # زر الإلغاء cancel_button = QPushButton(" إلغاء") cancel_button.clicked.connect(self.reject) QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #6c757d, stop:1 #495057); color: white; border: none; border-radius: 12px; padding: 15px 40px; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 17px; font-weight: bold; min-width: 150px; min-height: 50px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #7c8690, stop:1 #6c757d); transform: translateY(-2px); } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #495057, stop:1 #343a40); transform: translateY(0px); } """) button_layout.addWidget(cancel_button) main_layout.addWidget(button_widget) def validate_data(self): errors = [] # التحقق من الحقول المطلوبة phone_number = self.phone_edit.text().strip() if not phone_number: errors.append("رقم الهاتف مطلوب") # التحقق من صحة رقم الهاتف if phone_number and not is_valid_phone(phone_number): errors.append("رقم الهاتف غير صحيح") return errors def accept(self): """قبول النافذة مع التحقق من البيانات""" errors = self.validate_data() if errors: show_error_message("خطأ في البيانات", "\n".join(errors)) return super().accept() def get_data(self): phone_number = self.phone_edit.text().strip() label = self.label_edit.text().strip() is_primary = self.is_primary.isChecked() return { 'phone_number': phone_number, 'label': label, 'is_primary': is_primary } class BalanceAdjustmentDialog(QDialog): """نافذة حوار لتعديل المبلغ المستحق للعميل""" def __init__(self, parent=None, client=None): super().__init__(parent) self.setWindowTitle("تعديل المبلغ المستحق") self.setModal(True) self.setMinimumSize(400, 300) self.client = client # تطبيق نمط أساسي للنافذة QDialog { background-color: #f8f9fa; border: 2px solid #dee2e6; border-radius: 10px; } QLabel { color: #495057; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 12px; } QLineEdit { padding: 8px; border: 2px solid #ced4da; border-radius: 6px; background-color: white; font-size: 12px; } QLineEdit:focus { border-color: #007bff; } QPushButton { padding: 8px 16px; border: none; border-radius: 6px; font-weight: bold; font-size: 12px; } """) self.init_ui() def init_ui(self): # إنشاء التخطيط الرئيسي main_layout = QVBoxLayout() # عرض معلومات العميل client_info = QLabel(f"العميل: {self.client.name}") client_info main_layout.addWidget(client_info) current_balance = QLabel(f"المبلغ المستحق الحالي: {self.client.balance:.2f}") main_layout.addWidget(current_balance) # إضافة شرح للمبلغ المستحق balance_explanation = QLabel("ملاحظة: القيمة الموجبة تعني أن المبلغ للعميل، والقيمة السالبة تعني أن المبلغ على العميل") balance_explanation.setWordWrap(True) main_layout.addWidget(balance_explanation) # إنشاء مجموعة الخيارات operation_group = QGroupBox("نوع العملية") operation_layout = QVBoxLayout() self.add_radio = QRadioButton("إضافة مبلغ") self.add_radio.setChecked(True) self.subtract_radio = QRadioButton("خصم مبلغ") self.set_radio = QRadioButton("تعيين قيمة جديدة") operation_layout.addWidget(self.add_radio) operation_layout.addWidget(self.subtract_radio) operation_layout.addWidget(self.set_radio) operation_group.setLayout(operation_layout) main_layout.addWidget(operation_group) # إنشاء مجموعة أزرار الاختيار self.operation_group = QButtonGroup() self.operation_group.addButton(self.add_radio, 1) self.operation_group.addButton(self.subtract_radio, 2) self.operation_group.addButton(self.set_radio, 3) # حقل إدخال المبلغ amount_layout = QFormLayout() self.amount_edit = QLineEdit() self.amount_edit.setPlaceholderText("أدخل المبلغ") amount_layout.addRow("المبلغ:", self.amount_edit) main_layout.addLayout(amount_layout) # أزرار الحفظ والإلغاء باستخدام النمط الموحد button_layout = QHBoxLayout() button_layout.setSpacing(10) save_button = QPushButton(" تطبيق") save_button.clicked.connect(self.accept) save_button.setStyleSheet(UnifiedStyles.get_button_style("success", "normal")) cancel_button = QPushButton(" إلغاء") cancel_button.clicked.connect(self.reject) cancel_button.setStyleSheet(UnifiedStyles.get_button_style("secondary", "normal")) button_layout.addWidget(save_button) button_layout.addWidget(cancel_button) main_layout.addLayout(button_layout) self.setLayout(main_layout) def validate_data(self): errors = [] # التحقق من أن حقل المبلغ ليس فارغاً amount_text = self.amount_edit.text().strip() if not amount_text: errors.append("يرجى إدخال المبلغ") else: # التحقق من صحة المبلغ try: amount = float(amount_text) if amount < 0: errors.append("المبلغ يجب أن يكون صفر أو أكبر") except ValueError: errors.append("المبلغ يجب أن يكون رقماً صحيحاً") return errors def get_data(self): """الحصول على بيانات التعديل""" # التحقق من صحة البيانات أولاً errors = self.validate_data() if errors: QMessageBox.warning(self, "خطأ في البيانات", "\n".join(errors)) return None # تحويل النص إلى رقم بعد التأكد من صحته amount_text = self.amount_edit.text().strip() if not amount_text: QMessageBox.warning(self, "خطأ", "يرجى إدخال المبلغ") return None try: amount = float(amount_text) except ValueError: QMessageBox.warning(self, "خطأ", "المبلغ المدخل غير صحيح") return None if amount < 0: QMessageBox.warning(self, "خطأ", "المبلغ يجب أن يكون صفر أو أكبر") return None operation_id = self.operation_group.checkedId() if operation_id == 1: # إضافة مبلغ return {'amount': amount, 'operation': 'add'} elif operation_id == 2: # خصم مبلغ return {'amount': amount, 'operation': 'subtract'} elif operation_id == 3: # تعيين قيمة جديدة current_balance = self.client.balance # حساب الفرق بين القيمة الحالية والقيمة الجديدة if amount == current_balance: # لا يوجد تغيير return {'amount': 0, 'operation': 'add'} elif amount > current_balance: return {'amount': amount - current_balance, 'operation': 'add'} else: return {'amount': current_balance - amount, 'operation': 'subtract'} return None class ClientDialog(QDialog): def __init__(self, session, client=None): super().__init__(None) self.client = client self.session = session self.phones = [] # قائمة أرقام الهواتف # إزالة شريط العنوان الافتراضي تماماً self.setWindowFlags(Qt.Window | Qt.FramelessWindowHint) self.setAttribute(Qt.WA_TranslucentBackground, False) # تحسين حجم النافذة وموضعها - زيادة قليلة لضمان ظهور جميع الأزرار self.setMinimumSize(1100, 780) self.setMaximumSize(1400, 980) self.resize(1200, 880) # متغيرات لتتبع السحب self.dragging = False self.drag_position = None self.init_ui() self.load_phones() # توسيط النافذة على الشاشة self.center_on_screen() def center_on_screen(self): """توسيط النافذة على الشاشة""" screen = QApplication.desktop().screenGeometry() size = self.geometry() self.move( (screen.width() - size.width()) // 2, (screen.height() - size.height()) // 2 ) def init_ui(self): # تعيين خلفية النافذة مع إطار مربع بسيط بألوان شريط العنوان self.setStyleSheet(""" QDialog { background: #f5f5f5; border: 4px solid #4facfe; border-radius: 0px; } # إنشاء التخطيط الرئيسي مع تحسينات بصرية main_layout = QVBoxLayout() main_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للشريط main_layout.setSpacing(0) # إضافة شريط العنوان المتقدم أولاً self.create_custom_title_bar(main_layout) # إضافة المحتوى الرئيسي content_widget = QWidget() content_layout = QVBoxLayout(content_widget) content_layout.setContentsMargins(25, 25, 25, 25) content_layout.setSpacing(20) # إضافة عنوان النافذة المتطور self.create_header_section(content_layout) # إنشاء تخطيط مقسم مع تحسينات splitter = QSplitter(Qt.Horizontal) splitter.setHandleWidth(8) splitter.setStyleSheet(""" QSplitter::handle { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #e3f2fd, stop:0.5 #2196f3, stop:1 #e3f2fd); border-radius: 4px; margin: 2px; } QSplitter::handle:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #bbdefb, stop:0.5 #1976d2, stop:1 #bbdefb); } # الجزء الأيمن: معلومات العميل المحسنة client_info_widget = self.create_client_info_section() splitter.addWidget(client_info_widget) # الجزء الأيسر: أرقام الهواتف المحسنة phones_widget = self.create_phones_section() splitter.addWidget(phones_widget) # ضبط أحجام المقسم - تخصيص مساحة أكبر لحقول البيانات مع استغلال الطول الإضافي splitter.setSizes([750, 450]) # زيادة كلا القسمين لاستغلال الطول الإضافي content_layout.addWidget(splitter) # أزرار الحفظ والإلغاء المحسنة self.create_action_buttons(content_layout) # إضافة المحتوى للتخطيط الرئيسي main_layout.addWidget(content_widget) self.setLayout(main_layout) def create_custom_title_bar(self, main_layout): """إنشاء شريط عنوان مخصص متطور مع تصميم احترافي""" try: # إنشاء شريط العنوان المخصص self.custom_title_bar = QWidget() self.custom_title_bar.setObjectName("customTitleBar") self.custom_title_bar.setFixedHeight(30) # تقليل الارتفاع self.custom_title_bar.setMinimumHeight(30) self.custom_title_bar.setMaximumHeight(30) # أخذ العرض الكامل للنافذة self.custom_title_bar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) # تطبيق النمط الموحد الكامل للشريط مع أولوية عالية QWidget#customTitleBar { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #1a1a2e, stop:0.1 #16213e, stop:0.15 #0f3460, stop:0.25 #533483, stop:0.35 #7209b7, stop:0.45 #a663cc, stop:0.55 #4facfe, stop:0.65 #00f2fe, stop:0.75 #43e97b, stop:0.85 #38f9d7, stop:0.95 #667eea, stop:1 #764ba2) !important; border: none !important; padding: 4px 12px !important; margin: 0px !important; min-height: 30px !important; max-height: 30px !important; border-radius: 0px !important; } QWidget#customTitleBar QLabel { color: white !important; font-family: 'Segoe UI', 'Arial', sans-serif !important; font-weight: bold !important; background: transparent !important; border: none !important; padding: 2px 8px !important; margin: 0px !important; } QWidget#customTitleBar QLabel#centerTitle { font-size: 12px !important; font-weight: 900 !important; text-align: center !important; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 215, 0, 0.5), stop:0.2 rgba(255, 193, 7, 0.4), stop:0.4 rgba(255, 255, 255, 0.3), stop:0.6 rgba(255, 193, 7, 0.4), stop:0.8 rgba(255, 215, 0, 0.5), stop:1 rgba(255, 235, 59, 0.3)) !important; border: none !important; border-radius: 15px !important; } QWidget#customTitleBar QPushButton { background: rgba(255, 255, 255, 0.1) !important; border: 1px solid rgba(255, 255, 255, 0.2) !important; border-radius: 8px !important; color: white !important; font-weight: bold !important; font-size: 12px !important; padding: 4px 8px !important; margin: 2px !important; min-width: 25px !important; max-width: 25px !important; min-height: 20px !important; max-height: 20px !important; } QWidget#customTitleBar QPushButton:hover { background: rgba(255, 255, 255, 0.2) !important; border: 1px solid rgba(255, 255, 255, 0.4) !important; } QWidget#customTitleBar QPushButton:pressed { background: rgba(255, 255, 255, 0.3) !important; } QWidget#customTitleBar QPushButton#closeButton:hover { background: rgba(244, 67, 54, 0.8) !important; border: 1px solid rgba(244, 67, 54, 1) !important; } """) # إنشاء التخطيط الأفقي للشريط title_layout = QHBoxLayout(self.custom_title_bar) title_layout.setContentsMargins(8, 2, 8, 2) title_layout.setSpacing(8) # إضافة أزرار التحكم في النافذة (في أقصى اليسار) self.create_window_control_buttons(title_layout) # مساحة مرنة title_layout.addStretch() # العنوان المركزي title_text = " إضافة عميل جديد" if not self.client else f" تعديل بيانات العميل: {self.client.name}" self.center_title_label = QLabel(title_text) self.center_title_label.setObjectName("centerTitle") self.center_title_label.setAlignment(Qt.AlignCenter) self.center_title_label.setMinimumWidth(200) # تقليل العرض title_layout.addWidget(self.center_title_label) # مساحة مرنة title_layout.addStretch() # إضافة وظائف السحب للنافذة self.setup_window_dragging() # إضافة الشريط للتخطيط الرئيسي main_layout.addWidget(self.custom_title_bar) except Exception as e: print(f" خطأ في إنشاء شريط العنوان المخصص: {e}") def create_window_control_buttons(self, layout): try: # زر الإغلاق (أولاً) self.close_btn = QPushButton("") self.close_btn.setObjectName("closeButton") self.close_btn.setToolTip("إغلاق النافذة") self.close_btn.clicked.connect(self.reject) layout.addWidget(self.close_btn) # زر التصغير (ثانياً) self.minimize_btn = QPushButton("") self.minimize_btn.setToolTip("تصغير النافذة") self.minimize_btn.clicked.connect(self.showMinimized) layout.addWidget(self.minimize_btn) except Exception as e: print(f" خطأ في إنشاء أزرار التحكم: {e}") def setup_window_dragging(self): """إعداد وظيفة سحب النافذة من شريط العنوان""" try: # تطبيق أحداث الماوس على شريط العنوان self.custom_title_bar.mousePressEvent = self.title_bar_mouse_press self.custom_title_bar.mouseMoveEvent = self.title_bar_mouse_move self.custom_title_bar.mouseReleaseEvent = self.title_bar_mouse_release except Exception as e: print(f" خطأ في إعداد وظيفة السحب: {e}") def title_bar_mouse_press(self, event): try: if event.button() == Qt.LeftButton: self.dragging = True self.drag_position = event.globalPos() - self.frameGeometry().topLeft() event.accept() except Exception as e: print(f" خطأ في بداية السحب: {e}") def title_bar_mouse_move(self, event): """سحب النافذة""" try: if event.buttons() == Qt.LeftButton and self.dragging and self.drag_position: self.move(event.globalPos() - self.drag_position) event.accept() except Exception as e: print(f" خطأ في السحب: {e}") def title_bar_mouse_release(self, event): try: if event.button() == Qt.LeftButton: self.dragging = False self.drag_position = None event.accept() except Exception as e: print(f" خطأ في انتهاء السحب: {e}") def create_header_section(self, main_layout): """إنشاء قسم العنوان المتطور""" header_widget = QWidget() header_widget.setFixedHeight(50) # تقليل الارتفاع أكثر لرفع العنوان QWidget { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4a90e2, stop:0.1 #5ba3f5, stop:0.2 #6bb6ff, stop:0.3 #7bc9ff, stop:0.4 #8bdcff, stop:0.5 #9befff, stop:0.6 #a8e6cf, stop:0.7 #b5f3e0, stop:0.8 #c2fff1, stop:0.9 #d0ffff, stop:1 #e0f7fa); border-radius: 25px; margin-bottom: 10px; /* تقليل المسافة السفلية */ border: 5px solid rgba(255, 255, 255, 0.9); box-shadow: 0 20px 60px rgba(74, 144, 226, 0.25); } """) header_layout = QVBoxLayout(header_widget) header_layout.setContentsMargins(15, 10, 15, 10) # تقليل المسافات لرفع العنوان header_layout.setSpacing(5) # تقليل المسافة بين العناصر # العنوان الرئيسي title = " إضافة عميل جديد" if not self.client else " تعديل بيانات العميل" title_label = QLabel(title) QLabel { color: white; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; /* تقليل حجم الخط */ font-weight: bold; background: transparent; border: none; padding: 2px; /* تقليل الحشو */ min-height: 20px; /* تقليل الارتفاع الأدنى */ } """) title_label.setAlignment(Qt.AlignCenter) header_layout.addWidget(title_label) # العنوان الفرعي subtitle = "أدخل بيانات العميل الجديد بعناية" if not self.client else f"تعديل بيانات العميل: {self.client.name}" subtitle_label = QLabel(subtitle) QLabel { color: rgba(255, 255, 255, 0.9); font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 11px; /* تقليل حجم الخط */ font-weight: normal; background: transparent; border: none; padding: 1px; /* تقليل الحشو */ min-height: 15px; /* تقليل الارتفاع الأدنى */ } """) subtitle_label.setAlignment(Qt.AlignCenter) header_layout.addWidget(subtitle_label) main_layout.addWidget(header_widget) def create_client_info_section(self): client_info_widget = QWidget() client_info_widget.setStyleSheet(""" QWidget { background: #ffffff; border: none; padding: 25px; } form_layout = QFormLayout() form_layout.setContentsMargins(20, 25, 20, 25) # هوامش متوسطة form_layout.setSpacing(22) # مسافة متوسطة بين الحقول form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter) # توسيع عرض العناوين لتظهر كاملة form_layout.setRowWrapPolicy(QFormLayout.DontWrapRows) form_layout.setHorizontalSpacing(30) # مسافة أفقية أكبر بين العنوان والحقل form_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow) # خط العناوين المحسن label_font = QFont('Segoe UI', 14, QFont.Bold) # نمط الحقول المتطور input_style = """ QLineEdit { padding: 12px 15px; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; border: 2px solid #e1e8ed; border-radius: 10px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa); min-height: 20px; selection-background-color: #4285f4; selection-color: white; } QLineEdit:focus { border: 2px solid #4285f4; background: white; } QLineEdit:hover { border: 2px solid #90caf9; background: #fafafa; } QTextEdit { padding: 12px 15px; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; border: 2px solid #e1e8ed; border-radius: 10px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f8f9fa); min-height: 120px; max-height: 200px; selection-background-color: #4285f4; selection-color: white; } QTextEdit:focus { border: 2px solid #4285f4; background: white; } QTextEdit:hover { border: 2px solid #90caf9; background: #fafafa; } # إنشاء الحقول مع التحسينات self.create_form_fields(form_layout, label_font, input_style) client_info_widget.setLayout(form_layout) return client_info_widget def create_form_fields(self, form_layout, label_font, input_style): """إنشاء حقول النموذج مع التحسينات""" # حقل الاسم مع أيقونة name_container = QWidget() name_layout = QHBoxLayout(name_container) name_layout.setContentsMargins(0, 0, 0, 0) name_layout.setSpacing(10) name_icon = QLabel("") QLabel { font-size: 20px; color: #4285f4; margin-right: 5px; font-family: 'Segoe UI Emoji', 'Noto Color Emoji', 'Apple Color Emoji', sans-serif; background: transparent; border: none; padding: 2px; min-width: 30px; max-width: 30px; } """) name_icon.setAlignment(Qt.AlignCenter) name_icon.setFixedWidth(30) # عرض ثابت للأيقونة name_layout.addWidget(name_icon) self.name_edit = QLineEdit() if self.client: self.name_edit.setText(self.client.name) self.name_edit.setStyleSheet(input_style) self.name_edit.setPlaceholderText("أدخل اسم العميل الكامل") name_layout.addWidget(self.name_edit) name_label = QLabel("الاسم:") name_label.setFont(label_font) name_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; font-weight: bold; min-width: 150px; padding: 5px;") name_label.setMinimumWidth(150) name_label.setWordWrap(False) form_layout.addRow(name_label, name_container) # حقل رقم الهاتف الرئيسي مع أيقونة phone_container = QWidget() phone_layout = QHBoxLayout(phone_container) phone_layout.setContentsMargins(0, 0, 0, 0) phone_layout.setSpacing(10) phone_icon = QLabel("") QLabel { font-size: 20px; color: #4285f4; margin-right: 5px; font-family: 'Segoe UI Emoji', 'Noto Color Emoji', 'Apple Color Emoji', sans-serif; background: transparent; border: none; padding: 2px; min-width: 30px; max-width: 30px; } """) phone_icon.setAlignment(Qt.AlignCenter) phone_icon.setFixedWidth(30) # عرض ثابت للأيقونة phone_layout.addWidget(phone_icon) self.phone_edit = QLineEdit() if self.client and self.client.phone: self.phone_edit.setText(self.client.phone) self.phone_edit.setStyleSheet(input_style) self.phone_edit.setPlaceholderText("أدخل رقم الهاتف الرئيسي") phone_layout.addWidget(self.phone_edit) phone_label = QLabel("رقم الهاتف الرئيسي:") phone_label.setFont(label_font) phone_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; font-weight: bold; min-width: 150px; padding: 5px;") phone_label.setMinimumWidth(150) phone_label.setWordWrap(False) form_layout.addRow(phone_label, phone_container) # حقل البريد الإلكتروني مع أيقونة email_container = QWidget() email_layout = QHBoxLayout(email_container) email_layout.setContentsMargins(0, 0, 0, 0) email_layout.setSpacing(10) email_icon = QLabel("") QLabel { font-size: 20px; color: #4285f4; margin-right: 5px; font-family: 'Segoe UI Emoji', 'Noto Color Emoji', 'Apple Color Emoji', sans-serif; background: transparent; border: none; padding: 2px; min-width: 30px; max-width: 30px; } """) email_icon.setAlignment(Qt.AlignCenter) email_icon.setFixedWidth(30) # عرض ثابت للأيقونة email_layout.addWidget(email_icon) self.email_edit = QLineEdit() if self.client and self.client.email: self.email_edit.setText(self.client.email) self.email_edit.setStyleSheet(input_style) self.email_edit.setPlaceholderText("أدخل البريد الإلكتروني") email_layout.addWidget(self.email_edit) email_label = QLabel("البريد الإلكتروني:") email_label.setFont(label_font) email_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; font-weight: bold; min-width: 150px; padding: 5px;") email_label.setMinimumWidth(150) email_label.setWordWrap(False) form_layout.addRow(email_label, email_container) # حقل العنوان مع أيقونة address_container = QWidget() address_layout = QHBoxLayout(address_container) address_layout.setContentsMargins(0, 0, 0, 0) address_layout.setSpacing(10) address_icon = QLabel("") QLabel { font-size: 20px; color: #4285f4; margin-right: 5px; font-family: 'Segoe UI Emoji', 'Noto Color Emoji', 'Apple Color Emoji', sans-serif; background: transparent; border: none; padding: 2px; min-width: 30px; max-width: 30px; } """) address_icon.setAlignment(Qt.AlignCenter) address_icon.setFixedWidth(30) # عرض ثابت للأيقونة address_layout.addWidget(address_icon) self.address_edit = QLineEdit() if self.client and self.client.address: self.address_edit.setText(self.client.address) self.address_edit.setStyleSheet(input_style) self.address_edit.setPlaceholderText("أدخل العنوان الكامل") address_layout.addWidget(self.address_edit) address_label = QLabel("العنوان:") address_label.setFont(label_font) address_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; font-weight: bold; min-width: 150px; padding: 5px;") address_label.setMinimumWidth(150) address_label.setWordWrap(False) form_layout.addRow(address_label, address_container) # حقل الرصيد الابتدائي مع أيقونة balance_container = QWidget() balance_layout = QHBoxLayout(balance_container) balance_layout.setContentsMargins(0, 0, 0, 0) balance_layout.setSpacing(10) balance_icon = QLabel("") QLabel { font-size: 20px; color: #4285f4; margin-right: 5px; font-family: 'Segoe UI Emoji', 'Noto Color Emoji', 'Apple Color Emoji', sans-serif; background: transparent; border: none; padding: 2px; min-width: 30px; max-width: 30px; } """) balance_icon.setAlignment(Qt.AlignCenter) balance_icon.setFixedWidth(30) # عرض ثابت للأيقونة balance_layout.addWidget(balance_icon) self.balance_edit = QLineEdit() if self.client and hasattr(self.client, 'balance'): self.balance_edit.setText(str(self.client.balance)) else: self.balance_edit.setText("0.0") self.balance_edit.setStyleSheet(input_style) self.balance_edit.setPlaceholderText("أدخل الرصيد الابتدائي (اختياري)") balance_layout.addWidget(self.balance_edit) balance_label = QLabel("الرصيد الابتدائي:") balance_label.setFont(label_font) balance_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; font-weight: bold; min-width: 150px; padding: 5px;") balance_label.setMinimumWidth(150) balance_label.setWordWrap(False) form_layout.addRow(balance_label, balance_container) # حقل الملاحظات مع أيقونة notes_container = QWidget() notes_layout = QHBoxLayout(notes_container) notes_layout.setContentsMargins(0, 0, 0, 0) notes_layout.setSpacing(10) notes_icon = QLabel("") QLabel { font-size: 20px; color: #4285f4; margin-right: 5px; margin-top: 10px; font-family: 'Segoe UI Emoji', 'Noto Color Emoji', 'Apple Color Emoji', sans-serif; background: transparent; border: none; padding: 2px; min-width: 30px; max-width: 30px; } """) notes_icon.setAlignment(Qt.AlignCenter) notes_icon.setFixedWidth(30) # عرض ثابت للأيقونة notes_layout.addWidget(notes_icon) self.notes_edit = QTextEdit() if self.client and self.client.notes: self.notes_edit.setText(self.client.notes) self.notes_edit.setStyleSheet(input_style) self.notes_edit.setPlaceholderText("أدخل ملاحظات إضافية حول العميل...") notes_layout.addWidget(self.notes_edit) notes_label = QLabel("ملاحظات:") notes_label.setFont(label_font) notes_label.setStyleSheet("color: #2c3e50; margin-bottom: 5px; font-weight: bold; min-width: 150px; padding: 5px;") notes_label.setMinimumWidth(150) notes_label.setWordWrap(False) form_layout.addRow(notes_label, notes_container) def create_phones_section(self): phones_widget = QWidget() phones_widget.setMaximumWidth(380) phones_widget.setStyleSheet(""" QWidget { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e8f5e8, stop:0.1 #f0fff0, stop:0.2 #f5fffa, stop:0.3 #e0f2e0, stop:0.4 #d4edda, stop:0.5 #c3e6cb, stop:0.6 #b1dfbb, stop:0.7 #a3d9a5, stop:0.8 #95d5b2, stop:0.9 #d4edda, stop:1 #f0fff0); border: 5px solid rgba(255, 255, 255, 0.9); border-radius: 25px; padding: 25px; box-shadow: 0 25px 60px rgba(0, 0, 0, 0.1); } phones_layout = QVBoxLayout() phones_layout.setContentsMargins(20, 25, 20, 25) # هوامش متوسطة phones_layout.setSpacing(18) # مسافة متوسطة بين العناصر # عنوان قسم أرقام الهواتف الجديد المحسن phones_header = QWidget() phones_header.setFixedHeight(45) # ارتفاع مناسب phones_header.setStyleSheet(""" QWidget { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #28a745, stop:0.1 #34ce57, stop:0.2 #40d869, stop:0.3 #4ce27b, stop:0.4 #58ec8d, stop:0.5 #64f69f, stop:0.6 #70ffb1, stop:0.7 #7cffc3, stop:0.8 #88ffd5, stop:0.9 #94ffe7, stop:1 #a0fff9); border-radius: 18px; margin-bottom: 18px; border: 5px solid rgba(255, 255, 255, 0.95); box-shadow: 0 15px 35px rgba(40, 167, 69, 0.2); } # تخطيط مركزي للعنوان header_layout = QVBoxLayout(phones_header) header_layout.setContentsMargins(0, 0, 0, 0) header_layout.setSpacing(0) # إضافة مساحة مرنة في الأعلى header_layout.addStretch() # تخطيط أفقي للأيقونة والنص content_layout = QHBoxLayout() content_layout.setContentsMargins(0, 0, 0, 0) content_layout.setSpacing(6) # إضافة مساحة مرنة في البداية content_layout.addStretch() # الأيقونة phones_icon = QLabel("") phones_icon.setStyleSheet(""" QLabel { font-size: 18px; background: transparent; color: white; } phones_icon.setAlignment(Qt.AlignCenter) content_layout.addWidget(phones_icon) # النص phones_title = QLabel("أرقام الهواتف") phones_title.setStyleSheet(""" QLabel { color: white; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; font-weight: bold; background: transparent; border: none; } phones_title.setAlignment(Qt.AlignCenter) content_layout.addWidget(phones_title) # إضافة مساحة مرنة في النهاية content_layout.addStretch() # إضافة التخطيط الأفقي للتخطيط الرئيسي header_layout.addLayout(content_layout) # إضافة مساحة مرنة في الأسفل header_layout.addStretch() phones_layout.addWidget(phones_header) # قائمة أرقام الهواتف المحسنة - زيادة الارتفاع self.phones_list = QListWidget() self.phones_list.setStyleSheet(""" QListWidget { background: white; border: 2px solid #e9ecef; border-radius: 10px; padding: 10px; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; selection-background-color: #4285f4; selection-color: white; } QListWidget::item { padding: 8px; margin: 2px; border-radius: 6px; border: 1px solid transparent; } QListWidget::item:hover { background: #f8f9fa; border: 1px solid #dee2e6; } QListWidget::item:selected { background: #4285f4; color: white; border: 1px solid #1976d2; } self.phones_list.setAlternatingRowColors(False) self.phones_list.setMinimumHeight(220) # تقليل ارتفاع القائمة لإفساح مجال للأزرار self.phones_list.setMaximumHeight(300) # تقليل الحد الأقصى phones_layout.addWidget(self.phones_list) # أزرار إدارة أرقام الهواتف المحسنة self.create_phone_buttons(phones_layout) phones_widget.setLayout(phones_layout) return phones_widget def create_phone_buttons(self, phones_layout): """إنشاء أزرار إدارة أرقام الهواتف المحسنة""" buttons_container = QWidget() QWidget { background: transparent; border: none; } """) buttons_layout = QVBoxLayout(buttons_container) buttons_layout.setContentsMargins(0, 15, 0, 10) # تقليل المسافة العلوية وإضافة مسافة سفلية buttons_layout.setSpacing(12) # تقليل المسافة بين الأزرار قليلاً # تصميم الأزرار المتطور QPushButton { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; font-weight: bold; padding: 12px 20px; border-radius: 8px; border: 2px solid transparent; min-height: 20px; text-align: center; } QPushButton:hover { border: 2px solid rgba(255, 255, 255, 0.3); } QPushButton:pressed { /* خصائص مدعومة في PyQt5 فقط */ } """ # زر إضافة رقم - محسن ومرفوع self.add_phone_button = QPushButton(" إضافة رقم") QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #66bb6a, stop:1 #a5d6a7); color: white; border: 2px solid #4caf50; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5cb85c, stop:1 #92c5f7); border: 2px solid #43a047; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4caf50, stop:1 #81c784); } """) self.add_phone_button.clicked.connect(self.add_phone) buttons_layout.addWidget(self.add_phone_button) # زر تعديل رقم - محسن ومرفوع self.edit_phone_button = QPushButton(" تعديل رقم") QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #42a5f5, stop:1 #90caf9); color: white; border: 2px solid #2196f3; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1e88e5, stop:1 #7bb3f0); border: 2px solid #1976d2; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1976d2, stop:1 #64b5f6); } """) self.edit_phone_button.clicked.connect(self.edit_phone) buttons_layout.addWidget(self.edit_phone_button) # زر حذف رقم - محسن ومرفوع self.delete_phone_button = QPushButton(" حذف رقم") QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef5350, stop:1 #ffab91); color: white; border: 2px solid #f44336; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #e53935, stop:1 #ff8a65); border: 2px solid #d32f2f; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #d32f2f, stop:1 #ff7043); } """) self.delete_phone_button.clicked.connect(self.delete_phone) buttons_layout.addWidget(self.delete_phone_button) # زر عرض التفاصيل - محسن ومرفوع self.view_phone_button = QPushButton(" عرض التفاصيل") QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffa726, stop:1 #ffcc80); color: white; border: 2px solid #ff9800; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fb8c00, stop:1 #ffb74d); border: 2px solid #f57c00; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f57c00, stop:1 #ff9800); } """) self.view_phone_button.clicked.connect(self.view_phone_details) buttons_layout.addWidget(self.view_phone_button) phones_layout.addWidget(buttons_container) def create_action_buttons(self, main_layout): buttons_container = QWidget() buttons_container.setFixedHeight(80) buttons_container.setStyleSheet(""" QWidget { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.1 #f8f9fa, stop:0.2 #f1f3f4, stop:0.3 #e8eaed, stop:0.4 #e1e3e6, stop:0.5 #dadce0, stop:0.6 #d3d5d9, stop:0.7 #ced0d4, stop:0.8 #c9cbcf, stop:0.9 #e1e3e6, stop:1 #f1f3f4); border: 5px solid rgba(255, 255, 255, 0.95); border-radius: 28px; margin-top: 25px; box-shadow: 0 25px 60px rgba(0, 0, 0, 0.1); } # تخطيط رئيسي للتوسيط الكامل main_buttons_layout = QVBoxLayout(buttons_container) main_buttons_layout.setContentsMargins(0, 0, 0, 0) main_buttons_layout.setSpacing(0) # إضافة مساحة مرنة في الأعلى للتوسيط العمودي main_buttons_layout.addStretch() # تخطيط أفقي للأزرار buttons_layout = QHBoxLayout() buttons_layout.setContentsMargins(0, 0, 0, 0) buttons_layout.setSpacing(20) # إضافة مساحة مرنة في البداية للتوسيط الأفقي buttons_layout.addStretch() # زر الإلغاء - محسن ومرفوع مع عرض مضاعف 3 مرات self.cancel_button = QPushButton(" إلغاء العملية") self.cancel_button.setFixedWidth(540) # عرض مضاعف 3 مرات (180 × 3) self.cancel_button.setStyleSheet(""" QPushButton { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; font-weight: bold; padding: 12px 25px; border-radius: 15px; border: 4px solid rgba(255, 255, 255, 0.8); background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ff6b6b, stop:0.2 #ee5a52, stop:0.4 #ff4757, stop:0.6 #f44336, stop:0.8 #e53935, stop:1 #d32f2f); color: white; min-height: 18px; box-shadow: 0 8px 25px rgba(255, 107, 107, 0.6); text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ff5252, stop:0.2 #f44336, stop:0.4 #e53935, stop:0.6 #d32f2f, stop:0.8 #c62828, stop:1 #b71c1c); border: 4px solid rgba(255, 255, 255, 1.0); box-shadow: 0 12px 35px rgba(255, 82, 82, 0.8); transform: translateY(-2px); } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #d32f2f, stop:0.2 #c62828, stop:0.4 #b71c1c, stop:0.6 #a00000, stop:0.8 #8b0000, stop:1 #7b0000); box-shadow: 0 4px 15px rgba(211, 47, 47, 0.9); transform: translateY(1px); } self.cancel_button.clicked.connect(self.reject) buttons_layout.addWidget(self.cancel_button) # زر الحفظ - محسن ومرفوع مع عرض مضاعف 3 مرات self.save_button = QPushButton(" حفظ البيانات") self.save_button.setFixedWidth(540) # عرض مضاعف 3 مرات (180 × 3) self.save_button.setStyleSheet(""" QPushButton { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; font-weight: bold; padding: 12px 25px; border-radius: 15px; border: 4px solid rgba(255, 255, 255, 0.8); background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #00d2ff, stop:0.2 #3a7bd5, stop:0.4 #667eea, stop:0.6 #764ba2, stop:0.8 #43e97b, stop:1 #38f9d7); color: white; min-height: 18px; box-shadow: 0 8px 25px rgba(0, 210, 255, 0.6); text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0099cc, stop:0.2 #2980b9, stop:0.4 #4facfe, stop:0.6 #00f2fe, stop:0.8 #26a69a, stop:1 #00897b); border: 4px solid rgba(255, 255, 255, 1.0); box-shadow: 0 12px 35px rgba(0, 153, 204, 0.8); transform: translateY(-2px); } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2980b9, stop:0.2 #1f4e79, stop:0.4 #1976d2, stop:0.6 #0d47a1, stop:0.8 #00695c, stop:1 #004d40); box-shadow: 0 4px 15px rgba(41, 128, 185, 0.9); transform: translateY(1px); } self.save_button.clicked.connect(self.accept) buttons_layout.addWidget(self.save_button) # إضافة مساحة مرنة في النهاية للتوسيط الأفقي buttons_layout.addStretch() # إضافة التخطيط الأفقي للتخطيط الرئيسي main_buttons_layout.addLayout(buttons_layout) # إضافة مساحة مرنة في الأسفل للتوسيط العمودي main_buttons_layout.addStretch() main_layout.addWidget(buttons_container) def load_phones(self): """تحميل أرقام هواتف العميل""" self.phones_list.clear() self.phones = [] if self.client and self.session: # الحصول على أرقام هواتف العميل من قاعدة البيانات phone_records = self.session.query(ClientPhone).filter_by(client_id=self.client.id).all() for phone in phone_records: self.phones.append(phone) # إنشاء عنصر في القائمة label_text = f"{phone.phone_number}" if phone.label: label_text += f" ({phone.label})" if phone.is_primary: label_text += " [رئيسي]" item = QListWidgetItem(label_text) item.setData(Qt.UserRole, phone.id) self.phones_list.addItem(item) def add_phone(self): dialog = PhoneDialog(self) # تعيين موضع النافذة في وسط النافذة الأم if self.parent(): parent_geometry = self.parent().geometry() dialog_geometry = dialog.geometry() x = parent_geometry.x() + (parent_geometry.width() - dialog_geometry.width()) // 2 y = parent_geometry.y() + (parent_geometry.height() - dialog_geometry.height()) // 2 dialog.move(x, y) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # إضافة رقم الهاتف إلى القائمة المؤقتة phone = ClientPhone( phone_number=data['phone_number'], label=data['label'], is_primary=data['is_primary'] ) # إذا كان هذا الرقم رئيسياً، قم بإلغاء تعيين الأرقام الرئيسية الأخرى if data['is_primary']: for p in self.phones: p.is_primary = False self.phones.append(phone) # تحديث القائمة self.refresh_phones_list() # تحديث حقل رقم الهاتف الرئيسي إذا كان هذا هو الرقم الرئيسي if data['is_primary']: self.phone_edit.setText(data['phone_number']) def edit_phone(self): """تعديل رقم هاتف""" selected_items = self.phones_list.selectedItems() if not selected_items: show_error_message("خطأ", "الرجاء اختيار رقم هاتف من القائمة") return selected_index = self.phones_list.currentRow() if selected_index < 0 or selected_index >= len(self.phones): return phone = self.phones[selected_index] dialog = PhoneDialog(self, phone) # تعيين موضع النافذة في وسط النافذة الأم if self.parent(): parent_geometry = self.parent().geometry() dialog_geometry = dialog.geometry() x = parent_geometry.x() + (parent_geometry.width() - dialog_geometry.width()) // 2 y = parent_geometry.y() + (parent_geometry.height() - dialog_geometry.height()) // 2 dialog.move(x, y) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # تحديث بيانات رقم الهاتف old_phone_number = phone.phone_number phone.phone_number = data['phone_number'] phone.label = data['label'] # إذا كان هذا الرقم رئيسياً، قم بإلغاء تعيين الأرقام الرئيسية الأخرى if data['is_primary'] and not phone.is_primary: for p in self.phones: p.is_primary = False phone.is_primary = data['is_primary'] # تحديث القائمة self.refresh_phones_list() # تحديث حقل رقم الهاتف الرئيسي إذا كان هذا هو الرقم الرئيسي if data['is_primary']: self.phone_edit.setText(data['phone_number']) # إذا كان هذا الرقم هو الرقم الرئيسي سابقاً ولم يعد كذلك، قم بمسح حقل رقم الهاتف الرئيسي elif self.phone_edit.text() == old_phone_number and not data['is_primary']: self.phone_edit.clear() def view_phone_details(self): selected_items = self.phones_list.selectedItems() if not selected_items: show_error_message("خطأ", "الرجاء اختيار رقم هاتف من القائمة") return selected_index = self.phones_list.currentRow() if selected_index < 0 or selected_index >= len(self.phones): return phone = self.phones[selected_index] # إنشاء نافذة حوار لعرض التفاصيل details_dialog = QDialog(self) details_dialog.setWindowTitle("تفاصيل رقم الهاتف") details_dialog.setMinimumWidth(400) details_dialog.setMinimumHeight(300) # إنشاء تخطيط للنافذة layout = QVBoxLayout() # إنشاء نموذج للتفاصيل form_layout = QFormLayout() # تعيين نمط للعناوين label_font = QFont() label_font.setPointSize(14) label_font.setBold(True) # رقم الهاتف phone_number_label = QLabel("رقم الهاتف:") phone_number_label.setFont(label_font) phone_number_value = QLabel(phone.phone_number) phone_number_value form_layout.addRow(phone_number_label, phone_number_value) # الوصف description_label = QLabel("الوصف:") description_label.setFont(label_font) description_value = QLabel(phone.label or "غير متوفر") description_value form_layout.addRow(description_label, description_value) # النوع type_label = QLabel("النوع:") type_label.setFont(label_font) type_value = QLabel("رقم رئيسي" if phone.is_primary else "رقم إضافي") type_value.setStyleSheet("font-size: 16px; font-weight: bold; color: " + ("#4CAF50" if phone.is_primary else "#FF9800") + ";") form_layout.addRow(type_label, type_value) # إضافة النموذج إلى التخطيط layout.addLayout(form_layout) # زر الإغلاق بعرض القائمة close_button = QPushButton(" إغلاق") close_button close_button.clicked.connect(details_dialog.accept) layout.addStretch() layout.addWidget(close_button) details_dialog.setLayout(layout) details_dialog.exec_() def delete_phone(self): """حذف رقم هاتف""" selected_items = self.phones_list.selectedItems() if not selected_items: show_error_message("خطأ", "الرجاء اختيار رقم هاتف من القائمة") return selected_index = self.phones_list.currentRow() if selected_index < 0 or selected_index >= len(self.phones): return phone = self.phones[selected_index] # طلب تأكيد الحذف if not show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف رقم الهاتف {phone.phone_number}؟"): return # التحقق مما إذا كان هذا هو الرقم الرئيسي is_primary = phone.is_primary phone_number = phone.phone_number # حذف رقم الهاتف من القائمة المؤقتة self.phones.pop(selected_index) # إذا كان هذا هو الرقم الرئيسي، قم بتعيين رقم آخر كرقم رئيسي إذا كان متاحاً if is_primary and self.phones: self.phones[0].is_primary = True # تحديث حقل رقم الهاتف الرئيسي self.phone_edit.setText(self.phones[0].phone_number) # إذا كان هذا هو الرقم الرئيسي ولم يعد هناك أرقام أخرى، قم بمسح حقل رقم الهاتف الرئيسي elif is_primary: self.phone_edit.clear() # إذا كان هذا الرقم هو الرقم المعروض في حقل رقم الهاتف الرئيسي، قم بمسحه elif self.phone_edit.text() == phone_number: self.phone_edit.clear() # تحديث القائمة self.refresh_phones_list() def refresh_phones_list(self): self.phones_list.clear() for phone in self.phones: # إنشاء عنصر في القائمة label_text = f"{phone.phone_number}" if phone.label: label_text += f" ({phone.label})" item = QListWidgetItem(label_text) # تعيين محاذاة النص في المنتصف item.setTextAlignment(Qt.AlignCenter) # تعيين نمط خاص للرقم الرئيسي if phone.is_primary: item.setIcon(QIcon("icons/star.png")) # يمكن استخدام أيقونة نجمة إذا كانت متوفرة item.setText(f"{label_text} [رئيسي]") item.setForeground(QColor("#0078D7")) # لون أزرق للرقم الرئيسي font = item.font() font.setBold(True) font.setPointSize(17) # تعيين حجم الخط بشكل صريح item.setFont(font) else: # تعيين حجم الخط للعناصر العادية أيضاً font = item.font() font.setPointSize(17) item.setFont(font) # تخزين معرف الهاتف في البيانات المخصصة للعنصر if hasattr(phone, 'id') and phone.id: item.setData(Qt.UserRole, phone.id) # إضافة العنصر إلى القائمة self.phones_list.addItem(item) def validate_data(self): """التحقق من صحة البيانات""" errors = [] # التحقق من الحقول المطلوبة required_fields = [ ('name', self.name_edit, 'اسم العميل') ] errors.extend(self.validate_required_fields(required_fields)) # التحقق من صحة رقم الهاتف phone = self.phone_edit.text().strip() if phone and not is_valid_phone(phone): errors.append("رقم الهاتف غير صحيح") # التحقق من صحة البريد الإلكتروني email = self.email_edit.text().strip() if email and not is_valid_email(email): errors.append("البريد الإلكتروني غير صحيح") # التحقق من عدم تكرار الاسم name = self.name_edit.text().strip() if name and self.session: existing_client = self.session.query(Client).filter(Client.name == name).first() if existing_client and (not self.client or existing_client.id != self.client.id): errors.append("يوجد عميل آخر بنفس الاسم") # التحقق من عدم تكرار البريد الإلكتروني if email and self.session: existing_client = self.session.query(Client).filter(Client.email == email).first() if existing_client and (not self.client or existing_client.id != self.client.id): errors.append("يوجد عميل آخر بنفس البريد الإلكتروني") # التحقق من صحة أرقام الهواتف for phone in self.phones: if not is_valid_phone(phone.phone_number): errors.append(f"رقم الهاتف {phone.phone_number} غير صحيح") return errors def get_data(self): name = self.name_edit.text().strip() phone = self.phone_edit.text().strip() email = self.email_edit.text().strip() address = self.address_edit.text().strip() notes = self.notes_edit.toPlainText().strip() # الحصول على الرصيد الابتدائي balance_text = self.balance_edit.text().strip() try: balance = float(balance_text) if balance_text else 0.0 except ValueError: balance = 0.0 # تعيين رقم الهاتف الرئيسي من قائمة أرقام الهواتف primary_phone = None for p in self.phones: if p.is_primary: primary_phone = p.phone_number break # إذا لم يكن هناك رقم هاتف رئيسي، استخدم الرقم المدخل في حقل رقم الهاتف الرئيسي if not primary_phone: primary_phone = phone return { 'name': name, 'phone': primary_phone, # استخدام رقم الهاتف الرئيسي 'email': email, 'address': address, 'balance': balance, # استخدام الرصيد المدخل 'notes': notes, 'phones': self.phones # إضافة قائمة أرقام الهواتف } class ClientDetailsDialog(QDialog): """نافذة حوار لعرض تفاصيل العميل""" def __init__(self, parent=None, client=None): super().__init__(parent) title = f" تفاصيل العميل: {client.name if client else 'غير محدد'}" self.setWindowTitle(title) self.setModal(True) self.setMinimumSize(900, 600) self.client = client self.session = parent.session if parent else None # تطبيق نمط أساسي للنافذة QDialog { background-color: #f8f9fa; border: 2px solid #dee2e6; border-radius: 10px; } QGroupBox { font-weight: bold; border: 2px solid #ced4da; border-radius: 8px; margin-top: 10px; padding-top: 10px; } QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; } """) self.init_ui() def init_ui(self): main_layout = QVBoxLayout() main_layout.setContentsMargins(20, 20, 20, 20) main_layout.setSpacing(20) # عنوان النافذة المتطور title_label = QLabel(f" معلومات العميل: {self.client.name}") title_label.setAlignment(Qt.AlignCenter) title_label.setStyleSheet(""" QLabel { font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px; background-color: #ecf0f1; border-radius: 8px; margin: 5px; } main_layout.addWidget(title_label) # وصف النافذة desc_label = QLabel("عرض شامل لجميع بيانات العميل ومعلومات الاتصال") desc_label.setAlignment(Qt.AlignCenter) desc_label.setStyleSheet(""" QLabel { font-size: 14px; color: #7f8c8d; padding: 5px; margin: 5px; } main_layout.addWidget(desc_label) # إنشاء مقسم للمحتوى splitter = QSplitter(Qt.Horizontal) # الجانب الأيسر - المعلومات الأساسية left_widget = self.create_basic_info_section() splitter.addWidget(left_widget) # الجانب الأيمن - معلومات الاتصال right_widget = self.create_contact_info_section() splitter.addWidget(right_widget) # ضبط أحجام المقسم splitter.setSizes([500, 400]) main_layout.addWidget(splitter) # أزرار الإجراءات buttons_layout = QHBoxLayout() buttons_layout.addStretch() close_button = QPushButton(" إغلاق") close_button.setStyleSheet(""" QPushButton { background-color: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 6px; font-weight: bold; } QPushButton:hover { background-color: #c82333; } close_button.clicked.connect(self.accept) buttons_layout.addWidget(close_button) buttons_layout.addStretch() main_layout.addLayout(buttons_layout) self.setLayout(main_layout) def create_basic_info_section(self): """إنشاء قسم المعلومات الأساسية""" widget = QWidget() layout = QVBoxLayout(widget) # مجموعة البيانات الشخصية personal_group = QGroupBox(" البيانات الشخصية") personal_layout = QFormLayout() # الاسم name_label = QLabel(self.client.name) name_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50;") name_title = QLabel("الاسم:") name_title.setStyleSheet("font-weight: bold; color: #34495e;") personal_layout.addRow(name_title, name_label) # البريد الإلكتروني email_label = QLabel(self.client.email or "غير متوفر") email_label.setStyleSheet("color: #7f8c8d;") email_title = QLabel("البريد الإلكتروني:") email_title.setStyleSheet("font-weight: bold; color: #34495e;") personal_layout.addRow(email_title, email_label) # العنوان address_label = QLabel(self.client.address or "غير متوفر") address_label.setStyleSheet("color: #7f8c8d;") address_title = QLabel("العنوان:") address_title.setStyleSheet("font-weight: bold; color: #34495e;") personal_layout.addRow(address_title, address_label) # الرصيد balance_color = "#27ae60" if self.client.balance >= 0 else "#e74c3c" balance_label = QLabel(f"{self.client.balance:.2f} ر.س") balance_label.setStyleSheet(f"font-weight: bold; color: {balance_color}; font-size: 14px;") balance_title = QLabel("الرصيد:") balance_title.setStyleSheet("font-weight: bold; color: #34495e;") personal_layout.addRow(balance_title, balance_label) personal_group.setLayout(personal_layout) layout.addWidget(personal_group) # مجموعة الملاحظات notes_group = QGroupBox(" ملاحظات إضافية") notes_layout = QVBoxLayout() notes_text = QTextEdit() notes_text.setPlainText(self.client.notes or "لا توجد ملاحظات") notes_text.setReadOnly(True) notes_text.setMaximumHeight(100) notes_layout.addWidget(notes_text) notes_group.setLayout(notes_layout) layout.addWidget(notes_group) return widget def create_contact_info_section(self): widget = QWidget() layout = QVBoxLayout(widget) # مجموعة أرقام الهواتف phones_group = QGroupBox(" أرقام الهواتف") phones_layout = QVBoxLayout() try: # البحث عن أرقام هواتف العميل phone_records = self.session.query(ClientPhone).filter_by(client_id=self.client.id).all() if phone_records: phones_list = QListWidget() phones_list.setMaximumHeight(150) for phone in phone_records: phone_label = f" ({phone.label})" if phone.label else "" primary_mark = " [رئيسي]" if phone.is_primary else "" item_text = f" {phone.phone_number}{phone_label}{primary_mark}" item = QListWidgetItem(item_text) phones_list.addItem(item) phones_layout.addWidget(phones_list) else: # إذا لم تكن هناك أرقام هواتف متعددة phone_label = QLabel(self.client.phone or "غير متوفر") phone_label.setStyleSheet("color: #7f8c8d; padding: 5px;") phones_layout.addWidget(phone_label) except Exception as e: print(f"خطأ في عرض أرقام الهواتف: {str(e)}") phone_label = QLabel(self.client.phone or "غير متوفر") phone_label.setStyleSheet("color: #7f8c8d; padding: 5px;") phones_layout.addWidget(phone_label) phones_group.setLayout(phones_layout) layout.addWidget(phones_group) # مجموعة إحصائيات سريعة stats_group = QGroupBox(" إحصائيات سريعة") stats_layout = QFormLayout() # عدد الفواتير try: invoices_count = len(self.client.invoices) if hasattr(self.client, 'invoices') else 0 invoices_title = QLabel("عدد الفواتير:") invoices_title.setStyleSheet("font-weight: bold; color: #34495e;") invoices_value = QLabel(str(invoices_count)) invoices_value.setStyleSheet("color: #7f8c8d;") stats_layout.addRow(invoices_title, invoices_value) except: invoices_title = QLabel("عدد الفواتير:") invoices_title.setStyleSheet("font-weight: bold; color: #34495e;") invoices_value = QLabel("0") invoices_value.setStyleSheet("color: #7f8c8d;") stats_layout.addRow(invoices_title, invoices_value) # تاريخ الإنشاء if hasattr(self.client, 'created_at') and self.client.created_at: created_date = self.client.created_at.strftime('%Y-%m-%d') else: created_date = "غير محدد" created_title = QLabel("تاريخ الإنشاء:") created_title.setStyleSheet("font-weight: bold; color: #34495e;") created_value = QLabel(created_date) created_value.setStyleSheet("color: #7f8c8d;") stats_layout.addRow(created_title, created_value) stats_group.setLayout(stats_layout) layout.addWidget(stats_group) return widget class ClientFinancialDetailsDialog(QDialog): """نافذة حوار متطورة لعرض التفاصيل المالية للعميل""" def __init__(self, parent=None, client=None, session=None): super().__init__(parent) title = f" التفاصيل المالية: {client.name if client else 'غير محدد'}" self.setWindowTitle(title) self.setModal(True) self.setMinimumSize(1000, 700) self.client = client self.session = session # تطبيق نمط أساسي للنافذة QDialog { background-color: #f8f9fa; border: 2px solid #dee2e6; border-radius: 10px; } QGroupBox { font-weight: bold; border: 2px solid #ced4da; border-radius: 8px; margin-top: 10px; padding-top: 10px; } QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 5px 0 5px; } QTableWidget { border: 1px solid #ced4da; border-radius: 6px; background-color: white; } """) self.init_ui() self.load_financial_data() def init_ui(self): main_layout = QVBoxLayout() main_layout.setContentsMargins(20, 20, 20, 20) main_layout.setSpacing(20) # عنوان النافذة المتطور title_label = QLabel(f" التفاصيل المالية: {self.client.name}") title_label.setAlignment(Qt.AlignCenter) title_label.setStyleSheet(""" QLabel { font-size: 18px; font-weight: bold; color: #2c3e50; padding: 10px; background-color: #ecf0f1; border-radius: 8px; margin: 5px; } main_layout.addWidget(title_label) # وصف النافذة desc_label = QLabel("عرض شامل للمعاملات المالية والفواتير") desc_label.setAlignment(Qt.AlignCenter) desc_label.setStyleSheet(""" QLabel { font-size: 14px; color: #7f8c8d; padding: 5px; margin: 5px; } main_layout.addWidget(desc_label) # إنشاء مقسم للمحتوى splitter = QSplitter(Qt.Vertical) # القسم العلوي - الملخص المالي top_widget = self.create_financial_summary_section() splitter.addWidget(top_widget) # القسم السفلي - جدول الفواتير bottom_widget = self.create_invoices_table_section() splitter.addWidget(bottom_widget) # ضبط أحجام المقسم splitter.setSizes([300, 500]) main_layout.addWidget(splitter) # أزرار الإجراءات buttons_layout = QHBoxLayout() buttons_layout.addStretch() close_button = QPushButton(" إغلاق") close_button.setStyleSheet(""" QPushButton { background-color: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 6px; font-weight: bold; } QPushButton:hover { background-color: #c82333; } close_button.clicked.connect(self.accept) buttons_layout.addWidget(close_button) buttons_layout.addStretch() main_layout.addLayout(buttons_layout) self.setLayout(main_layout) def create_financial_summary_section(self): """إنشاء قسم الملخص المالي""" widget = QWidget() layout = QVBoxLayout(widget) # مجموعة الملخص المالي summary_group = QGroupBox(" الملخص المالي") summary_layout = QHBoxLayout() try: # حساب الإحصائيات المالية invoices = self.session.query(Invoice).filter_by(client_id=self.client.id).all() total_amount = sum(invoice.total_amount for invoice in invoices) paid_amount = sum(invoice.paid_amount for invoice in invoices) remaining_amount = total_amount - paid_amount # بطاقة إجمالي المبالغ total_card = self.create_summary_card(" إجمالي المبالغ", f"{total_amount:.2f} ر.س", "#3498db") summary_layout.addWidget(total_card) # بطاقة المبلغ المدفوع paid_card = self.create_summary_card(" المبلغ المدفوع", f"{paid_amount:.2f} ر.س", "#27ae60") summary_layout.addWidget(paid_card) # بطاقة المبلغ المتبقي remaining_color = "#e74c3c" if remaining_amount > 0 else "#27ae60" remaining_card = self.create_summary_card("⏳ المبلغ المتبقي", f"{remaining_amount:.2f} ر.س", remaining_color) summary_layout.addWidget(remaining_card) # بطاقة عدد الفواتير count_card = self.create_summary_card(" عدد الفواتير", str(len(invoices)), "#9b59b6") summary_layout.addWidget(count_card) except Exception as e: print(f"خطأ في حساب الملخص المالي: {str(e)}") # عرض بيانات افتراضية في حالة الخطأ error_card = self.create_summary_card(" خطأ", "لا يمكن تحميل البيانات", 'danger') summary_layout.addWidget(error_card) summary_group.setLayout(summary_layout) layout.addWidget(summary_group) return widget def create_summary_card(self, title, value, color): card = QWidget() card_layout = QVBoxLayout(card) card_layout.setContentsMargins(15, 15, 15, 15) card_layout.setSpacing(10) # عنوان البطاقة title_label = QLabel(title) title_label.setAlignment(Qt.AlignCenter) title_label.setStyleSheet("font-size: 12px; color: #7f8c8d; font-weight: bold;") card_layout.addWidget(title_label) # قيمة البطاقة value_label = QLabel(value) value_label.setAlignment(Qt.AlignCenter) value_label.setStyleSheet(f"font-size: 16px; color: {color}; font-weight: bold;") card_layout.addWidget(value_label) # تطبيق نمط البطاقة # تحويل اللون من hex إلى RGB للشفافية r = int(color[1:3], 16) g = int(color[3:5], 16) b = int(color[5:7], 16) card.setStyleSheet(f""" QWidget {{ background-color: rgba({r}, {g}, {b}, 0.1); border: 2px solid {color}; border-radius: 8px; margin: 5px; }} return card def create_invoices_table_section(self): """إنشاء قسم جدول الفواتير""" widget = QWidget() layout = QVBoxLayout(widget) # مجموعة جدول الفواتير table_group = QGroupBox(" قائمة الفواتير") table_layout = QVBoxLayout() # إنشاء الجدول المتطور self.invoices_table = QTableWidget() self.invoices_table.setColumnCount(6) self.invoices_table.setHorizontalHeaderLabels([ "رقم الفاتورة", "التاريخ", "تاريخ الاستحقاق", "المبلغ الإجمالي", "المبلغ المدفوع", "الحالة" ]) # إعداد خصائص الجدول self.invoices_table.setSelectionBehavior(QTableWidget.SelectRows) self.invoices_table.setSelectionMode(QTableWidget.SingleSelection) self.invoices_table.setEditTriggers(QTableWidget.NoEditTriggers) # تطبيق نمط الجدول QTableWidget { border: 1px solid #ced4da; border-radius: 6px; background-color: white; gridline-color: #dee2e6; } QTableWidget::item { padding: 8px; border-bottom: 1px solid #dee2e6; } QTableWidget::item:selected { background-color: #e3f2fd; } QHeaderView::section { background-color: #f8f9fa; padding: 8px; border: 1px solid #dee2e6; font-weight: bold; } """) table_layout.addWidget(self.invoices_table) table_group.setLayout(table_layout) layout.addWidget(table_group) return widget def load_financial_data(self): try: # البحث عن فواتير العميل invoices = self.session.query(Invoice).filter_by(client_id=self.client.id).order_by(Invoice.date.desc()).all() # تحديث الجدول self.invoices_table.setRowCount(len(invoices)) for row, invoice in enumerate(invoices): # رقم الفاتورة self.invoices_table.setItem(row, 0, QTableWidgetItem(invoice.invoice_number or "")) # التاريخ date_str = invoice.date.strftime('%Y-%m-%d') if invoice.date else "" self.invoices_table.setItem(row, 1, QTableWidgetItem(date_str)) # تاريخ الاستحقاق due_date_str = invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else "" self.invoices_table.setItem(row, 2, QTableWidgetItem(due_date_str)) # المبلغ الإجمالي self.invoices_table.setItem(row, 3, QTableWidgetItem(f"{invoice.total_amount:.2f}")) # المبلغ المدفوع self.invoices_table.setItem(row, 4, QTableWidgetItem(f"{invoice.paid_amount:.2f}")) # الحالة status_text = self.get_status_text(invoice.status) self.invoices_table.setItem(row, 5, QTableWidgetItem(status_text)) except Exception as e: print(f"خطأ في تحميل البيانات المالية: {str(e)}") def get_status_text(self, status): """تحويل حالة الفاتورة إلى نص""" status_map = { 'pending': 'قيد الانتظار', 'paid': 'مدفوعة', 'partially_paid': 'مدفوعة جزئياً', 'cancelled': 'ملغاة' } return status_map.get(status, 'غير محدد') def add_financial_data(self): try: # التحقق من صحة البيانات المدخلة required_amount_text = self.required_amount_edit.text().strip() paid_amount_text = self.paid_amount_edit.text().strip() if not required_amount_text: show_error_message("خطأ", "يجب إدخال المبلغ المطلوب") return try: required_amount = float(required_amount_text) paid_amount = float(paid_amount_text) if paid_amount_text else 0.0 except ValueError: show_error_message("خطأ", "يجب إدخال قيم رقمية صحيحة") return if required_amount <= 0: show_error_message("خطأ", "يجب أن يكون المبلغ المطلوب أكبر من صفر") return if paid_amount < 0: show_error_message("خطأ", "لا يمكن أن يكون المبلغ المدفوع سالباً") return # تحديد حالة الفاتورة if paid_amount == 0: status = 'pending' # قيد الانتظار elif paid_amount >= required_amount: status = 'paid' # مدفوعة paid_amount = required_amount # تصحيح المبلغ المدفوع إذا كان أكبر من المطلوب else: status = 'partially_paid' # مدفوعة جزئياً # إنشاء رقم فاتورة جديد invoice_number = f"INV-{self.client.id}-{datetime.now().strftime('%Y%m%d%H%M%S')}" # طلب تأكيد الإضافة confirmation_message = f"هل أنت متأكد من إضافة الفاتورة التالية؟\n\n" confirmation_message += f"المبلغ المطلوب: {required_amount:.2f}\n" confirmation_message += f"المبلغ المدفوع: {paid_amount:.2f}\n" confirmation_message += f"تاريخ الدفع: {self.payment_date_edit.date().toString('yyyy-MM-dd')}\n" confirmation_message += f"تاريخ المبلغ المتبقي: {self.due_date_edit.date().toString('yyyy-MM-dd')}\n" if show_confirmation_message("تأكيد الإضافة", confirmation_message): # إنشاء فاتورة جديدة new_invoice = Invoice( invoice_number=invoice_number, client_id=self.client.id, date=self.payment_date_edit.date().toPyDate(), due_date=self.due_date_edit.date().toPyDate(), total_amount=required_amount, paid_amount=paid_amount, status=status, notes="" ) # إضافة الفاتورة إلى قاعدة البيانات self.session.add(new_invoice) self.session.commit() # تحديث البيانات المعروضة self.load_data() # مسح حقول الإدخال self.required_amount_edit.clear() self.paid_amount_edit.clear() show_info_message("تم", "تمت إضافة البيانات المالية بنجاح") else: # إلغاء الإضافة return except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء إضافة البيانات المالية: {str(e)}") def edit_financial_data(self, invoice_id): """تعديل البيانات المالية""" try: # البحث عن الفاتورة في قاعدة البيانات invoice = self.session.query(Invoice).get(invoice_id) if not invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return # استخراج البيانات الحالية required_amount = invoice.total_amount paid_amount = invoice.paid_amount # تعيين القيم الحالية في حقول الإدخال self.required_amount_edit.setText(str(required_amount)) self.paid_amount_edit.setText(str(paid_amount)) # تعيين التواريخ if invoice.date: self.payment_date_edit.setDate(QDate(invoice.date)) if invoice.due_date: self.due_date_edit.setDate(QDate(invoice.due_date)) # تخزين معرف الفاتورة للاستخدام لاحقاً self.current_invoice_id = invoice_id # تغيير نص زر الإضافة إلى "تحديث" self.add_button.setText("تحديث") self.add_button.clicked.disconnect() self.add_button.clicked.connect(self.update_financial_data) # التمرير إلى أعلى النافذة لعرض حقول الإدخال self.scroll_to_top() except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تحميل البيانات للتعديل: {str(e)}") def update_financial_data(self): try: # التحقق من صحة البيانات المدخلة required_amount_text = self.required_amount_edit.text().strip() paid_amount_text = self.paid_amount_edit.text().strip() if not required_amount_text: show_error_message("خطأ", "يجب إدخال المبلغ المطلوب") return try: required_amount = float(required_amount_text) paid_amount = float(paid_amount_text) if paid_amount_text else 0.0 except ValueError: show_error_message("خطأ", "يجب إدخال قيم رقمية صحيحة") return if required_amount <= 0: show_error_message("خطأ", "يجب أن يكون المبلغ المطلوب أكبر من صفر") return if paid_amount < 0: show_error_message("خطأ", "لا يمكن أن يكون المبلغ المدفوع سالباً") return # تحديد حالة الفاتورة if paid_amount == 0: status = 'pending' # قيد الانتظار elif paid_amount >= required_amount: status = 'paid' # مدفوعة paid_amount = required_amount # تصحيح المبلغ المدفوع إذا كان أكبر من المطلوب else: status = 'partially_paid' # مدفوعة جزئياً # البحث عن الفاتورة في قاعدة البيانات invoice = self.session.query(Invoice).get(self.current_invoice_id) if not invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return # تحديث بيانات الفاتورة invoice.total_amount = required_amount invoice.paid_amount = paid_amount invoice.date = self.payment_date_edit.date().toPyDate() invoice.due_date = self.due_date_edit.date().toPyDate() invoice.status = status # حفظ التغييرات self.session.commit() # إعادة تعيين زر الإضافة self.add_button.setText("إضافة") self.add_button.clicked.disconnect() self.add_button.clicked.connect(self.add_financial_data) # مسح حقول الإدخال self.required_amount_edit.clear() self.paid_amount_edit.clear() # تحديث البيانات المعروضة self.load_data() show_info_message("تم", "تم تحديث البيانات المالية بنجاح") except Exception as e: self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء تحديث البيانات المالية: {str(e)}") def delete_financial_data(self, invoice_id): """حذف البيانات المالية""" try: # البحث عن الفاتورة في قاعدة البيانات invoice = self.session.query(Invoice).get(invoice_id) if not invoice: show_error_message("خطأ", "لم يتم العثور على الفاتورة") return # طلب تأكيد الحذف if show_confirmation_message("تأكيد الحذف", "هل أنت متأكد من حذف هذه الفاتورة؟"): # حذف الفاتورة self.session.delete(invoice) self.session.commit() # تحديث البيانات المعروضة self.load_data() show_info_message("تم", "تم حذف الفاتورة بنجاح") except Exception as e: self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء حذف الفاتورة: {str(e)}") def scroll_to_top(self): # يمكن تنفيذ هذه الدالة إذا كانت النافذة تحتوي على عنصر تمرير pass def save_changes(self): """حفظ التغييرات""" try: self.session.commit() show_info_message("تم", "تم حفظ التغييرات بنجاح") except Exception as e: self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء حفظ التغييرات: {str(e)}") def apply_filter(self): try: # الحصول على قيم الفلترة from_date = self.from_date_edit.date().toPyDate() to_date = self.to_date_edit.date().toPyDate() status = self.status_combo.currentData() # الحصول على فواتير العميل مع تطبيق الفلترة query = self.session.query(Invoice).filter_by(client_id=self.client.id) # فلترة حسب التاريخ query = query.filter(Invoice.date >= from_date, Invoice.date <= to_date) # فلترة حسب الحالة if status != "all": query = query.filter(Invoice.status == status) # تنفيذ الاستعلام invoices = query.all() # تحديث الجدول self.update_table(invoices) # عرض رسالة تأكيد show_info_message("تم", "تم تطبيق الفلترة بنجاح") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تطبيق الفلترة: {str(e)}") def reset_filter(self): """إعادة تعيين الفلترة""" try: # إعادة تعيين حقول الفلترة self.from_date_edit.setDate(QDate.currentDate().addMonths(-1)) self.to_date_edit.setDate(QDate.currentDate()) self.status_combo.setCurrentIndex(0) # "الكل" # إعادة تحميل البيانات self.load_data() # عرض رسالة تأكيد show_info_message("تم", "تم إعادة تعيين الفلترة بنجاح") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء إعادة تعيين الفلترة: {str(e)}") def update_table(self, invoices): try: # حساب المبالغ total_due = sum(invoice.total_amount for invoice in invoices if invoice.status != 'paid' and invoice.status != 'cancelled') total_paid = sum(invoice.paid_amount for invoice in invoices) remaining = total_due - total_paid # عرض المبالغ مع تنسيق العملة self.total_due_label.setText(format_currency(total_due)) self.total_paid_label.setText(format_currency(total_paid)) self.remaining_label.setText(format_currency(remaining)) # ملء جدول الفواتير self.invoices_table.setRowCount(0) for row, invoice in enumerate(invoices): self.invoices_table.insertRow(row) self.invoices_table.setItem(row, 0, QTableWidgetItem(invoice.invoice_number)) # التاريخ مع تنسيق مناسب للغة العربية date_str = format_date(invoice.date) if invoice.date else "" self.invoices_table.setItem(row, 1, QTableWidgetItem(date_str)) # تاريخ الاستحقاق مع تنسيق مناسب للغة العربية due_date_str = format_date(invoice.due_date) if invoice.due_date else "" self.invoices_table.setItem(row, 2, QTableWidgetItem(due_date_str)) # المبلغ الإجمالي مع تنسيق العملة self.invoices_table.setItem(row, 3, QTableWidgetItem(format_currency(invoice.total_amount))) # المبلغ المدفوع مع تنسيق العملة self.invoices_table.setItem(row, 4, QTableWidgetItem(format_currency(invoice.paid_amount))) # الحالة مع تمييز واضح باستخدام الألوان status_map = { 'pending': 'قيد الانتظار', 'paid': 'مدفوعة', 'partially_paid': 'مدفوعة جزئياً', 'cancelled': 'ملغاة' } status_text = status_map.get(invoice.status, invoice.status) status_item = QTableWidgetItem(status_text) # تمييز الحالة باستخدام الألوان if invoice.status == 'pending': status_item.setBackground(Qt.yellow) # أصفر لقيد الانتظار elif invoice.status == 'paid': status_item.setBackground(Qt.green) # أخضر للمدفوعة elif invoice.status == 'partially_paid': status_item.setBackground(Qt.cyan) # أزرق فاتح للمدفوعة جزئياً elif invoice.status == 'cancelled': status_item.setBackground(Qt.gray) # رمادي للملغاة self.invoices_table.setItem(row, 5, status_item) # إضافة زر التعديل edit_button = QPushButton("تعديل") edit_button edit_button.clicked.connect(lambda _, inv_id=invoice.id: self.edit_financial_data(inv_id)) self.invoices_table.setCellWidget(row, 6, edit_button) # إضافة زر الحذف delete_button = QPushButton("حذف") delete_button delete_button.clicked.connect(lambda _, inv_id=invoice.id: self.delete_financial_data(inv_id)) self.invoices_table.setCellWidget(row, 7, delete_button) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تحديث الجدول: {str(e)}") def load_data(self): """تحميل البيانات المالية للعميل""" try: # الحصول على فواتير العميل invoices = self.session.query(Invoice).filter_by(client_id=self.client.id).all() # تحديث الجدول self.update_table(invoices) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تحميل البيانات المالية: {str(e)}") class ClientsWidget(QWidget): def __init__(self, session): super().__init__() self.session = session self.enhancements_applied = False # متغير لتتبع تطبيق التحسينات self.init_ui() self.refresh_data() # تطبيق التحسينات المرئية عند البدء print(" تطبيق التحسينات المرئية عند بدء البرنامج...") # self.apply_startup_enhancements() # محذوف - استخدام apply_visual_enhancements_after_populate بدلاً منه QTimer.singleShot(500, self.apply_visual_enhancements_after_populate) # تطبيق التحسينات الجديدة فوراً عند البدء QTimer.singleShot(2000, self.apply_immediate_updates) # تأخير ثانيتين للتأكد من اكتمال التحميل def init_ui(self): # إنشاء التخطيط الرئيسي main_layout = QVBoxLayout() main_layout.setContentsMargins(10, 10, 10, 10) main_layout.setSpacing(8) # إضافة العنوان الرئيسي المطور والمحسن title_label = QLabel("🤝 إدارة العملاء المتطورة - نظام شامل ومتقدم لإدارة بيانات العملاء مع أدوات احترافية للبحث والتحليل والتقارير") title_label.setFont(QFont("Segoe UI", 18, QFont.Bold)) # خط أكبر وأوضح title_label.setAlignment(Qt.AlignCenter) # توسيط النص في المنتصف title_label.setStyleSheet(""" QLabel { color: #ffffff; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e40af, stop:0.2 #3b82f6, stop:0.4 #6366f1, stop:0.6 #8b5cf6, stop:0.8 #a855f7, stop:1 #c084fc); border: 3px solid #000000; border-radius: 10px; padding: 4px 10px; margin: 2px; font-weight: bold; max-height: 40px; min-height: 40px; } main_layout.addWidget(title_label) # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) top_frame = QFrame() top_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } # تخطيط أفقي واحد محسن (الطريقة القديمة) search_layout = QHBoxLayout() search_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق search_layout.setSpacing(4) # مسافات متوازنة # إنشاء حاوي عمودي للتوسيط الحقيقي top_container = QVBoxLayout() top_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط top_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط top_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف top_container.addLayout(search_layout) # إضافة مساحة فارغة أسفل للتوسيط top_container.addStretch(1) # تسمية البحث محسنة مع ألوان موحدة search_label = QLabel(" بحث:") search_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #dc2626, stop:0.5 #b91c1c, stop:1 #991b1b); border: 3px solid #7f1d1d; border-radius: 12px; min-width: 70px; max-width: 70px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ef4444, stop:0.5 #dc2626, stop:1 #b91c1c); border: 3px solid #ef4444; } search_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية self.search_edit = QLineEdit() self.search_edit.setPlaceholderText(" ابحث بالاسم، الهاتف، الإيميل أو العنوان...") self.search_edit.textChanged.connect(self.filter_clients) self.search_edit.setStyleSheet(""" QLineEdit { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.5 #f8fafc, stop:1 #e2e8f0); border: 3px solid #4f46e5; border-radius: 12px; padding: 8px 15px; font-size: 14px; font-weight: bold; color: #1f2937; max-height: 38px; min-height: 34px; selection-background-color: #4f46e5; } QLineEdit:focus { border: 3px solid #3730a3; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f0f9ff, stop:1 #e0f2fe); } QLineEdit:hover { border: 3px solid #5b52f0; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fafbff, stop:1 #f1f5f9); } search_button = QPushButton("") search_button.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0891b2, stop:0.5 #0e7490, stop:1 #155e75); color: #ffffff; border: 3px solid #164e63; border-radius: 12px; padding: 8px; font-size: 20px; font-weight: bold; min-width: 50px; max-width: 50px; max-height: 38px; min-height: 34px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #22d3ee, stop:0.5 #0891b2, stop:1 #0e7490); border: 3px solid #22d3ee; } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0e7490, stop:1 #155e75); border: 3px solid #0e7490; } search_button.clicked.connect(self.filter_clients) search_button.setToolTip("بحث متقدم") search_button.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed) search_button.setContentsMargins(0, 0, 0, 0) # تسمية التصفية مطورة بألوان احترافية filter_label = QLabel(" تصفية:") filter_label.setStyleSheet(""" QLabel { color: #ffffff; font-size: 14px; font-weight: bold; padding: 8px 12px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f59e0b, stop:0.5 #d97706, stop:1 #b45309); border: 3px solid #92400e; border-radius: 12px; min-width: 65px; max-width: 65px; max-height: 38px; min-height: 34px; } QLabel:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #fbbf24, stop:0.5 #f59e0b, stop:1 #d97706); border: 3px solid #fbbf24; } filter_label.setAlignment(Qt.AlignCenter) # توسيط النص داخل التسمية # أزرار التصفية السريعة بألوان مختلفة ومميزة filter_all_button = QPushButton(" الكل") self.style_advanced_button(filter_all_button, 'slate') # رمادي داكن للكل filter_all_button.clicked.connect(lambda: self.filter_by_balance('all')) filter_all_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) filter_positive_button = QPushButton(" لهم مبلغ") self.style_advanced_button(filter_positive_button, 'emerald') # أخضر زمردي مختلف filter_positive_button.clicked.connect(lambda: self.filter_by_balance('positive')) filter_positive_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) filter_negative_button = QPushButton(" عليهم مبلغ") self.style_advanced_button(filter_negative_button, 'danger') # أحمر للسالب filter_negative_button.clicked.connect(lambda: self.filter_by_balance('negative')) filter_negative_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) filter_zero_button = QPushButton(" بدون رصيد") self.style_advanced_button(filter_zero_button, 'secondary') # رمادي فاتح مختلف filter_zero_button.clicked.connect(lambda: self.filter_by_balance('zero')) filter_zero_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) # أزرار الأدوات المتقدمة بنفس التنسيق columns_button = QPushButton(" الأعمدة") self.style_advanced_button(columns_button, 'purple') # بنفسجي للأعمدة columns_button.clicked.connect(self.show_columns_dialog) columns_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) bulk_operations_button = QPushButton(" عمليات مجمعة") self.style_advanced_button(bulk_operations_button, 'indigo') # نيلي للعمليات bulk_operations_button.clicked.connect(self.show_bulk_operations_dialog) bulk_operations_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) advanced_filters_button = QPushButton(" فلاتر متقدمة") self.style_advanced_button(advanced_filters_button, 'rose') # وردي للفلاتر advanced_filters_button.clicked.connect(self.show_advanced_filters_dialog) advanced_filters_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) # تطبيق نفس مقاسات الإطار السفلي على أزرار الإطار العلوي top_buttons = [filter_all_button, filter_positive_button, filter_negative_button, filter_zero_button, columns_button, bulk_operations_button, advanced_filters_button] for button in top_buttons: button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # تغيير سياسة الحجم button.setMinimumWidth(95) # نفس العرض الأدنى للإطار السفلي button.setMaximumHeight(38) # ارتفاع أكبر ليناسب الإطار الأكبر (75px) button.setMinimumHeight(34) # ارتفاع أدنى أكبر button.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش الإضافية # حجم خط مناسب للارتفاع الجديد font = button.font() font.setPointSize(12) # حجم خط أكبر ليناسب الارتفاع الأكبر font.setBold(True) button.setFont(font) # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار search_layout.addWidget(search_label, 0, Qt.AlignVCenter) search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter) # يأخذ مساحة أكبر search_layout.addWidget(search_button, 0, Qt.AlignVCenter) search_layout.addWidget(filter_label, 0, Qt.AlignVCenter) search_layout.addWidget(filter_all_button, 1, Qt.AlignVCenter) # توسيع الأزرار search_layout.addWidget(filter_positive_button, 1, Qt.AlignVCenter) search_layout.addWidget(filter_negative_button, 1, Qt.AlignVCenter) search_layout.addWidget(filter_zero_button, 1, Qt.AlignVCenter) search_layout.addWidget(columns_button, 1, Qt.AlignVCenter) search_layout.addWidget(bulk_operations_button, 1, Qt.AlignVCenter) search_layout.addWidget(advanced_filters_button, 1, Qt.AlignVCenter) # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط top_frame.setLayout(top_container) # إنشاء جدول العملاء المتطور والمحسن self.create_advanced_clients_table() # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل bottom_frame = QFrame() bottom_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.3 #f8fafc, stop:0.7 #e2e8f0, stop:1 #cbd5e0); border: 3px solid #000000; border-radius: 10px; margin: 1px; padding: 0px; max-height: 75px; min-height: 70px; } actions_layout = QHBoxLayout() actions_layout.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش للتوسيط الدقيق actions_layout.setSpacing(4) # مسافة أكبر بين الأزرار لتوزيع أفضل # إنشاء حاوي عمودي للتوسيط الحقيقي bottom_container = QVBoxLayout() bottom_container.setContentsMargins(6, 0, 6, 0) # هوامش جانبية فقط bottom_container.setSpacing(0) # إضافة مساحة فارغة أعلى للتوسيط bottom_container.addStretch(1) # إضافة التخطيط الأفقي في المنتصف bottom_container.addLayout(actions_layout) # إضافة مساحة فارغة أسفل للتوسيط bottom_container.addStretch(1) # إنشاء الأزرار بأحجام متناسقة حسب الترتيب الجديد # المجموعة الأولى - العمليات الأساسية مع ألوان متنوعة self.add_button = QPushButton(" إضافة عميل ") self.style_advanced_button(self.add_button, 'emerald', has_menu=True) # أخضر زمردي مميز مع قائمة self.add_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة لإضافة العميل مع النمط الموحد الجديد add_menu = QMenu(self) add_menu.setStyleSheet(UnifiedStyles.get_menu_style('success', 'normal')) # إضافة خيارات الإضافة المتقدمة add_normal_action = QAction(" إضافة عميل عادي", self) add_normal_action.triggered.connect(self.add_client) add_menu.addAction(add_normal_action) add_menu.addSeparator() # quick_add_action = QAction(" إضافة سريعة", self) # محذوف # quick_add_action.triggered.connect(self.quick_add_client) # محذوف # add_menu.addAction(quick_add_action) # محذوف # import_action = QAction(" استيراد من ملف", self) # محذوف # import_action.triggered.connect(self.import_clients) # محذوف # add_menu.addAction(import_action) # محذوف # ربط القائمة بالزر self.add_button.setMenu(add_menu) self.edit_button = QPushButton(" تعديل") self.style_advanced_button(self.edit_button, 'primary') # أزرق كلاسيكي self.edit_button.clicked.connect(self.edit_client) self.edit_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.delete_button = QPushButton(" حذف") self.style_advanced_button(self.delete_button, 'danger') # أحمر تحذيري self.delete_button.clicked.connect(self.delete_client) self.delete_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.refresh_button = QPushButton(" تحديث") self.style_advanced_button(self.refresh_button, 'modern_teal') # تصميم حديث ومتطور self.refresh_button.clicked.connect(self.refresh_data) # استخدام refresh_data الأساسية self.refresh_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # المجموعة الثانية - العمليات المالية والملفات مع ألوان متنوعة self.balance_button = QPushButton(" تعديل الرصيد") self.style_advanced_button(self.balance_button, 'orange') # برتقالي مالي self.balance_button.clicked.connect(self.adjust_balance) self.balance_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.attachments_button = QPushButton(" إضافة المرفقات") self.style_advanced_button(self.attachments_button, 'purple') # بنفسجي مميز self.attachments_button.clicked.connect(self.manage_attachments) self.attachments_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.details_button = QPushButton(" عرض التفاصيل ") self.style_advanced_button(self.details_button, 'indigo', has_menu=True) # نيلي عميق self.details_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.call_button = QPushButton(" اتصال سريع ") self.style_advanced_button(self.call_button, 'lime', has_menu=True) # أخضر ليموني مختلف للاتصال self.call_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة لعرض التفاصيل مع النمط الموحد الجديد details_menu = QMenu(self) details_menu.setStyleSheet(UnifiedStyles.get_menu_style('primary', 'normal')) # إضافة خيارات عرض التفاصيل basic_details_action = QAction(" التفاصيل الأساسية", self) basic_details_action.triggered.connect(self.show_client_details) details_menu.addAction(basic_details_action) financial_details_action = QAction(" التفاصيل المالية", self) financial_details_action.triggered.connect(self.show_financial_details) details_menu.addAction(financial_details_action) contact_details_action = QAction(" تفاصيل الاتصال", self) contact_details_action.triggered.connect(self.show_contact_details) details_menu.addAction(contact_details_action) history_details_action = QAction(" سجل المعاملات", self) history_details_action.triggered.connect(self.show_transaction_history) details_menu.addAction(history_details_action) # ربط القائمة بالزر self.details_button.setMenu(details_menu) self.export_button = QPushButton(" تصدير ") self.style_advanced_button(self.export_button, 'info', has_menu=True) # لون متسق مع نظام الألوان self.export_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة للتصدير مع النمط الموحد الجديد export_menu = QMenu(self) export_menu.setStyleSheet(UnifiedStyles.get_menu_style('warning', 'normal')) # إضافة خيارات التصدير export_excel_action = QAction(" تصدير إلى Excel", self) export_excel_action.triggered.connect(self.export_data) export_menu.addAction(export_excel_action) export_pdf_action = QAction(" تصدير إلى PDF", self) export_pdf_action.triggered.connect(self.export_to_pdf) export_menu.addAction(export_pdf_action) export_csv_action = QAction(" تصدير إلى CSV", self) export_csv_action.triggered.connect(self.export_to_csv) export_menu.addAction(export_csv_action) export_json_action = QAction(" تصدير إلى JSON", self) export_json_action.triggered.connect(self.export_to_json) export_menu.addAction(export_json_action) # ربط القائمة بالزر self.export_button.setMenu(export_menu) # المجموعة الثالثة - التقارير والإحصائيات مع ألوان متنوعة self.report_button = QPushButton(" التقارير") self.style_advanced_button(self.report_button, 'cyan') # سيان مميز للتقارير self.report_button.clicked.connect(self.generate_clients_report) self.report_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.statistics_button = QPushButton(" الإحصائيات ") self.style_advanced_button(self.statistics_button, 'rose', has_menu=True) # وردي للإحصائيات self.statistics_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # المجموعة الرابعة - النظام مع ألوان متنوعة self.backup_button = QPushButton(" نسخ احتياطي") self.style_advanced_button(self.backup_button, 'warning') # كهرماني للنسخ الاحتياطي self.backup_button.clicked.connect(self.backup_clients_data) self.backup_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) self.restore_button = QPushButton(" استعادة") self.style_advanced_button(self.restore_button, 'slate') # رمادي داكن مميز للاستعادة self.restore_button.clicked.connect(self.restore_clients_data) self.restore_button.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred) # إنشاء قائمة منسدلة للاتصال السريع مع النمط الموحد الجديد call_menu = QMenu(self) call_menu.setStyleSheet(UnifiedStyles.get_menu_style('success', 'normal')) # إضافة خيارات الاتصال المتقدمة direct_call_action = QAction(" اتصال مباشر", self) direct_call_action.triggered.connect(self.make_direct_call) call_menu.addAction(direct_call_action) whatsapp_call_action = QAction(" اتصال واتساب", self) whatsapp_call_action.triggered.connect(self.make_whatsapp_call) call_menu.addAction(whatsapp_call_action) call_menu.addSeparator() copy_number_action = QAction(" نسخ الرقم", self) copy_number_action.triggered.connect(self.copy_phone_number) call_menu.addAction(copy_number_action) save_contact_action = QAction(" حفظ في جهات الاتصال", self) save_contact_action.triggered.connect(self.save_to_contacts) call_menu.addAction(save_contact_action) call_history_action = QAction(" سجل المكالمات", self) call_history_action.triggered.connect(self.show_call_history) call_menu.addAction(call_history_action) # ربط القائمة بالزر self.call_button.setMenu(call_menu) # إنشاء قائمة منسدلة للإحصائيات مع النمط الموحد الجديد statistics_menu = QMenu(self) statistics_menu.setStyleSheet(UnifiedStyles.get_menu_style('danger', 'normal')) # إضافة خيارات الإحصائيات basic_stats_action = QAction(" إحصائيات أساسية", self) basic_stats_action.triggered.connect(self.show_statistics) statistics_menu.addAction(basic_stats_action) detailed_stats_action = QAction(" إحصائيات مفصلة", self) detailed_stats_action.triggered.connect(self.show_detailed_statistics) statistics_menu.addAction(detailed_stats_action) balance_analysis_action = QAction(" تحليل الأرصدة", self) balance_analysis_action.triggered.connect(self.show_balance_analysis) statistics_menu.addAction(balance_analysis_action) monthly_report_action = QAction(" تقرير شهري", self) monthly_report_action.triggered.connect(self.show_monthly_report) statistics_menu.addAction(monthly_report_action) # ربط القائمة بالزر self.statistics_button.setMenu(statistics_menu) # إضافة الأزرار بالترتيب المطلوب مع ألوان متنوعة ومميزة # المجموعة الأولى - العمليات الأساسية مع التوسيط العمودي actions_layout.addWidget(self.add_button, 0, Qt.AlignVCenter) # 1. إضافة عميل (أخضر) actions_layout.addWidget(self.edit_button, 0, Qt.AlignVCenter) # 2. تعديل (أزرق) actions_layout.addWidget(self.delete_button, 0, Qt.AlignVCenter) # 3. حذف (أحمر) actions_layout.addWidget(self.refresh_button, 0, Qt.AlignVCenter) # 4. تحديث (تيل) # فاصل راقي 1 actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter) # المجموعة الثانية - العمليات المالية والملفات مع التوسيط العمودي actions_layout.addWidget(self.balance_button, 0, Qt.AlignVCenter) # 5. تعديل رصيد (برتقالي) actions_layout.addWidget(self.attachments_button, 0, Qt.AlignVCenter) # 6. إضافة المرفقات (بنفسجي) actions_layout.addWidget(self.details_button, 0, Qt.AlignVCenter) # 7. عرض التفاصيل (نيلي) # فاصل راقي 2 actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter) # المجموعة الثالثة - الاتصال مع التوسيط العمودي actions_layout.addWidget(self.call_button, 0, Qt.AlignVCenter) # 8. اتصال سريع (زمردي) # فاصل راقي 3 actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter) # المجموعة الرابعة - التقارير والإحصائيات مع التوسيط العمودي (ترتيب جديد) actions_layout.addWidget(self.statistics_button, 0, Qt.AlignVCenter) # 9. الإحصائيات (وردي) actions_layout.addWidget(self.report_button, 0, Qt.AlignVCenter) # 10. التقارير (سيان) # فاصل راقي 4 actions_layout.addWidget(self.create_elegant_separator(), 0, Qt.AlignVCenter) # المجموعة الخامسة - النظام مع التوسيط العمودي (ترتيب محسن) actions_layout.addWidget(self.backup_button, 0, Qt.AlignVCenter) # 11. نسخ احتياطي (كهرماني) actions_layout.addWidget(self.export_button, 0, Qt.AlignVCenter) # 12. تصدير (تيل فاتح) actions_layout.addWidget(self.restore_button, 0, Qt.AlignVCenter) # 13. استعادة (رمادي داكن) # تطبيق الأحجام الجديدة على جميع الأزرار بما في ذلك زر التصدير all_buttons = [ self.add_button, self.edit_button, self.delete_button, self.refresh_button, self.balance_button, self.attachments_button, self.details_button, self.call_button, self.statistics_button, self.report_button, self.backup_button, self.export_button, self.restore_button ] for button in all_buttons: # تطبيق الأحجام الجديدة المناسبة للإطار الأكبر (75px) button.setMaximumHeight(38) # ارتفاع أكبر ليناسب الإطار الأكبر button.setMinimumHeight(34) # ارتفاع أدنى أكبر button.setMinimumWidth(95) # عرض أدنى ثابت button.setContentsMargins(0, 0, 0, 0) # إزالة الهوامش # تطبيق خط أكبر ليناسب الارتفاع الجديد font = button.font() font.setPointSize(12) # حجم خط أكبر ليناسب الارتفاع الأكبر font.setBold(True) button.setFont(font) # تعيين التخطيط للإطار السفلي - استخدام الحاوي العمودي للتوسيط bottom_frame.setLayout(bottom_container) # تجميع التخطيط النهائي مع الإطارات واستغلال العرض الكامل main_layout.addWidget(top_frame) main_layout.addWidget(self.clients_table, 1) # إعطاء الجدول أولوية في التمدد main_layout.addWidget(bottom_frame) # ضمان استغلال العرض الكامل للتخطيط الرئيسي main_layout.setContentsMargins(5, 5, 5, 5) # هوامش صغيرة main_layout.setSpacing(5) # مسافات صغيرة بين العناصر self.setLayout(main_layout) # ضمان أن الواجهة تستغل العرض الكامل self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding) def refresh_data(self): """تحديث بيانات العملاء مع الحفاظ الكامل على التنسيق - محسن""" print(" تحديث البيانات مع الحفاظ الكامل على التنسيق...") # الحصول على جميع العملاء من قاعدة البيانات clients = self.session.query(Client).all() # استخدام التحديث الذكي بدلاً من populate_table_simple self.smart_update_table_data_with_colors(clients) # فرض تطبيق جميع التحسينات بعد التحديث self.apply_immediate_updates() self.apply_balance_colors() self.apply_status_colors() # تهيئة الأزرار - تظهر مفعلة عند البدء self.initialize_button_states() print(" تم تحديث البيانات مع الحفاظ الكامل على التنسيق") def create_advanced_clients_table(self): print(" بدء إنشاء جدول العملاء المتطور...") # إنشاء الجدول الأساسي مع تحسينات مرئية self.clients_table = QTableWidget() # إضافة العلامة المائية للجدول self.add_watermark_to_clients_table() # تطبيق نمط متطور جداً ومبتكر مع تأثيرات بصرية متقدمة self.clients_table.setStyleSheet(""" QTableWidget { gridline-color: rgba(44, 62, 80, 0.3); background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f8fafc, stop:0.5 #f1f5f9, stop:1 #e2e8f0); border: 4px solid transparent; border-image: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #667eea, stop:0.25 #764ba2, stop:0.5 #f093fb, stop:0.75 #f5576c, stop:1 #4facfe) 1; border-radius: 20px; font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif; font-size: 14px; selection-background-color: rgba(102, 126, 234, 0.25); alternate-background-color: rgba(241, 245, 249, 0.5); outline: none; } QTableWidget::item { padding: 15px 10px; border: 2px solid rgba(102, 126, 234, 0.15); border-left: 5px solid rgba(102, 126, 234, 0.4); border-right: 5px solid rgba(102, 126, 234, 0.4); text-align: center; min-height: 40px; max-height: 55px; font-weight: 600; font-size: 14px; border-radius: 12px; margin: 3px; background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(255, 255, 255, 0.95), stop:1 rgba(248, 250, 252, 0.95)); /* خلفية شفافة متدرجة تسمح بظهور ألوان الرصيد */ box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05); } QTableWidget::item:selected { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(102, 126, 234, 0.8), stop:0.5 rgba(118, 75, 162, 0.8), stop:1 rgba(240, 147, 251, 0.8)) !important; color: white !important; border: 4px solid rgba(255, 255, 255, 0.8) !important; border-left: 6px solid #ffd700 !important; border-right: 6px solid #ffd700 !important; border-radius: 15px !important; font-weight: bold !important; box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4) !important; transform: scale(1.02) !important; } QTableWidget::item:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(102, 126, 234, 0.1), stop:1 rgba(102, 126, 234, 0.2)) !important; border: 4px solid rgba(102, 126, 234, 0.6) !important; border-left: 6px solid #4facfe !important; border-right: 6px solid #4facfe !important; border-radius: 15px !important; box-shadow: 0px 6px 16px rgba(102, 126, 234, 0.3) !important; transform: translateY(-2px) scale(1.01) !important; transition: all 0.3s ease !important; } QTableWidget::item:selected:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9), stop:1 rgba(102, 126, 234, 0.9)) !important; border: 5px solid rgba(255, 255, 255, 0.9) !important; border-left: 8px solid #ffd700 !important; border-right: 8px solid #ffd700 !important; box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important; transform: scale(1.03) !important; } QHeaderView::section { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #667eea, stop:0.2 #764ba2, stop:0.4 #f093fb, stop:0.6 #f5576c, stop:0.8 #4facfe, stop:1 #00f2fe); color: white; padding: 18px 12px; border: 3px solid rgba(255, 255, 255, 0.3); border-bottom: 5px solid rgba(255, 255, 255, 0.5); font-weight: bold; font-size: 15px; text-align: center; border-radius: 15px 15px 0 0; margin: 0px; min-height: 60px; max-height: 60px; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3); } QHeaderView::section:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4facfe, stop:0.3 #00f2fe, stop:0.6 #667eea, stop:1 #764ba2); border: 5px solid rgba(255, 255, 255, 0.6); border-bottom: 6px solid #ffd700; transform: scale(1.02); box-shadow: 0px 4px 12px rgba(102, 126, 234, 0.4); } QHeaderView::section:pressed { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF); border: 4px solid rgba(255, 255, 255, 0.8); border-bottom: 7px solid #ffd700; transform: scale(0.98); } QScrollBar:vertical { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 rgba(102, 126, 234, 0.1), stop:1 rgba(102, 126, 234, 0.2)); width: 18px; border-radius: 9px; margin: 3px; border: 2px solid rgba(102, 126, 234, 0.3); } QScrollBar::handle:vertical { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb); border-radius: 7px; min-height: 30px; border: 2px solid rgba(255, 255, 255, 0.4); margin: 2px; } QScrollBar::handle:vertical:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #4facfe, stop:0.5 #00f2fe, stop:1 #667eea); border: 3px solid rgba(255, 255, 255, 0.6); transform: scale(1.1); } QScrollBar::handle:vertical:pressed { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF); border: 3px solid rgba(255, 255, 255, 0.8); } QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical { height: 0px; } QScrollBar:horizontal { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(102, 126, 234, 0.1), stop:1 rgba(102, 126, 234, 0.2)); height: 18px; border-radius: 9px; margin: 3px; border: 2px solid rgba(102, 126, 234, 0.3); } QScrollBar::handle:horizontal { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb); border-radius: 7px; min-width: 30px; border: 2px solid rgba(255, 255, 255, 0.4); margin: 2px; } QScrollBar::handle:horizontal:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4facfe, stop:0.5 #00f2fe, stop:1 #667eea); border: 3px solid rgba(255, 255, 255, 0.6); } QScrollBar::handle:horizontal:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #2563EB, stop:0.5 #1D4ED8, stop:1 #1E40AF); border: 3px solid rgba(255, 255, 255, 0.8); } QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal { width: 0px; } print(" تم إنشاء الجدول الأساسي") # تكوين الجدول المتطور self.setup_table_structure() print(" تم إعداد هيكل الجدول") self.setup_table_styling() print(" تم إعداد تنسيق الجدول") self.setup_table_behavior() print(" تم إعداد سلوك الجدول") self.setup_advanced_features() print(" تم إعداد المميزات المتقدمة") # إعادة تطبيق الأعراض للتأكد من تطبيقها self.apply_column_widths() print(f" تم تطبيق أعراض الأعمدة - الهاتف: {self.clients_table.columnWidth(2)}px, الرصيد: {self.clients_table.columnWidth(5)}px") # تطبيق ألوان الرصيد فور إنشاء الجدول if hasattr(self, 'force_balance_colors_final'): self.force_balance_colors_final() print(" تم تطبيق ألوان الرصيد فور إنشاء الجدول") # تطبيق التحسينات الجديدة فوراً self.apply_immediate_updates() # تطبيق التحسينات فوراً عند إنشاء الجدول (بدون مؤقت) # تم إزالة المؤقت لتجنب التحديث التلقائي غير المرغوب فيه print(" تم إنشاء جدول العملاء المتطور بنجاح!") # إنشاء بيانات تجريبية إذا لم توجد بيانات if self.clients_table.rowCount() == 0: print("🧪 لا توجد بيانات، إنشاء بيانات تجريبية...") self.create_simple_test_data() # تطبيق التحسينات النهائية مرة واحدة print(" تطبيق التحسينات النهائية...") # self.apply_final_enhancements() # محذوف - استخدام apply_visual_enhancements_after_populate بدلاً منه self.apply_visual_enhancements_after_populate() # فرض تطبيق الأعراض الصحيحة في النهاية self.apply_correct_widths() def add_watermark_to_clients_table(self): """إضافة العلامة المائية لجدول العملاء""" # إنشاء فئة مخصصة للجدول مع العلامة المائية class WatermarkTableWidget(QTableWidget): def paintEvent(self, event): super().paintEvent(event) # رسم العلامة المائية painter = QPainter(self.viewport()) painter.setRenderHint(QPainter.Antialiasing) # إعداد الخط والنص - حجم أكبر يناسب عرض وارتفاع الجدول viewport_rect = self.viewport().rect() # حساب حجم الخط بناءً على أبعاد الجدول font_size = min(viewport_rect.width() // 8, viewport_rect.height() // 4) font_size = max(font_size, 60) # حد أدنى 60 font_size = min(font_size, 150) # حد أقصى 150 font = QFont("Arial", font_size, QFont.Bold) painter.setFont(font) painter.setPen(QColor(200, 200, 200, 60)) # شفافية أكثر للحجم الأكبر # النص والموضع text = "Smart Finish" rect = self.viewport().rect() painter.drawText(rect, Qt.AlignCenter, text) painter.end() # استبدال الجدول العادي بالجدول مع العلامة المائية if hasattr(self, 'clients_table'): # نسخ الخصائص الحالية parent = self.clients_table.parent() geometry = self.clients_table.geometry() # إنشاء جدول جديد مع العلامة المائية new_table = WatermarkTableWidget() new_table.setParent(parent) new_table.setGeometry(geometry) # استبدال المرجع self.clients_table = new_table print(" تم إضافة العلامة المائية لجدول العملاء") def setup_table_structure(self): # تحديد عدد الأعمدة والعناوين المحسنة self.clients_table.setColumnCount(9) # عدد مناسب من الأعمدة # عناوين محسنة ومختصرة مع أيقونات مختلفة headers = [ " ID", # رقم مسلسل " اسم العميل", # الاسم كاملاً " الهاتف", # رقم الهاتف " الإيميل", # البريد الإلكتروني " العنوان", # العنوان " الرصيد", # الرصيد الحالي " ملاحظات", # الملاحظات " الحالة", # حالة العميل " التاريخ" # تاريخ الإنشاء ] self.clients_table.setHorizontalHeaderLabels(headers) def setup_table_styling(self): """إعداد تنسيق وأسلوب الجدول""" header = self.clients_table.horizontalHeader() # تحسين خط العناوين - خط مميز ومتطور header_font = QFont("Segoe UI", 16, QFont.Bold) # خط أكبر للعناوين header_font.setLetterSpacing(QFont.AbsoluteSpacing, 1.5) # تباعد أحرف أوسع header_font.setStyleHint(QFont.SansSerif) # نمط خط احترافي header.setFont(header_font) # تحسين ارتفاع رأس الجدول مع المزيد من الأناقة header.setFixedHeight(65) # ارتفاع أكبر للعناوين المتطورة # إضافة تأثيرات بصرية للعناوين header.setDefaultAlignment(Qt.AlignCenter) # توسيط العناوين # إزالة التنسيق المعقد من الرأس أيضاً # سيتم تطبيق الرأس الأسود البسيط لاحقاً # إعداد أعراض الأعمدة مع زيادة كبيرة للهاتف والرصيد # تحديد الأعراض المحسنة مع زيادة واضحة للهاتف والرصيد fixed_widths = { 0: 120, # الرقم - عرض أكبر 2: 220, # الهاتف - عرض كبير للأرقام الطويلة 5: 200, # الرصيد - عرض كبير للمبالغ مع العملة 7: 120, # الحالة - مناسب 8: 90 # التاريخ - عرض أصغر } # تطبيق الأعراض الثابتة for col, width in fixed_widths.items(): self.clients_table.setColumnWidth(col, width) # إعداد سلوك تغيير الحجم لاستغلال العرض الكامل header.setSectionResizeMode(0, header.Fixed) # الرقم ثابت header.setSectionResizeMode(1, header.Stretch) # الاسم يتمدد header.setSectionResizeMode(2, header.Fixed) # الهاتف ثابت header.setSectionResizeMode(3, header.Stretch) # الإيميل يتمدد header.setSectionResizeMode(4, header.Stretch) # العنوان يتمدد header.setSectionResizeMode(5, header.Fixed) # الرصيد ثابت header.setSectionResizeMode(6, header.Stretch) # ملاحظات يتمدد header.setSectionResizeMode(7, header.Fixed) # الحالة ثابت header.setSectionResizeMode(8, header.Fixed) # التاريخ ثابت - مهم! # تحسين ارتفاع الصفوف vertical_header = self.clients_table.verticalHeader() vertical_header.setDefaultSectionSize(35) # ارتفاع مناسب vertical_header.setMinimumSectionSize(30) vertical_header.setMaximumSectionSize(50) vertical_header.hide() # إخفاء أرقام الصفوف لتوفير مساحة def setup_table_behavior(self): # تمكين الميزات التفاعلية الأصلية self.clients_table.setMouseTracking(True) self.clients_table.setAlternatingRowColors(False) # تعطيل التلوين التلقائي لتجنب التضارب self.clients_table.setSortingEnabled(True) self.clients_table.setSelectionBehavior(QTableWidget.SelectRows) self.clients_table.setSelectionMode(QTableWidget.SingleSelection) # إرجاع التحديد الفردي # ربط الأحداث self.clients_table.cellEntered.connect(self.show_enhanced_tooltip) self.clients_table.doubleClicked.connect(self.edit_client) self.clients_table.itemSelectionChanged.connect(self.on_selection_changed) # إضافة خاصية إلغاء التحديد عند النقر في مكان فارغ self.clients_table.mousePressEvent = self.table_mouse_press_event # إزالة التنسيق المعقد والاكتفاء بالتنسيق الأصلي # سيتم تطبيق الإطار الأسود لاحقاً في force_black_border_style() def table_mouse_press_event(self, event): """معالجة النقر في الجدول لإلغاء التحديد عند النقر في مكان فارغ""" # الحصول على العنصر المنقور عليه item = self.clients_table.itemAt(event.pos()) if item is None: # النقر في مكان فارغ - إلغاء التحديد self.clients_table.clearSelection() self.clients_table.setCurrentItem(None) # تعطيل الأزرار التي تحتاج تحديد self.update_button_states(False) else: # النقر على عنصر - تفعيل الأزرار self.update_button_states(True) # استدعاء الدالة الأصلية QTableWidget.mousePressEvent(self.clients_table, event) def initialize_button_states(self): try: # قائمة أسماء جميع الأزرار all_button_names = [ 'add_button', 'edit_button', 'delete_button', 'balance_button', 'attachments_button', 'details_button', 'call_button', 'export_button', 'report_button', 'statistics_button', 'backup_button', 'restore_button' ] # تفعيل جميع الأزرار for button_name in all_button_names: if hasattr(self, button_name): button = getattr(self, button_name) if button: button.setEnabled(True) # إزالة أي شفافية current_style = button.styleSheet() new_style = current_style.replace('opacity: 0.5;', '').replace('opacity:0.5;', '') button.setStyleSheet(new_style) except Exception as e: print(f"خطأ في تهيئة حالة الأزرار: {e}") def update_button_states(self, has_selection): """تحديث حالة الأزرار حسب وجود تحديد - فقط الأزرار التي تحتاج تحديد""" try: # قائمة أسماء الأزرار التي تحتاج تحديد فقط selection_dependent_buttons = [ 'edit_button', 'delete_button', 'balance_button', 'attachments_button', 'details_button', 'call_button' ] for button_name in selection_dependent_buttons: if hasattr(self, button_name): button = getattr(self, button_name) if button: button.setEnabled(has_selection) # تغيير الشفافية بصرياً current_style = button.styleSheet() if has_selection: # إزالة الشفافية new_style = current_style.replace('opacity: 0.5;', '').replace('opacity:0.5;', '') button.setStyleSheet(new_style) else: # إضافة الشفافية if 'opacity:' not in current_style: button.setStyleSheet(current_style + ' opacity: 0.5;') except Exception as e: print(f"خطأ في تحديث حالة الأزرار: {e}") # ضمان استغلال العرض الكامل بدون تمديد العمود الأخير self.clients_table.horizontalHeader().setStretchLastSection(False) # إيقاف تمديد العمود الأخير self.clients_table.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded) self.clients_table.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded) # إعادة تطبيق التنسيق الأسود للتأكد من عدم تداخل التنسيقات self.force_black_border_style() def force_black_border_style(self): try: # الحصول على التنسيق الحالي وتعديل الإطار فقط current_style = self.clients_table.styleSheet() # إضافة الإطار الأسود فقط black_border_addition = """ QTableWidget { border: 3px solid #000000 !important; } # دمج التنسيق الحالي مع الإطار الأسود combined_style = current_style + black_border_addition self.clients_table.setStyleSheet(combined_style) print(" تم تطبيق الإطار الأسود البسيط") except Exception as e: print(f" خطأ في تطبيق الإطار الأسود: {str(e)}") def darken_color(self, color): """تعتيم اللون""" try: # إزالة # إذا كانت موجودة color = color.replace('#', '') # تحويل إلى RGB r = int(color[0:2], 16) g = int(color[2:4], 16) b = int(color[4:6], 16) # تعتيم بنسبة 20% r = max(0, int(r * 0.8)) g = max(0, int(g * 0.8)) b = max(0, int(b * 0.8)) return f"#{r:02x}{g:02x}{b:02x}" except: return color def lighten_color(self, color): try: # إزالة # إذا كانت موجودة color = color.replace('#', '') # تحويل إلى RGB r = int(color[0:2], 16) g = int(color[2:4], 16) b = int(color[4:6], 16) # تفتيح بنسبة 20% r = min(255, int(r * 1.2)) g = min(255, int(g * 1.2)) b = min(255, int(b * 1.2)) return f"#{r:02x}{g:02x}{b:02x}" except: return color def get_filter_button_style(self, color): """إنشاء تنسيق أزرار التصفية""" QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color}, stop:1 {self.darken_color(color)}); color: white; border: none; border-radius: 10px; padding: 10px 15px; font-size: 12px; font-weight: bold; min-width: 100px; margin: 2px; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {self.lighten_color(color)}, stop:1 {color}); border: 2px solid {self.lighten_color(color)}; }} QPushButton:pressed {{ background: {self.darken_color(color)}; border: 2px solid {self.darken_color(self.darken_color(color))}; }} """ def get_tool_button_style(self, color): return f""" QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color}, stop:1 {self.darken_color(color)}); color: white; border: none; border-radius: 12px; padding: 12px 18px; font-size: 13px; font-weight: bold; min-width: 140px; margin: 2px; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {self.lighten_color(color)}, stop:1 {color}); border: 2px solid {self.lighten_color(color)}; }} QPushButton:pressed {{ background: {self.darken_color(color)}; border: 2px solid {self.darken_color(self.darken_color(color))}; }} def get_enhanced_button_style(self, color): """إنشاء تنسيق أزرار محسن مع ألوان متطورة وتأثيرات جذابة""" QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color}, stop:0.5 {self.darken_color(color)}, stop:1 {self.darken_color(self.darken_color(color))}); color: white; border: 2px solid {self.darken_color(color)}; border-radius: 10px; padding: 6px 8px; font-size: 10px; font-weight: bold; min-width: 70px; max-height: 35px; margin: 0px; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {self.lighten_color(color)}, stop:0.5 {color}, stop:1 {self.darken_color(color)}); border: 2px solid {self.lighten_color(color)}; }} QPushButton:pressed {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {self.darken_color(color)}, stop:1 {self.darken_color(self.darken_color(color))}); border: 2px solid {self.darken_color(self.darken_color(color))}; }} """ def get_compact_button_style(self, color): return f""" QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color}, stop:0.5 {self.darken_color(color)}, stop:1 {self.darken_color(self.darken_color(color))}); color: white; border: 2px solid {self.darken_color(color)}; border-radius: 10px; padding: 6px; font-size: 12px; font-weight: bold; min-width: 40px; max-width: 40px; max-height: 35px; margin: 0px; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {self.lighten_color(color)}, stop:0.5 {color}, stop:1 {self.darken_color(color)}); border: 2px solid {self.lighten_color(color)}; }} QPushButton:pressed {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {self.darken_color(color)}, stop:1 {self.darken_color(self.darken_color(color))}); border: 2px solid {self.darken_color(self.darken_color(color))}; }} def setup_advanced_features(self): """إعداد المميزات المتقدمة والحديثة للجدول""" # 1. تفعيل السحب والإفلات المتقدم self.clients_table.setDragDropMode(QTableWidget.InternalMove) self.clients_table.setDefaultDropAction(Qt.MoveAction) # 2. تفعيل التحديد المتعدد المتقدم self.clients_table.setSelectionMode(QTableWidget.ExtendedSelection) # 3. تفعيل القائمة السياقية المتقدمة self.clients_table.setContextMenuPolicy(Qt.CustomContextMenu) self.clients_table.customContextMenuRequested.connect(self.show_advanced_context_menu) # 4. تفعيل التصفية السريعة بالكتابة self.clients_table.keyPressEvent = self.advanced_key_press_event # 5. تفعيل التلميحات المتقدمة والذكية self.clients_table.setMouseTracking(True) self.clients_table.cellEntered.connect(self.show_advanced_tooltip) # 6. تعطيل التحديث التلقائي لتجنب التحديث غير المرغوب فيه # from PyQt5.QtCore import QTimer # self.auto_refresh_timer = QTimer() # self.auto_refresh_timer.timeout.connect(self.auto_refresh_data) # self.auto_refresh_timer.start(30000) # كل 30 ثانية - معطل # 7. تم حذف حفظ حالة الجدول لتبسيط الكود # self.save_table_state() # محذوف # 8. تم حذف التصدير المتقدم لتبسيط الكود # self.setup_export_features() # محذوف # 9. تم حذف البحث المتقدم لتبسيط الكود # self.setup_advanced_search() # محذوف # 10. تفعيل الإحصائيات المباشرة والتفاعلية self.setup_live_statistics() # 11. تفعيل التحليل الذكي للبيانات try: self.setup_smart_analytics() except Exception as e: print(f" خطأ في إعداد التحليل الذكي: {str(e)}") # 12. تفعيل التنبيهات الذكية try: self.setup_smart_notifications() except Exception as e: print(f" خطأ في إعداد التنبيهات الذكية: {str(e)}") # 13. تفعيل التصفية المتقدمة try: self.setup_advanced_filtering() except Exception as e: print(f" خطأ في إعداد التصفية المتقدمة: {str(e)}") # 14. تفعيل التجميع والتصنيف الذكي try: self.setup_smart_grouping() except Exception as e: print(f" خطأ في إعداد التجميع الذكي: {str(e)}") # 15. تفعيل التصور المرئي للبيانات try: self.setup_data_visualization() except Exception as e: print(f" خطأ في إعداد التصور المرئي: {str(e)}") # 16. تفعيل التنبؤ الذكي try: self.setup_predictive_analytics() except Exception as e: print(f" خطأ في إعداد التنبؤ الذكي: {str(e)}") # 17. تفعيل التكامل مع الخدمات الخارجية try: self.setup_external_integrations() except Exception as e: print(f" خطأ في إعداد التكاملات الخارجية: {str(e)}") # 18. تفعيل الأمان المتقدم try: self.setup_advanced_security() except Exception as e: print(f" خطأ في إعداد الأمان المتقدم: {str(e)}") # 19. تفعيل التخصيص المتقدم try: self.setup_advanced_customization() except Exception as e: print(f" خطأ في إعداد التخصيص المتقدم: {str(e)}") # 20. تفعيل الذكاء الاصطناعي try: self.setup_ai_features() except Exception as e: print(f" خطأ في إعداد الذكاء الاصطناعي: {str(e)}") def apply_immediate_updates(self): try: print(" تطبيق التحسينات الجديدة فوراً...") # 1. تطبيق خط موحد وبولد على جميع خلايا الجدول for row in range(self.clients_table.rowCount()): for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) if item: # تطبيق خط موحد مع Bold (حجم ثابت 11px) font = QFont("Segoe UI", 11, QFont.Bold) # خط موحد وبولد item.setFont(font) # 2. تطبيق ألوان الرصيد الجديدة (أحمر وأخضر فقط) self.apply_simple_balance_colors() # 3. تحديث الحالة للعملاء (رمادي رصاصي للعادي) self.update_status_colors() # 4. تلوين "غير متوفر" باللون الأحمر self.color_unavailable_text() # 5. تطبيق الألوان الصحيحة للإيميلات self.fix_email_colors() # 6. فرض تحديث الجدول self.clients_table.viewport().update() self.clients_table.repaint() print(" تم تطبيق التحسينات الجديدة بنجاح") except Exception as e: print(f" خطأ في تطبيق التحسينات الفورية: {str(e)}") def apply_simple_balance_colors(self): """تطبيق ألوان الرصيد البسيطة - أحمر وأخضر فقط""" try: print(" تطبيق ألوان الرصيد البسيطة...") for row in range(self.clients_table.rowCount()): balance_item = self.clients_table.item(row, 5) # عمود الرصيد if balance_item: try: # استخراج القيمة الرقمية balance_text = balance_item.text().replace(',', '').replace('ر.س', '').strip() balance = float(balance_text) # تطبيق الألوان البسيطة if balance > 0: # أخضر للأرصدة الموجبة balance_item.setBackground(QColor(200, 255, 200)) # أخضر فاتح balance_item.setForeground(QColor(0, 150, 0)) # أخضر داكن elif balance < 0: # أحمر للأرصدة السالبة balance_item.setBackground(QColor(255, 200, 200)) # أحمر فاتح balance_item.setForeground(QColor(200, 0, 0)) # أحمر داكن else: # رمادي رصاصي للرصيد صفر balance_item.setBackground(QColor(220, 220, 220)) # رمادي فاتح balance_item.setForeground(QColor(80, 80, 80)) # رمادي رصاصي داكن # تطبيق خط موحد ثابت للرصيد font = QFont("Segoe UI", 11, QFont.Bold) # حجم ثابت 11 balance_item.setFont(font) print(f" تم تلوين الرصيد للصف {row}: {balance}") except ValueError: print(f" خطأ في قراءة الرصيد للصف {row}") continue print(" تم تطبيق ألوان الرصيد البسيطة بنجاح") except Exception as e: print(f" خطأ في تطبيق ألوان الرصيد البسيطة: {str(e)}") def update_status_colors(self): try: for row in range(self.clients_table.rowCount()): status_item = self.clients_table.item(row, 7) # عمود الحالة if status_item and "عادى" in status_item.text(): # تطبيق اللون الأسود للحالة العادية مع العلامة السوداء status_item.setForeground(QColor("#000000")) # أسود للنص والعلامة status_item.setBackground(QColor("#f8f9fa")) # خلفية رمادية فاتحة # التأكد من النص الصحيح مع العلامة السوداء if status_item.text() != " عادى": status_item.setText(" عادى") # النص مع علامة سوداء print(f" تم تحديث لون الحالة العادية للصف {row}") except Exception as e: print(f" خطأ في تحديث ألوان الحالة: {str(e)}") def color_unavailable_text(self): """تلوين نص 'غير متوفر' باللون الأحمر في جميع الأعمدة""" try: print(" تلوين نص 'غير متوفر' باللون الأحمر...") for row in range(self.clients_table.rowCount()): for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) if item and item.text() == "غير متوفر": # تطبيق اللون الأحمر item.setForeground(QColor("#DC2626")) # أحمر item.setBackground(QColor("#FEE2E2")) # خلفية حمراء فاتحة print(f" تم تلوين 'غير متوفر' في الصف {row}, العمود {col}") print(" تم تلوين جميع نصوص 'غير متوفر' بنجاح") except Exception as e: print(f" خطأ في تلوين نص 'غير متوفر': {str(e)}") def fix_email_colors(self): try: print(" إصلاح ألوان الإيميلات...") for row in range(self.clients_table.rowCount()): email_item = self.clients_table.item(row, 3) # عمود الإيميل if email_item and email_item.text(): email_text = email_item.text() # إذا كان النص "غير متوفر" فلا نغير لونه (يبقى أحمر) if email_text != "غير متوفر" and "غير متوفر" not in email_text: # تطبيق اللون الأسود الموحد لجميع الإيميلات email_item.setForeground(QColor("#000000")) # أسود موحد email_item.setBackground(QColor("#f8f9fa")) # خلفية رمادية فاتحة print(f" تم إصلاح لون الإيميل في الصف {row}: {email_text}") print(" تم إصلاح جميع ألوان الإيميلات بنجاح") except Exception as e: print(f" خطأ في إصلاح ألوان الإيميلات: {str(e)}") def smart_update_table_data(self, clients): """تحديث ذكي للبيانات مع الحفاظ الكامل على التنسيق""" try: print(" تحديث ذكي للبيانات مع الحفاظ الكامل على التنسيق...") # تحديث عدد الصفوف إذا لزم الأمر current_rows = self.clients_table.rowCount() needed_rows = len(clients) if current_rows != needed_rows: self.clients_table.setRowCount(needed_rows) # تحديث البيانات في الخلايا الموجودة بدون إعادة إنشاء for row, client in enumerate(clients): # البيانات الأساسية مع أيقونات حسب حالة العميل # اختيار الأيقونة حسب الرصيد والحالة if client.balance > 10000: icon = "" # VIP - مبلغ كبير جداً status = "VIP" elif client.balance > 1000: icon = "" # ممتاز - مبلغ كبير status = "ممتاز" elif client.balance > 0: icon = "🟢" # نشط - مبلغ موجب status = "نشط" elif client.balance == 0: icon = "" # عادي - بدون رصيد status = "عادي" elif client.balance > -1000: icon = "🟡" # مدين بسيط status = "مدين بسيط" elif client.balance > -5000: icon = "" # تحذير - مدين متوسط status = "مدين متوسط" else: icon = "" # خطر - مدين كبير status = "خطر عالي" id_text = f"{client.id} {icon}" print(f" تحديث رقم العميل {client.id} مع أيقونة {icon} ({status}): {id_text}") data = [ id_text, # رقم العميل مع أيقونة مختلفة client.name or "غير متوفر", client.phone or "غير متوفر", client.email or "غير متوفر", client.address or "غير متوفر", f"{client.balance:,.0f}", # إزالة ر.س client.notes or "غير متوفر", "🟢 نشط" if client.balance > 0 else (" عادى" if client.balance == 0 else " مدين"), client.created_at.strftime('%Y-%m-%d') if hasattr(client, 'created_at') and client.created_at else "غير متوفر" ] # تحديث النص فقط بدون تغيير التنسيق for col, text in enumerate(data): item = self.clients_table.item(row, col) if item is None: # إنشاء خلية جديدة فقط إذا لم تكن موجودة item = QTableWidgetItem() self.clients_table.setItem(row, col, item) # تطبيق التنسيق الأساسي للخلايا الجديدة فقط unified_bold_font = QFont("Segoe UI", 11, QFont.Bold) item.setFont(unified_bold_font) item.setTextAlignment(Qt.AlignCenter) # تحديث النص فقط if item.text() != text: item.setText(text) # تطبيق الألوان حسب المحتوى (فقط عند تغيير النص) if text == "غير متوفر": item.setForeground(QColor("#DC2626")) # أحمر item.setBackground(QColor("#FEE2E2")) # خلفية حمراء فاتحة elif col == 5: # عمود الرصيد if client.balance > 0: item.setForeground(QColor("#155724")) # أخضر داكن item.setBackground(QColor("#d1ecf1")) # أخضر فاتح elif client.balance < 0: item.setForeground(QColor("#721c24")) # أحمر داكن item.setBackground(QColor("#f8d7da")) # أحمر فاتح else: item.setForeground(QColor("#495057")) # رمادي داكن item.setBackground(QColor("#e9ecef")) # رمادي فاتح elif col == 7: # عمود الحالة if client.balance > 0: item.setForeground(QColor("#38a169")) # أخضر item.setBackground(QColor("#c6f6d5")) # خلفية خضراء elif client.balance == 0: item.setForeground(QColor("#000000")) # أسود للعادى item.setBackground(QColor("#f8f9fa")) # خلفية رمادية فاتحة else: item.setForeground(QColor("#e53e3e")) # أحمر item.setBackground(QColor("#fed7d7")) # خلفية حمراء elif col not in [5, 7] and text != "غير متوفر": # الحفاظ على الألوان الافتراضية للخلايا العادية if item.foreground().color() != QColor("#000000"): item.setForeground(QColor("#000000")) # أسود if item.background().color() != QColor("#ffffff"): item.setBackground(QColor("#ffffff")) # أبيض print(" تم التحديث الذكي مع الحفاظ الكامل على التنسيق") print(" تم تحديث جميع أرقام العملاء مع أيقونات حسب الحالة بنجاح في عمود رقم") except Exception as e: print(f" خطأ في التحديث الذكي: {str(e)}") def smart_update_table_data_with_colors(self, clients): try: print(" تحديث البيانات مع الحفاظ على الألوان...") # تحديث عدد الصفوف إذا لزم الأمر current_rows = self.clients_table.rowCount() needed_rows = len(clients) if current_rows != needed_rows: self.clients_table.setRowCount(needed_rows) # تحديث البيانات في الخلايا الموجودة بدون إعادة إنشاء for row, client in enumerate(clients): # البيانات الأساسية مع أيقونات حسب حالة العميل # اختيار الأيقونة حسب الرصيد والحالة if client.balance > 10000: icon = "" # VIP - مبلغ كبير جداً elif client.balance > 1000: icon = "" # ممتاز - مبلغ كبير elif client.balance > 0: icon = "🟢" # نشط - مبلغ موجب elif client.balance == 0: icon = "" # عادي - بدون رصيد elif client.balance > -1000: icon = "🟡" # مدين بسيط elif client.balance > -5000: icon = "" # تحذير - مدين متوسط else: icon = "" # خطر - مدين كبير id_text = f"{client.id} {icon}" data = [ id_text, # رقم العميل مع أيقونة حسب الحالة client.name or "غير متوفر", client.phone or "غير متوفر", client.email or "غير متوفر", client.address or "غير متوفر", f"{client.balance:,.0f}", # إزالة ر.س client.notes or "غير متوفر", "🟢 نشط" if client.balance > 0 else (" عادى" if client.balance == 0 else " مدين"), client.created_at.strftime('%Y-%m-%d') if hasattr(client, 'created_at') and client.created_at else "غير متوفر" ] # تحديث البيانات في الخلايا for col, value in enumerate(data): if col < self.clients_table.columnCount(): item = self.clients_table.item(row, col) if item is None: item = QTableWidgetItem(str(value)) self.clients_table.setItem(row, col, item) else: item.setText(str(value)) # تطبيق خط موحد مع Bold (حجم ثابت 11px) font = QFont("Segoe UI", 11, QFont.Bold) # خط موحد وبولد item.setFont(font) item.setTextAlignment(Qt.AlignCenter) # تطبيق ألوان "غير متوفر" فوراً if str(value) == "غير متوفر": item.setForeground(QColor("#dc2626")) # أحمر # تطبيق جميع الألوان والتنسيقات فوراً self.apply_immediate_updates() # تطبيق ألوان الرصيد self.apply_balance_colors() # تطبيق ألوان الحالة self.apply_status_colors() except Exception as e: print(f" خطأ في التحديث مع الألوان: {str(e)}") def apply_status_colors(self): """تطبيق ألوان الحالة""" try: for row in range(self.clients_table.rowCount()): status_item = self.clients_table.item(row, 7) # عمود الحالة if status_item and status_item.text(): status_text = status_item.text() if "نشط" in status_text: status_item.setForeground(QColor("#16a34a")) # أخضر status_item.setBackground(QColor("#dcfce7")) # خلفية خضراء فاتحة elif "عادى" in status_text: status_item.setForeground(QColor("#000000")) # أسود status_item.setBackground(QColor("#f8f9fa")) # خلفية رمادية فاتحة elif "مدين" in status_text: status_item.setForeground(QColor("#dc2626")) # أحمر status_item.setBackground(QColor("#fef2f2")) # خلفية حمراء فاتحة except Exception as e: print(f" خطأ في تطبيق ألوان الحالة: {str(e)}") def update_table_data_only(self, clients): # استخدام الدالة الذكية الجديدة self.smart_update_table_data(clients) def preserve_formatting(self): """الحفاظ على التنسيق والخطوط عند التنقل بين التبويبات""" try: print(" الحفاظ على التنسيق عند التنقل...") # تطبيق الخط الموحد والبولد على جميع الخلايا self.apply_immediate_updates() # فرض تطبيق ألوان الرصيد self.force_balance_colors_final() # تطبيق التحسينات الفورية self.apply_immediate_updates() print(" تم الحفاظ على التنسيق بنجاح") except Exception as e: print(f" خطأ في الحفاظ على التنسيق: {str(e)}") def apply_updates_with_message(self): try: print(" المستخدم طلب تطبيق التحسينات...") # تطبيق التحسينات self.apply_immediate_updates() # عرض رسالة نجاح show_info_message("تم التحديث", " تم تطبيق التحسينات بنجاح!\n\n" " الخط: تم توحيده إلى 11px مع Bold\n" "🟢 الرصيد الموجب: أخضر\n" " الرصيد السالب: أحمر\n" " الرصيد صفر: رمادي رصاصي\n" " الحالة العادية: رمادي رصاصي\n" " نص 'غير متوفر': أحمر في جميع الأعمدة") except Exception as e: print(f" خطأ في تطبيق التحسينات مع الرسالة: {str(e)}") show_error_message("خطأ", f"حدث خطأ في تطبيق التحسينات:\n{str(e)}") def apply_column_widths(self): """تطبيق أعراض الأعمدة مرة أخرى للتأكد""" # الأعراض المحددة للأعمدة المهمة column_widths = { 0: 120, # الرقم - عرض أكبر 2: 220, # الهاتف - عرض كبير 5: 200, # الرصيد - عرض كبير 7: 120, # الحالة 8: 90 # التاريخ - عرض أصغر } # تطبيق الأعراض مع التأكد من التطبيق for col, width in column_widths.items(): self.clients_table.setColumnWidth(col, width) # التأكد من أن العمود لا يتمدد if col in [0, 2, 5, 7, 8]: # الأعمدة الثابتة self.clients_table.horizontalHeader().setSectionResizeMode(col, self.clients_table.horizontalHeader().Fixed) def on_selection_changed(self): try: # التحقق من وجود تحديد has_selection = len(self.clients_table.selectedItems()) > 0 # تحديث حالة الأزرار self.update_button_states(has_selection) except Exception as e: print(f"خطأ في معالجة تغيير التحديد: {e}") def populate_table(self, clients): """ملء جدول العملاء المتطور بالبيانات المحسنة والمتقدمة""" print(" ملء الجدول بالبيانات المتطورة والمحسنة...") self.clients_table.setRowCount(0) for row, client in enumerate(clients): self.clients_table.insertRow(row) # خطوط متطورة وموحدة - حجم مناسب مع Bold unified_font = QFont("Segoe UI", 11, QFont.Bold) # خط مناسب مع Bold unified_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.5) unified_font.setStyleHint(QFont.SansSerif) unified_font.setWeight(QFont.Bold) unified_bold_font = QFont("Segoe UI", 11, QFont.Bold) # نفس الحجم للعريض unified_bold_font.setLetterSpacing(QFont.AbsoluteSpacing, 0.8) unified_bold_font.setStyleHint(QFont.SansSerif) unified_bold_font.setWeight(QFont.ExtraBold) # 1. الرقم المسلسل مع تصميم متطور ومميز وأيقونة حسب الحالة # اختيار الأيقونة حسب الرصيد والحالة if client.balance > 10000: icon = "" # VIP - مبلغ كبير جداً elif client.balance > 1000: icon = "" # ممتاز - مبلغ كبير elif client.balance > 0: icon = "🟢" # نشط - مبلغ موجب elif client.balance == 0: icon = "" # عادي - بدون رصيد elif client.balance > -1000: icon = "🟡" # مدين بسيط elif client.balance > -5000: icon = "" # تحذير - مدين متوسط else: icon = "" # خطر - مدين كبير id_display = f"{client.id} {icon}" id_item = QTableWidgetItem(id_display) id_item.setTextAlignment(Qt.AlignCenter) id_item.setFont(unified_bold_font) # ألوان متدرجة حسب رقم العميل if client.id <= 10: # عملاء أوائل - ذهبي id_item.setForeground(QColor("#B8860B")) # ذهبي داكن id_item.setBackground(QColor("#FFF8DC")) # كريمي فاتح id_item.setToolTip(f" عميل مؤسس رقم: {client.id}") elif client.id <= 50: # عملاء مبكرين - فضي id_item.setForeground(QColor("#4682B4")) # أزرق فولاذي id_item.setBackground(QColor("#F0F8FF")) # أزرق فاتح جداً id_item.setToolTip(f" عميل مبكر رقم: {client.id}") else: # عملاء عاديين - رمادي أنيق id_item.setForeground(QColor("#2C3E50")) # رمادي داكن id_item.setBackground(QColor("#ECF0F1")) # رمادي فاتح id_item.setToolTip(f" رقم العميل: {client.id}") self.clients_table.setItem(row, 0, id_item) # 2. اسم العميل مع تصميم جذاب ومتطور # تحسين عرض الاسم display_name = client.name.title() if client.name else "غير محدد" if len(display_name) > 20: display_name = display_name[:17] + "..." name_item = QTableWidgetItem(display_name) name_item.setTextAlignment(Qt.AlignCenter) name_item.setFont(unified_bold_font) # ألوان متدرجة حسب طول الاسم وحالة العميل if client.balance > 10000: # عملاء VIP - ذهبي فاخر name_item.setForeground(QColor("#8B4513")) # بني ذهبي name_item.setBackground(QColor("#FFF8DC")) # كريمي ذهبي name_item.setToolTip(f" عميل VIP: {client.name}\n رصيد عالي: {client.balance:,.0f} ر.س") elif client.balance > 0: # عملاء نشطين - أخضر أنيق name_item.setForeground(QColor("#2F4F4F")) # أخضر داكن name_item.setBackground(QColor("#F0FFF0")) # أخضر فاتح جداً name_item.setToolTip(f" عميل نشط: {client.name}\n رصيد موجب: {client.balance:,.0f} ر.س") elif client.balance < 0: # عملاء مدينين - أحمر تحذيري name_item.setForeground(QColor("#8B0000")) # أحمر داكن name_item.setBackground(QColor("#FFE4E1")) # أحمر فاتح جداً name_item.setToolTip(f" عميل مدين: {client.name}\n مبلغ مستحق: {abs(client.balance):,.0f} ر.س") else: # عملاء عاديين - رمادي أنيق name_item.setForeground(QColor("#2C3E50")) # رمادي داكن name_item.setBackground(QColor("#F8F9FA")) # رمادي فاتح جداً name_item.setToolTip(f" عميل عادي: {client.name}\n رصيد صفر") self.clients_table.setItem(row, 1, name_item) # 3. الهاتف مع تصميم احترافي ومتطور if client.phone: # تنسيق رقم الهاتف بشكل جميل phone_clean = client.phone.replace(" ", "").replace("-", "") if phone_clean.startswith("966"): phone_display = f"+966 {phone_clean[3:6]} {phone_clean[6:9]} {phone_clean[9:]}" elif phone_clean.startswith("05"): phone_display = f"0{phone_clean[1:3]} {phone_clean[3:6]} {phone_clean[6:]}" else: phone_display = client.phone phone_item = QTableWidgetItem(phone_display) phone_item.setFont(unified_bold_font) # ألوان متدرجة حسب نوع الرقم if phone_clean.startswith("966") or phone_clean.startswith("00966"): # رقم دولي - أزرق فاخر phone_item.setForeground(QColor("#1E3A8A")) # أزرق داكن phone_item.setBackground(QColor("#DBEAFE")) # أزرق فاتح phone_item.setToolTip(f" رقم دولي: {phone_display}\n انقر نقرتين للاتصال\n واتساب متاح") elif phone_clean.startswith("05"): # رقم جوال سعودي - أخضر مميز phone_item.setForeground(QColor("#059669")) # أخضر داكن phone_item.setBackground(QColor("#D1FAE5")) # أخضر فاتح phone_item.setToolTip(f" جوال سعودي: {phone_display}\n انقر نقرتين للاتصال\n واتساب متاح") else: # رقم عادي - بنفسجي أنيق phone_item.setForeground(QColor("#7C3AED")) # بنفسجي داكن phone_item.setBackground(QColor("#EDE9FE")) # بنفسجي فاتح phone_item.setToolTip(f" هاتف: {phone_display}\n انقر نقرتين للاتصال") else: phone_item = QTableWidgetItem(" غير متوفر") phone_item.setFont(unified_font) phone_item.setForeground(QColor("#DC2626")) # أحمر للغير متوفر phone_item.setBackground(QColor("#FEE2E2")) # أحمر فاتح جداً phone_item.setToolTip(" لا يوجد رقم هاتف مسجل\n يمكن إضافة رقم من خلال التعديل") phone_item.setTextAlignment(Qt.AlignCenter) self.clients_table.setItem(row, 2, phone_item) # 4. البريد الإلكتروني مع تصميم متطور if client.email: email_text = client.email if len(email_text) > 25: email_display = email_text[:22] + "..." else: email_display = email_text email_item = QTableWidgetItem(email_display) email_item.setFont(unified_font) # تحديد نوع البريد الإلكتروني - جميع الإيميلات بلون أسود موحد # تم توحيد لون جميع الإيميلات إلى الأسود لتجنب الالتباس email_item.setForeground(QColor("#000000")) # أسود موحد لجميع الإيميلات email_item.setBackground(QColor("#f8f9fa")) # خلفية رمادية فاتحة موحدة # تحديد نوع الإيميل في التلميح فقط if "@gmail.com" in client.email.lower(): email_item.setToolTip(f" Gmail: {client.email}\n انقر نقرتين لإرسال إيميل") elif "@outlook.com" in client.email.lower() or "@hotmail.com" in client.email.lower(): email_item.setToolTip(f" Outlook: {client.email}\n انقر نقرتين لإرسال إيميل") elif "@yahoo.com" in client.email.lower(): email_item.setToolTip(f" Yahoo: {client.email}\n انقر نقرتين لإرسال إيميل") else: email_item.setToolTip(f" إيميل: {client.email}\n انقر نقرتين لإرسال إيميل") else: email_item = QTableWidgetItem(" غير متوفر") email_item.setFont(unified_font) email_item.setForeground(QColor("#DC2626")) # أحمر للغير متوفر email_item.setBackground(QColor("#FEE2E2")) # أحمر فاتح email_item.setToolTip(" لا يوجد بريد إلكتروني\n يمكن إضافة إيميل من خلال التعديل") email_item.setTextAlignment(Qt.AlignCenter) self.clients_table.setItem(row, 3, email_item) # 5. العنوان address_text = client.address or "غير متوفر" if len(address_text) > 20: # تقصير العناوين الطويلة address_display = address_text[:17] + "..." else: address_display = address_text address_item = QTableWidgetItem(address_display) address_item.setTextAlignment(Qt.AlignCenter) # توسيط النص if client.address: address_item.setFont(unified_font) # خط موحد address_item.setForeground(QColor("#4a5568")) address_item.setBackground(QColor("#f7fafc")) address_item.setToolTip(f" العنوان: {client.address}\n انقر نقرتين لفتح الخريطة") else: address_item.setFont(unified_font) # خط موحد address_item.setForeground(QColor("#DC2626")) # أحمر للغير متوفر address_item.setBackground(QColor("#FEE2E2")) # أحمر فاتح address_item.setToolTip(" لا يوجد عنوان مسجل\n يمكن إضافة عنوان من خلال التعديل") self.clients_table.setItem(row, 4, address_item) # 6. الرصيد مع تصميم متطور وجذاب (بدون كسور وبدون ر.س) balance_text = f"{client.balance:,.0f}" balance_item = QTableWidgetItem(balance_text) balance_item.setTextAlignment(Qt.AlignCenter) # تطبيق خط موحد ثابت للرصيد balance_font = QFont("Segoe UI", 11, QFont.Bold) # حجم ثابت 11 balance_item.setFont(balance_font) # تلوين الرصيد حسب القيمة مع ألوان واضحة ومميزة if client.balance > 10000: # رصيد عالي جداً - أخضر ذهبي مميز balance_item.setForeground(QColor("#1a5f3f")) # أخضر داكن balance_item.setBackground(QColor("#d4edda")) # أخضر فاتح balance_item.setToolTip(f" رصيد ممتاز: {balance_text}\n عميل VIP") elif client.balance > 0: # رصيد موجب - أخضر واضح balance_item.setForeground(QColor("#155724")) # أخضر داكن balance_item.setBackground(QColor("#d1ecf1")) # أخضر فاتح جداً balance_item.setToolTip(f" رصيد موجب: {balance_text}\n عميل نشط") elif client.balance < -5000: # رصيد سالب عالي - أحمر قوي balance_item.setForeground(QColor("#721c24")) # أحمر داكن جداً balance_item.setBackground(QColor("#f8d7da")) # أحمر فاتح balance_item.setToolTip(f" رصيد مدين عالي: {balance_text}\n يحتاج متابعة") elif client.balance < 0: # رصيد سالب - أحمر واضح balance_item.setForeground(QColor("#856404")) # برتقالي داكن balance_item.setBackground(QColor("#fff3cd")) # أصفر فاتح balance_item.setToolTip(f" رصيد مدين: {balance_text}\n يحتاج متابعة") else: # رصيد صفر - رمادي balance_item.setForeground(QColor("#495057")) # رمادي داكن balance_item.setBackground(QColor("#e9ecef")) # رمادي فاتح balance_item.setToolTip(f" رصيد صفر: {balance_text}\n لا يوجد رصيد") self.clients_table.setItem(row, 5, balance_item) # 7. الملاحظات notes_text = client.notes or "غير متوفر" if len(notes_text) > 15: # تقصير النص الطويل notes_display = notes_text[:12] + "..." else: notes_display = notes_text notes_item = QTableWidgetItem(notes_display) notes_item.setTextAlignment(Qt.AlignCenter) # توسيط النص if client.notes: notes_item.setFont(unified_font) # خط موحد notes_item.setForeground(QColor("#4a5568")) notes_item.setToolTip(client.notes) # النص الكامل في التلميح else: notes_item.setFont(unified_font) # خط موحد notes_item.setForeground(QColor("#DC2626")) # أحمر للغير متوفر notes_item.setBackground(QColor("#FEE2E2")) # أحمر فاتح notes_item.setToolTip(" لا توجد ملاحظات\n يمكن إضافة ملاحظات من خلال التعديل") self.clients_table.setItem(row, 6, notes_item) # 8. الحالة مع تصميم احترافي محسن # تحديد الحالة بناءً على الرصيد مع إضافة حالة "عادي" if client.balance > 0: status_text = "🟢 نشط" status_color = QColor("#38a169") # أخضر للنشط status_bg = QColor("#c6f6d5") # خلفية خضراء فاتحة elif client.balance == 0: status_text = " عادى" # النص مع علامة سوداء status_color = QColor("#000000") # أسود للعادى status_bg = QColor("#f8f9fa") # خلفية رمادية فاتحة else: status_text = " مدين" status_color = QColor("#e53e3e") # أحمر للمدين status_bg = QColor("#fed7d7") # خلفية حمراء فاتحة # إنشاء عنصر الحالة مع تنسيق موحد status_item = QTableWidgetItem(status_text) status_item.setTextAlignment(Qt.AlignCenter) # تطبيق خط موحد للحالة (حجم ثابت 12 عريض) status_font = QFont("Segoe UI", 12, QFont.Bold) status_item.setFont(status_font) # تطبيق الألوان status_item.setForeground(status_color) status_item.setBackground(status_bg) # إضافة تلميح للحالة if client.balance > 0: status_item.setToolTip(f"🟢 عميل نشط\n الرصيد: {client.balance:,.0f}") elif client.balance == 0: status_item.setToolTip(f" عميل عادى\n الرصيد: صفر") else: status_item.setToolTip(f" عميل مدين\n المبلغ المستحق: {abs(client.balance):,.0f}") self.clients_table.setItem(row, 7, status_item) # 9. التاريخ if hasattr(client, 'created_at') and client.created_at: date_text = client.created_at.strftime('%Y-%m-%d') date_item = QTableWidgetItem(date_text) date_item.setForeground(QColor("#4a5568")) # رمادي للتاريخ الموجود date_item.setBackground(QColor("#f7fafc")) # خلفية رمادية فاتحة date_item.setToolTip(f" تاريخ الإضافة: {date_text}") else: date_text = "غير متوفر" date_item = QTableWidgetItem(date_text) date_item.setForeground(QColor("#DC2626")) # أحمر للغير متوفر date_item.setBackground(QColor("#FEE2E2")) # أحمر فاتح date_item.setToolTip(" لا يوجد تاريخ إضافة مسجل") date_item.setTextAlignment(Qt.AlignCenter) date_item.setFont(unified_font) # خط موحد self.clients_table.setItem(row, 8, date_item) # تطبيق الأعراض الجديدة مباشرة مع فرض التطبيق print(" فرض تطبيق الأعراض الجديدة...") # أولاً: تعيين الأعمدة الثابتة header = self.clients_table.horizontalHeader() header.setSectionResizeMode(8, header.Fixed) # التاريخ ثابت # ثانياً: تطبيق الأعراض self.clients_table.setColumnWidth(0, 120) # الرقم self.clients_table.setColumnWidth(2, 220) # الهاتف self.clients_table.setColumnWidth(5, 200) # الرصيد self.clients_table.setColumnWidth(7, 120) # الحالة self.clients_table.setColumnWidth(8, 70) # التاريخ - مهم! print(" تم فرض تطبيق الأعراض الجديدة") # تحديث الإحصائيات المباشرة self.update_live_statistics() # تطبيق التحسينات المرئية مباشرة على البيانات print(" تطبيق التحسينات المرئية على البيانات...") self.apply_visual_enhancements_to_data() # تطبيق التحسينات البسيطة print(" تطبيق التحسينات البسيطة...") # self.apply_simple_enhancements() # محذوف - استخدام apply_visual_enhancements_after_populate بدلاً منه self.apply_visual_enhancements_after_populate() # تطبيق التحسينات الجديدة فوراً على البيانات المضافة (بدون مؤقت) print(" تطبيق التحسينات الجديدة على البيانات...") # تم إزالة المؤقت لتجنب التحديث التلقائي غير المرغوب فيه print(" تم ملء الجدول بالبيانات المحسنة والمطورة") def populate_table_simple(self, clients): try: self.clients_table.setRowCount(0) for row, client in enumerate(clients): self.clients_table.insertRow(row) # خط موحد وبولد للجميع unified_bold_font = QFont("Segoe UI", 11, QFont.Bold) # خط موحد وبولد للجميع # البيانات الأساسية مع أيقونات حسب حالة العميل # اختيار الأيقونة حسب الرصيد والحالة if client.balance > 10000: icon = "" # VIP - مبلغ كبير جداً elif client.balance > 1000: icon = "" # ممتاز - مبلغ كبير elif client.balance > 0: icon = "🟢" # نشط - مبلغ موجب elif client.balance == 0: icon = "" # عادي - بدون رصيد elif client.balance > -1000: icon = "🟡" # مدين بسيط elif client.balance > -5000: icon = "" # تحذير - مدين متوسط else: icon = "" # خطر - مدين كبير items = [ f"{client.id} {icon}", # رقم مع أيقونة حسب الحالة client.name or "غير متوفر", client.phone or "غير متوفر", client.email or "غير متوفر", client.address or "غير متوفر", f"{client.balance:,.0f}", # إزالة ر.س client.notes or "غير متوفر", "🟢 نشط" if client.balance > 0 else (" عادى" if client.balance == 0 else " مدين"), client.created_at.strftime('%Y-%m-%d') if hasattr(client, 'created_at') and client.created_at else "غير متوفر" ] for col, text in enumerate(items): item = QTableWidgetItem(text) item.setTextAlignment(Qt.AlignCenter) # تطبيق الخط الموحد والبولد على جميع الخلايا item.setFont(unified_bold_font) # تلوين "غير متوفر" باللون الأحمر if text == "غير متوفر": item.setForeground(QColor("#DC2626")) # أحمر item.setBackground(QColor("#FEE2E2")) # خلفية حمراء فاتحة # تلوين الرصيد حسب القيمة if col == 5: # عمود الرصيد if client.balance > 10000: # رصيد عالي جداً - أخضر ذهبي مميز item.setForeground(QColor("#1a5f3f")) # أخضر داكن item.setBackground(QColor("#d4edda")) # أخضر فاتح elif client.balance > 0: # رصيد موجب - أخضر واضح item.setForeground(QColor("#155724")) # أخضر داكن item.setBackground(QColor("#d1ecf1")) # أخضر فاتح جداً elif client.balance < -5000: # رصيد سالب عالي - أحمر قوي item.setForeground(QColor("#721c24")) # أحمر داكن جداً item.setBackground(QColor("#f8d7da")) # أحمر فاتح elif client.balance < 0: # رصيد سالب - أحمر واضح item.setForeground(QColor("#856404")) # برتقالي داكن item.setBackground(QColor("#fff3cd")) # أصفر فاتح else: # رصيد صفر - رمادي item.setForeground(QColor("#495057")) # رمادي داكن item.setBackground(QColor("#e9ecef")) # رمادي فاتح self.clients_table.setItem(row, col, item) # إزالة تلوين الصفوف للحفاظ على البساطة # self.apply_row_colors() # تطبيق التحسينات الجديدة فوراً (بدون مؤقت) # تم إزالة المؤقت لتجنب التحديث التلقائي غير المرغوب فيه except Exception as e: print(f" خطأ في ملء الجدول البسيط: {str(e)}") def filter_clients(self): """تصفية العملاء بناءً على نص البحث مع الحفاظ على التنسيق""" search_text = self.search_edit.text().strip().lower() if not search_text: print(" مسح البحث - العودة للبيانات الكاملة...") # الحصول على جميع العملاء من قاعدة البيانات all_clients = self.session.query(Client).all() if not all_clients: self.create_simple_test_data() return self.smart_update_table_data_with_colors(all_clients) return print(f" البحث عن: {search_text}") # البحث في قاعدة البيانات مع تحسينات clients = self.session.query(Client).filter( Client.name.like(f"%{search_text}%") | Client.phone.like(f"%{search_text}%") | Client.email.like(f"%{search_text}%") | Client.address.like(f"%{search_text}%") | Client.notes.like(f"%{search_text}%") ).all() # البحث أيضاً في أرقام الهواتف المتعددة try: phone_clients = self.session.query(Client).join(ClientPhone).filter( ClientPhone.phone_number.like(f"%{search_text}%") | ClientPhone.label.like(f"%{search_text}%") ).all() # دمج النتائج وإزالة التكرار all_clients = list(set(clients + phone_clients)) except: # في حالة عدم وجود جدول ClientPhone all_clients = clients print(f" تم العثور على {len(all_clients)} عميل") # استخدام التحديث الذكي مع الحفاظ على التنسيق والألوان self.smart_update_table_data_with_colors(all_clients) # فرض تطبيق جميع التحسينات بعد البحث self.apply_immediate_updates() self.apply_balance_colors() self.apply_status_colors() def filter_by_balance(self, balance_type='all'): print(f" تصفية العملاء حسب الرصيد: {balance_type}") if balance_type == 'positive': # العملاء النشطين (لهم مبلغ) clients = self.session.query(Client).filter(Client.balance > 0).all() elif balance_type == 'negative': # العملاء المدينين (عليهم مبلغ) clients = self.session.query(Client).filter(Client.balance < 0).all() elif balance_type == 'zero': # العملاء العاديين (بدون رصيد) clients = self.session.query(Client).filter(Client.balance == 0).all() elif balance_type == 'active': # العملاء النشطين فقط (رصيد موجب) clients = self.session.query(Client).filter(Client.balance > 0).all() elif balance_type == 'normal': # العملاء العاديين فقط (رصيد صفر) clients = self.session.query(Client).filter(Client.balance == 0).all() elif balance_type == 'debtor': # العملاء المدينين فقط (رصيد سالب) clients = self.session.query(Client).filter(Client.balance < 0).all() else: # جميع العملاء clients = self.session.query(Client).all() print(f" تم العثور على {len(clients)} عميل") # استخدام التحديث الذكي مع الحفاظ على التنسيق والألوان self.smart_update_table_data_with_colors(clients) # فرض تطبيق جميع التحسينات بعد التصفية self.apply_immediate_updates() self.apply_balance_colors() self.apply_status_colors() def add_client(self): """إضافة عميل جديد""" dialog = ClientDialog(self.session) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # استخراج أرقام الهواتف من البيانات phones = data.pop('phones', []) # إنشاء عميل جديد في قاعدة البيانات client = Client(**data) self.session.add(client) self.session.flush() # للحصول على معرف العميل # إضافة أرقام الهواتف for phone in phones: phone.client_id = client.id self.session.add(phone) self.session.commit() show_info_message("تم", "تمت إضافة العميل بنجاح") self.refresh_data() def edit_client(self): try: # الحصول على رقم العميل المحدد client_id = self.get_selected_client_id() if client_id is None: show_error_message("خطأ", "الرجاء اختيار عميل من القائمة") return print(f" تعديل العميل رقم: {client_id}") client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return dialog = ClientDialog(self.session, client) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # استخراج أرقام الهواتف من البيانات phones = data.pop('phones', []) # تحديث بيانات العميل for key, value in data.items(): setattr(client, key, value) # حذف جميع أرقام الهواتف الحالية self.session.query(ClientPhone).filter_by(client_id=client.id).delete() # إضافة أرقام الهواتف الجديدة for phone in phones: phone.client_id = client.id self.session.add(phone) self.session.commit() show_info_message("تم", "تم تحديث بيانات العميل بنجاح") self.refresh_data() except Exception as e: print(f" خطأ في تعديل العميل: {str(e)}") show_error_message("خطأ", f"حدث خطأ أثناء تعديل العميل: {str(e)}") self.session.rollback() def delete_client(self): """حذف عميل""" try: # الحصول على رقم العميل المحدد client_id = self.get_selected_client_id() if client_id is None: show_error_message("خطأ", "الرجاء اختيار عميل من القائمة") return print(f" حذف العميل رقم: {client_id}") client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return # التأكد من أن العميل ليس لديه فواتير مرتبطة if client.invoices: show_error_message( "خطأ", f"لا يمكن حذف العميل لأنه مرتبط بـ {len(client.invoices)} فاتورة. قم بحذف الفواتير أولاً." ) return # طلب تأكيد الحذف if show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف العميل {client.name}؟"): # حذف أرقام الهواتف المرتبطة بالعميل self.session.query(ClientPhone).filter_by(client_id=client.id).delete() # حذف العميل self.session.delete(client) self.session.commit() show_info_message("تم", "تم حذف العميل بنجاح") self.refresh_data() except Exception as e: print(f" خطأ في حذف العميل: {str(e)}") self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء حذف العميل: {str(e)}") def show_client_details(self): selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من القائمة") return # استخراج رقم العميل من النص (إزالة الرموز التعبيرية) client_id_text = self.clients_table.item(selected_row, 0).text() client_id_match = re.search(r'\d+', client_id_text) if not client_id_match: show_error_message("خطأ", "لا يمكن العثور على رقم العميل") return client_id = int(client_id_match.group()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return try: dialog = ClientDetailsDialog(self, client) dialog.exec_() except Exception as e: QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض تفاصيل العميل: {str(e)}") print(f"خطأ في عرض تفاصيل العميل: {str(e)}") print(traceback.format_exc()) def adjust_balance(self): """تعديل المبلغ المستحق للعميل""" selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من القائمة") return # استخراج رقم العميل من النص (إزالة الرموز التعبيرية) client_id_text = self.clients_table.item(selected_row, 0).text() client_id_match = re.search(r'\d+', client_id_text) if not client_id_match: show_error_message("خطأ", "لا يمكن العثور على رقم العميل") return client_id = int(client_id_match.group()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return dialog = BalanceAdjustmentDialog(self, client) if dialog.exec_() == QDialog.Accepted: data = dialog.get_data() if data: # تحديث المبلغ المستحق success, new_balance = update_client_balance( self.session, client_id, data['amount'], data['operation'] ) if success: show_info_message( "تم", f"تم تحديث المبلغ المستحق للعميل {client.name} بنجاح.\nالمبلغ الجديد: {new_balance:.2f}" ) self.refresh_data() else: show_error_message("خطأ", "حدث خطأ أثناء تحديث المبلغ المستحق") def show_financial_details(self): selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من القائمة") return # استخراج رقم العميل من النص (إزالة الرموز التعبيرية) client_id_text = self.clients_table.item(selected_row, 0).text() client_id_match = re.search(r'\d+', client_id_text) if not client_id_match: show_error_message("خطأ", "لا يمكن العثور على رقم العميل") return client_id = int(client_id_match.group()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return try: dialog = ClientFinancialDetailsDialog(self, client, self.session) dialog.exec_() except Exception as e: QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض التفاصيل المالية: {str(e)}") print(f"خطأ في عرض التفاصيل المالية: {str(e)}") print(traceback.format_exc()) def show_enhanced_tooltip(self, row, column): """عرض تلميح متطور للخلية""" try: item = self.clients_table.item(row, column) if not item or not item.text(): return # تلميحات مخصصة حسب العمود column_tooltips = { 0: f" رقم العميل: {item.text()}", 1: f" اسم العميل: {item.text()}", 2: f" رقم الهاتف: {item.text()}\n انقر نقرتين للاتصال", 3: f" البريد الإلكتروني: {item.text()}\n انقر نقرتين لإرسال بريد", 4: f" العنوان: {item.text()}", 5: f" الرصيد الحالي: {item.text()}\n انقر نقرتين لتعديل الرصيد", 6: f" الملاحظات: {item.text()}", 7: f" حالة العميل: {item.text()}", 8: f" تاريخ الإنشاء: {item.text()}", 9: f" تقييم العميل: {item.text()}\n يعتمد على الرصيد والنشاط" } tooltip = column_tooltips.get(column, f"القيمة: {item.text()}") item.setToolTip(tooltip) except Exception as e: print(f"خطأ في عرض التلميح: {str(e)}") def show_cell_tooltip(self, row, column): self.show_enhanced_tooltip(row, column) def get_client_statistics(self): """الحصول على إحصائيات العملاء مع دعم الحالة الجديدة""" try: # إجمالي عدد العملاء total_clients = self.session.query(Client).count() # العملاء النشطين (رصيد موجب) active_clients = self.session.query(Client).filter(Client.balance > 0).count() # العملاء العاديين (رصيد صفر) normal_clients = self.session.query(Client).filter(Client.balance == 0).count() # العملاء المدينين (رصيد سالب) debtor_clients = self.session.query(Client).filter(Client.balance < 0).count() # إجمالي المبالغ المستحقة للعملاء النشطين total_positive_balance = self.session.query(Client).filter(Client.balance > 0).with_entities( self.session.query(Client.balance).filter(Client.balance > 0).subquery().c.balance ).all() total_positive = sum([balance[0] for balance in total_positive_balance]) if total_positive_balance else 0 # إجمالي المبالغ المستحقة على العملاء المدينين total_negative_balance = self.session.query(Client).filter(Client.balance < 0).with_entities( self.session.query(Client.balance).filter(Client.balance < 0).subquery().c.balance ).all() total_negative = sum([abs(balance[0]) for balance in total_negative_balance]) if total_negative_balance else 0 return { 'total_clients': total_clients, 'active_clients': active_clients, 'normal_clients': normal_clients, 'debtor_clients': debtor_clients, 'clients_with_positive_balance': active_clients, # للتوافق مع الكود القديم 'clients_with_negative_balance': debtor_clients, # للتوافق مع الكود القديم 'total_positive_balance': total_positive, 'total_negative_balance': total_negative } except Exception as e: print(f"خطأ في حساب إحصائيات العملاء: {str(e)}") return None # ==================== المميزات المتقدمة ==================== def show_advanced_context_menu(self, position): menu = QMenu(self) menu.setStyleSheet(""" QMenu { background-color: white; border: 2px solid #e2e8f0; border-radius: 8px; padding: 5px; } QMenu::item { padding: 8px 20px; border-radius: 4px; } QMenu::item:selected { background-color: #e6f3ff; color: #1a202c; } # الإجراءات الأساسية edit_action = menu.addAction(" تعديل العميل") edit_action.triggered.connect(self.edit_client) delete_action = menu.addAction(" حذف العميل") delete_action.triggered.connect(self.delete_client) menu.addSeparator() # الإجراءات المالية balance_action = menu.addAction(" تعديل الرصيد") balance_action.triggered.connect(self.adjust_balance) financial_action = menu.addAction(" التفاصيل المالية") financial_action.triggered.connect(self.show_financial_details) menu.addSeparator() # الإجراءات المتقدمة duplicate_action = menu.addAction(" نسخ العميل") duplicate_action.triggered.connect(self.duplicate_client) export_action = menu.addAction(" تصدير بيانات العميل") export_action.triggered.connect(self.export_selected_client) menu.addSeparator() # إجراءات التواصل call_action = menu.addAction(" اتصال") call_action.triggered.connect(self.call_client) email_action = menu.addAction(" إرسال بريد") email_action.triggered.connect(self.email_client) whatsapp_action = menu.addAction(" واتساب") whatsapp_action.triggered.connect(self.whatsapp_client) menu.exec_(self.clients_table.mapToGlobal(position)) def advanced_key_press_event(self, event): """معالج الأحداث المتقدم للوحة المفاتيح""" # البحث السريع بالكتابة if event.text().isalnum() or event.text() in [' ', '-', '_']: self.quick_search(event.text()) # اختصارات لوحة المفاتيح elif event.key() == Qt.Key_Delete: self.delete_client() elif event.key() == Qt.Key_F2: self.edit_client() elif event.key() == Qt.Key_F5: self.refresh_data() elif event.key() == Qt.Key_Escape: self.clients_table.clearSelection() # استدعاء المعالج الأصلي QTableWidget.keyPressEvent(self.clients_table, event) def show_advanced_tooltip(self, row, column): try: item = self.clients_table.item(row, column) if not item: return # الحصول على بيانات العميل client_id_text = self.clients_table.item(row, 0).text() # استخراج الرقم من النص (إزالة الرموز التعبيرية) client_id_match = re.search(r'\d+', client_id_text) if not client_id_match: return client_id = int(client_id_match.group()) client = self.session.query(Client).get(client_id) if not client: return # تلميحات مخصصة ومتقدمة حسب العمود tooltips = { 0: f" رقم العميل: {client.id}\n تاريخ الإنشاء: {client.created_at.strftime('%Y-%m-%d') if client.created_at else 'غير محدد'}\n آخر تحديث: {client.updated_at.strftime('%Y-%m-%d') if hasattr(client, 'updated_at') and client.updated_at else 'غير محدد'}", 1: f" اسم العميل: {client.name}\n عدد الفواتير: {len(client.invoices) if client.invoices else 0}\n إجمالي المشتريات: {sum([getattr(inv, 'total', 0) for inv in client.invoices]) if client.invoices else 0:.2f} ر.س", 2: f" رقم الهاتف: {client.phone or 'غير محدد'}\n انقر نقرتين للاتصال\n البحث عن العميل بهذا الرقم", 3: f" البريد الإلكتروني: {client.email or 'غير محدد'}\n انقر نقرتين لإرسال بريد\n حالة التحقق: {'محقق' if client.email else 'غير محدد'}", 4: f" العنوان: {client.address or 'غير محدد'}\n انقر نقرتين لفتح الخريطة\n عدد التوصيلات: {len([inv for inv in client.invoices if hasattr(inv, 'delivery_address')]) if client.invoices else 0}", 5: f" الرصيد الحالي: {client.balance:.2f} ر.س\n{' مبلغ مستحق على العميل' if client.balance < 0 else ' مبلغ مستحق للعميل' if client.balance > 0 else ' لا يوجد رصيد'}\n آخر معاملة: {self.get_last_transaction_date(client)}", 6: f" الملاحظات: {client.notes or 'لا توجد ملاحظات'}\n آخر تحديث للملاحظات: {self.get_notes_update_date(client)}\n انقر نقرتين للتعديل", 7: f" حالة العميل: {'نشط' if client.balance >= 0 else 'مدين'}\n تقييم العميل: {self.get_client_rating(client)}\n مستوى الأولوية: {self.get_client_priority(client)}" } tooltip = tooltips.get(column, f"القيمة: {item.text()}") item.setToolTip(tooltip) except Exception as e: print(f"خطأ في عرض التلميح المتقدم: {str(e)}") def setup_live_statistics(self): """إعداد الإحصائيات المباشرة""" self.live_stats = { 'total_clients': 0, 'active_clients': 0, 'debtor_clients': 0, 'total_balance': 0.0, 'average_balance': 0.0 } # ==================== المميزات المتقدمة الجديدة ==================== def setup_smart_analytics(self): self.analytics_engine = { 'trends': [], 'patterns': {}, 'predictions': {}, 'insights': [] } # تحليل الاتجاهات self.analyze_client_trends() # تحليل الأنماط self.analyze_client_patterns() # توليد الرؤى self.generate_insights() def setup_smart_notifications(self): """إعداد التنبيهات الذكية""" self.notification_system = { 'overdue_clients': [], 'high_value_clients': [], 'inactive_clients': [], 'payment_reminders': [] } # تفعيل مراقبة التنبيهات self.monitor_notifications() def setup_advanced_filtering(self): self.advanced_filters = { 'date_range': None, 'balance_range': None, 'location_filter': None, 'activity_filter': None, 'custom_filters': [] } # إنشاء واجهة التصفية المتقدمة self.create_advanced_filter_ui() def setup_smart_grouping(self): """إعداد التجميع والتصنيف الذكي""" self.grouping_options = { 'by_balance': True, 'by_location': True, 'by_activity': True, 'by_registration_date': True, 'custom_groups': [] } # تطبيق التجميع الذكي self.apply_smart_grouping() def setup_data_visualization(self): self.visualization_tools = { 'charts': [], 'graphs': [], 'heatmaps': [], 'dashboards': [] } # إنشاء المخططات التفاعلية self.create_interactive_charts() def setup_predictive_analytics(self): """إعداد التنبؤ الذكي""" self.prediction_models = { 'payment_prediction': None, 'churn_prediction': None, 'value_prediction': None, 'behavior_prediction': None } # تدريب نماذج التنبؤ self.train_prediction_models() def setup_external_integrations(self): self.integrations = { 'google_maps': False, 'social_media': False, 'payment_gateways': False, 'email_services': False, 'sms_services': False } # تفعيل التكاملات المتاحة self.enable_integrations() def update_live_statistics(self): """تحديث الإحصائيات المباشرة""" try: total_rows = self.clients_table.rowCount() active_count = 0 debtor_count = 0 total_balance = 0.0 for row in range(total_rows): # قراءة الرصيد من الجدول balance_item = self.clients_table.item(row, 5) if balance_item: balance_text = balance_item.text().replace(' ر.س', '').replace(',', '') try: balance = float(balance_text) total_balance += balance if balance >= 0: active_count += 1 else: debtor_count += 1 except ValueError: pass # تحديث الإحصائيات self.live_stats.update({ 'total_clients': total_rows, 'active_clients': active_count, 'debtor_clients': debtor_count, 'total_balance': total_balance, 'average_balance': total_balance / total_rows if total_rows > 0 else 0.0 }) # عرض الإحصائيات في شريط الحالة self.update_status_bar_stats() except Exception as e: print(f"خطأ في تحديث الإحصائيات المباشرة: {str(e)}") def update_status_bar_stats(self): try: stats_text = f" العملاء: {self.live_stats['total_clients']} | " stats_text += f"🟢 نشط: {self.live_stats['active_clients']} | " stats_text += f" مدين: {self.live_stats['debtor_clients']} | " stats_text += f" إجمالي الرصيد: {self.live_stats['total_balance']:.2f} ر.س" # تحديث شريط الحالة إذا كان متاحاً if hasattr(self.parent(), 'statusBar'): self.parent().statusBar().showMessage(stats_text) except Exception as e: print(f"خطأ في تحديث شريط الحالة: {str(e)}") def quick_search(self, text): """البحث السريع بالكتابة""" try: if not hasattr(self, 'quick_search_text'): self.quick_search_text = "" self.quick_search_text += text # البحث في الجدول for row in range(self.clients_table.rowCount()): match_found = False for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) if item and self.quick_search_text.lower() in item.text().lower(): match_found = True break # إخفاء/إظهار الصف حسب النتيجة self.clients_table.setRowHidden(row, not match_found) # مسح النص بعد ثانيتين QTimer.singleShot(2000, self.clear_quick_search) except Exception as e: print(f"خطأ في البحث السريع: {str(e)}") def clear_quick_search(self): try: self.quick_search_text = "" # إظهار جميع الصفوف for row in range(self.clients_table.rowCount()): self.clients_table.setRowHidden(row, False) except Exception as e: print(f"خطأ في مسح البحث السريع: {str(e)}") def duplicate_client(self): """نسخ العميل المحدد""" try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من القائمة") return client_id = int(self.clients_table.item(selected_row, 0).text()) original_client = self.session.query(Client).get(client_id) if not original_client: show_error_message("خطأ", "لم يتم العثور على العميل") return # إنشاء نسخة جديدة new_client = Client( name=f"{original_client.name} (نسخة)", phone=original_client.phone, email=original_client.email, address=original_client.address, notes=original_client.notes, balance=0.0 # بدء برصيد صفر ) self.session.add(new_client) self.session.commit() show_info_message("تم", "تم نسخ العميل بنجاح") self.refresh_data() except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء نسخ العميل: {str(e)}") def export_selected_client(self): try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من القائمة") return # قائمة خيارات التصدير formats = list(self.export_formats.values()) format_choice, ok = QInputDialog.getItem( self, "تصدير العميل", "اختر تنسيق التصدير:", formats, 0, False ) if ok and format_choice: # تنفيذ التصدير حسب التنسيق المختار format_key = list(self.export_formats.keys())[formats.index(format_choice)] self.perform_client_export(selected_row, format_key) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}") def perform_client_export(self, row, format_type): """تنفيذ عملية التصدير""" try: # الحصول على بيانات العميل client_data = {} headers = ["الرقم", "الاسم", "الهاتف", "الإيميل", "العنوان", "الرصيد", "الملاحظات", "الحالة"] for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) client_data[headers[col]] = item.text() if item else "" # تصدير حسب النوع if format_type == 'json': self.export_to_json(client_data) elif format_type == 'csv': self.export_to_csv([client_data]) elif format_type == 'excel': self.export_to_excel([client_data]) # يمكن إضافة المزيد من التنسيقات except Exception as e: print(f"خطأ في تنفيذ التصدير: {str(e)}") def call_client(self): try: selected_row = self.clients_table.currentRow() if selected_row < 0: return phone_item = self.clients_table.item(selected_row, 2) if phone_item and phone_item.text() != "غير محدد": phone = phone_item.text() # فتح تطبيق الهاتف (Windows) subprocess.run(f'start tel:{phone}', shell=True) except Exception as e: print(f"خطأ في الاتصال: {str(e)}") def email_client(self): """إرسال بريد للعميل""" try: selected_row = self.clients_table.currentRow() if selected_row < 0: return email_item = self.clients_table.item(selected_row, 3) if email_item and email_item.text() != "غير محدد": email = email_item.text() # فتح تطبيق البريد subprocess.run(f'start mailto:{email}', shell=True) except Exception as e: print(f"خطأ في إرسال البريد: {str(e)}") def whatsapp_client(self): try: selected_row = self.clients_table.currentRow() if selected_row < 0: return phone_item = self.clients_table.item(selected_row, 2) if phone_item and phone_item.text() != "غير محدد": phone = phone_item.text().replace('+', '').replace(' ', '').replace('-', '') # فتح واتساب ويب webbrowser.open(f'https://wa.me/{phone}') except Exception as e: print(f"خطأ في فتح واتساب: {str(e)}") def get_last_transaction_date(self, client): """الحصول على تاريخ آخر معاملة""" try: if client.invoices: last_invoice = max(client.invoices, key=lambda x: x.created_at if x.created_at else datetime.min) return last_invoice.created_at.strftime('%Y-%m-%d') if last_invoice.created_at else 'غير محدد' return 'لا توجد معاملات' except: return 'غير محدد' def get_notes_update_date(self, client): try: if hasattr(client, 'updated_at') and client.updated_at: return client.updated_at.strftime('%Y-%m-%d') return 'غير محدد' except: return 'غير محدد' def get_client_rating(self, client): """الحصول على تقييم العميل""" try: if client.balance > 1000: return " ممتاز" elif client.balance > 500: return " جيد جداً" elif client.balance > 0: return " جيد" elif client.balance == 0: return " متوسط" else: return " ضعيف" except: return "غير محدد" def get_client_priority(self, client): try: invoice_count = len(client.invoices) if client.invoices else 0 total_purchases = sum([getattr(inv, 'total', 0) for inv in client.invoices]) if client.invoices else 0 if total_purchases > 10000 or invoice_count > 20: return " عالية جداً" elif total_purchases > 5000 or invoice_count > 10: return " عالية" elif total_purchases > 1000 or invoice_count > 5: return "🟡 متوسطة" else: return "🟢 عادية" except: return "غير محدد" def show_statistics(self): """عرض إحصائيات العملاء""" stats = self.get_client_statistics() if stats: إجمالي عدد العملاء: {stats['total_clients']} العملاء الذين لهم مبلغ: {stats['clients_with_positive_balance']} إجمالي المبالغ المستحقة لهم: {stats['total_positive_balance']:.2f} ر.س العملاء الذين عليهم مبلغ: {stats['clients_with_negative_balance']} إجمالي المبالغ المستحقة عليهم: {stats['total_negative_balance']:.2f} ر.س صافي الرصيد: {stats['total_positive_balance'] - stats['total_negative_balance']:.2f} ر.س""" show_info_message("إحصائيات العملاء", message) else: show_error_message("خطأ", "حدث خطأ أثناء حساب الإحصائيات") def show_detailed_statistics(self): stats = self.get_client_statistics() if stats: # حساب إحصائيات إضافية try: # العملاء الجدد هذا الشهر current_month = datetime.now().replace(day=1) new_clients_this_month = self.session.query(Client).filter( Client.created_at >= current_month ).count() # متوسط الرصيد all_balances = [client.balance for client in self.session.query(Client).all()] avg_balance = sum(all_balances) / len(all_balances) if all_balances else 0 message = f""" إحصائيات العملاء المفصلة: العدد الإجمالي: {stats['total_clients']} عميل تحليل الأرصدة: • العملاء الدائنون: {stats['clients_with_positive_balance']} • إجمالي المبالغ لهم: {stats['total_positive_balance']:.2f} ر.س • العملاء المدينون: {stats['clients_with_negative_balance']} • إجمالي المبالغ عليهم: {stats['total_negative_balance']:.2f} ر.س • متوسط الرصيد: {avg_balance:.2f} ر.س النمو: • عملاء جدد هذا الشهر: {new_clients_this_month} • صافي الرصيد: {stats['total_positive_balance'] - stats['total_negative_balance']:.2f} ر.س النسب المئوية: • نسبة العملاء الدائنين: {(stats['clients_with_positive_balance']/stats['total_clients']*100):.1f}% show_info_message("إحصائيات مفصلة", message) except Exception as e: show_error_message("خطأ", f"حدث خطأ في الإحصائيات المفصلة: {str(e)}") else: show_error_message("خطأ", "حدث خطأ أثناء حساب الإحصائيات") def show_balance_analysis(self): """عرض تحليل الأرصدة""" try: clients = self.session.query(Client).all() if not clients: show_info_message("تحليل الأرصدة", "لا توجد بيانات عملاء") return # تصنيف العملاء حسب الرصيد high_positive = [c for c in clients if c.balance > 10000] medium_positive = [c for c in clients if 1000 < c.balance <= 10000] low_positive = [c for c in clients if 0 < c.balance <= 1000] zero_balance = [c for c in clients if c.balance == 0] low_negative = [c for c in clients if -1000 <= c.balance < 0] medium_negative = [c for c in clients if -10000 <= c.balance < -1000] high_negative = [c for c in clients if c.balance < -10000] 🟢 الأرصدة الموجبة: • عالية (أكثر من 10,000): {len(high_positive)} عميل • متوسطة (1,000 - 10,000): {len(medium_positive)} عميل • منخفضة (1 - 1,000): {len(low_positive)} عميل رصيد صفر: {len(zero_balance)} عميل الأرصدة السالبة: • منخفضة (-1 إلى -1,000): {len(low_negative)} عميل • متوسطة (-1,000 إلى -10,000): {len(medium_negative)} عميل • عالية (أقل من -10,000): {len(high_negative)} عميل التوزيع: • إجمالي الدائنين: {len(high_positive) + len(medium_positive) + len(low_positive)} • إجمالي المدينين: {len(low_negative) + len(medium_negative) + len(high_negative)}""" show_info_message("تحليل الأرصدة", message) except Exception as e: show_error_message("خطأ", f"حدث خطأ في تحليل الأرصدة: {str(e)}") def show_monthly_report(self): try: now = datetime.now() current_month = now.replace(day=1) last_month = (current_month - timedelta(days=1)).replace(day=1) # العملاء الجدد هذا الشهر new_this_month = self.session.query(Client).filter( Client.created_at >= current_month ).count() # العملاء الجدد الشهر الماضي new_last_month = self.session.query(Client).filter( Client.created_at >= last_month, Client.created_at < current_month ).count() # نمو العملاء growth = new_this_month - new_last_month growth_percent = (growth / new_last_month * 100) if new_last_month > 0 else 0 message = f""" التقرير الشهري - {now.strftime('%B %Y')}: نمو العملاء: • عملاء جدد هذا الشهر: {new_this_month} • عملاء جدد الشهر الماضي: {new_last_month} • النمو: {growth:+d} عميل • نسبة النمو: {growth_percent:+.1f}% الإحصائيات الحالية: • إجمالي العملاء: {self.session.query(Client).count()} • متوسط العملاء الجدد شهرياً: {(new_this_month + new_last_month) / 2:.1f} التوقعات: show_info_message("التقرير الشهري", message) except Exception as e: show_error_message("خطأ", f"حدث خطأ في التقرير الشهري: {str(e)}") def export_to_pdf(self): """تصدير بيانات العملاء إلى ملف PDF""" try: # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName( self, "حفظ تقرير العملاء كـ PDF", f"تقرير_العملاء_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf", "ملفات PDF (*.pdf)" ) if not file_path: return # إنشاء طابعة PDF printer = QPrinter(QPrinter.HighResolution) printer.setOutputFormat(QPrinter.PdfFormat) printer.setOutputFileName(file_path) printer.setPageSize(QPrinter.A4) printer.setPageMargins(20, 20, 20, 20, QPrinter.Millimeter) # إنشاء مستند نصي document = QTextDocument() # الحصول على الإحصائيات stats = self.get_client_statistics() if not stats: show_error_message("خطأ", "حدث خطأ أثناء حساب الإحصائيات") return # الحصول على جميع العملاء clients = self.session.query(Client).all() # إنشاء محتوى HTML للتقرير <!DOCTYPE html> <html dir="rtl" lang="ar"> <head> <meta charset="UTF-8"> <title>تقرير العملاء</title> <style> body {{ font-family: 'Arial', sans-serif; margin: 20px; direction: rtl; font-size: 12px; }} .header {{ text-align: center; background-color: #3498db; color: white; padding: 15px; margin-bottom: 20px; }} .stats-container {{ display: flex; justify-content: space-around; margin-bottom: 20px; }} .stat-card {{ background-color: #f8f9fa; padding: 10px; text-align: center; border: 1px solid #ddd; min-width: 120px; }} .stat-number {{ font-size: 1.5em; font-weight: bold; color: #3498db; }} .stat-label {{ color: #666; font-size: 0.9em; }} table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }} th, td {{ border: 1px solid #ddd; padding: 8px; text-align: center; font-size: 10px; }} th {{ background-color: #3498db; color: white; font-weight: bold; }} tr:nth-child(even) {{ background-color: #f9f9f9; }} .positive-balance {{ color: #27ae60; font-weight: bold; }} .negative-balance {{ color: #e74c3c; font-weight: bold; }} .footer {{ text-align: center; margin-top: 20px; color: #666; font-size: 10px; }} </style> </head> <body> <div class="header"> <h1>تقرير العملاء</h1> <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p> </div> <div class="stats-container"> <div class="stat-card"> <div class="stat-number">{stats['total_clients']}</div> <div class="stat-label">إجمالي العملاء</div> </div> <div class="stat-card"> <div class="stat-number">{stats['clients_with_positive_balance']}</div> <div class="stat-label">عملاء لهم مبلغ</div> </div> <div class="stat-card"> <div class="stat-number">{stats['clients_with_negative_balance']}</div> <div class="stat-label">عملاء عليهم مبلغ</div> </div> <div class="stat-card"> <div class="stat-number">{stats['total_positive_balance']:.2f}</div> <div class="stat-label">إجمالي المبالغ لهم (ر.س)</div> </div> <div class="stat-card"> <div class="stat-number">{abs(stats['total_negative_balance']):.2f}</div> <div class="stat-label">إجمالي المبالغ عليهم (ر.س)</div> </div> </div> <table> <thead> <tr> <th>الرقم</th> <th>الاسم</th> <th>رقم الهاتف</th> <th>البريد الإلكتروني</th> <th>العنوان</th> <th>الرصيد (ر.س)</th> <th>ملاحظات</th> </tr> </thead> <tbody> """ # إضافة بيانات العملاء for client in clients: balance_class = "" if client.balance > 0: balance_class = "positive-balance" elif client.balance < 0: balance_class = "negative-balance" <tr> <td>{client.id}</td> <td>{client.name}</td> <td>{client.phone or 'غير متوفر'}</td> <td>{client.email or 'غير متوفر'}</td> <td>{client.address or 'غير متوفر'}</td> <td class="{balance_class}">{client.balance:.2f}</td> <td>{client.notes or 'لا توجد ملاحظات'}</td> </tr> """ </tbody> </table> <div class="footer"> <p>تم إنشاء هذا التقرير بواسطة برنامج Smart Finish للمحاسبة الإدارية</p> </div> </body> </html> """ # تطبيق المحتوى على المستند document.setHtml(html_content) # طباعة المستند إلى PDF document.print_(printer) show_info_message("تم", f"تم تصدير تقرير العملاء بنجاح إلى:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير إلى PDF: {str(e)}") def export_to_csv(self): try: file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف CSV", "قائمة_العملاء.csv", "ملفات CSV (*.csv)") if not file_path: return with open(file_path, 'w', newline='', encoding='utf-8') as csvfile: writer = csv.writer(csvfile) # كتابة العناوين headers = [] for col in range(self.clients_table.columnCount()): headers.append(self.clients_table.horizontalHeaderItem(col).text()) writer.writerow(headers) # كتابة البيانات for row in range(self.clients_table.rowCount()): row_data = [] for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) row_data.append(item.text() if item else "") writer.writerow(row_data) show_info_message("تم", f"تم تصدير البيانات إلى CSV بنجاح:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}") def generate_clients_report(self): """إنشاء تقرير العملاء""" try: stats = self.get_client_statistics() if not stats: show_error_message("خطأ", "لا يمكن إنشاء التقرير") return تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} الإحصائيات العامة: • إجمالي عدد العملاء: {stats['total_clients']} • العملاء الدائنون: {stats['clients_with_positive_balance']} • العملاء المدينون: {stats['clients_with_negative_balance']} الأرصدة المالية: • إجمالي المبالغ للعملاء: {stats['total_positive_balance']:.2f} ر.س • إجمالي المبالغ على العملاء: {stats['total_negative_balance']:.2f} ر.س • صافي الرصيد: {stats['total_positive_balance'] - stats['total_negative_balance']:.2f} ر.س التحليل: • نسبة العملاء الدائنين: {(stats['clients_with_positive_balance']/stats['total_clients']*100):.1f}% • نسبة العملاء المدينين: {(stats['clients_with_negative_balance']/stats['total_clients']*100):.1f}% تم إنشاء هذا التقرير تلقائياً بواسطة نظام إدارة العملاء""" show_info_message("تقرير العملاء", report) except Exception as e: show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}") def restore_clients_data(self): try: file_path, _ = QFileDialog.getOpenFileName(self, "اختيار ملف النسخة الاحتياطية", "", "ملفات JSON (*.json)") if not file_path: return if not show_confirmation_message("تأكيد الاستعادة", "هل أنت متأكد من استعادة البيانات؟ سيتم استبدال البيانات الحالية."): return with open(file_path, 'r', encoding='utf-8') as f: backup_data = json.load(f) # حذف البيانات الحالية self.session.query(ClientPhone).delete() self.session.query(Client).delete() # استعادة البيانات for client_data in backup_data: phones_data = client_data.pop('phones', []) client = Client(**client_data) self.session.add(client) self.session.flush() # إضافة أرقام الهواتف for phone_data in phones_data: phone = ClientPhone(client_id=client.id, **phone_data) self.session.add(phone) self.session.commit() self.refresh_data() show_info_message("تم", "تم استعادة البيانات بنجاح") except Exception as e: self.session.rollback() show_error_message("خطأ", f"حدث خطأ في استعادة البيانات: {str(e)}") def show_contact_details(self): """عرض تفاصيل الاتصال للعميل""" selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من القائمة") return # استخراج رقم العميل من النص (إزالة الرموز التعبيرية) client_id_text = self.clients_table.item(selected_row, 0).text() client_id_match = re.search(r'\d+', client_id_text) if not client_id_match: show_error_message("خطأ", "لا يمكن العثور على رقم العميل") return client_id = int(client_id_match.group()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return try: # البحث عن أرقام هواتف العميل phone_records = self.session.query(ClientPhone).filter_by(client_id=client.id).all() # إنشاء نص تفاصيل الاتصال أرقام الهواتف:""" if phone_records: for i, phone in enumerate(phone_records, 1): phone_label = f" ({phone.label})" if phone.label else "" primary_mark = " [رئيسي]" if phone.is_primary else "" contact_info += f"\n {i}. {phone.phone_number}{phone_label}{primary_mark}" else: contact_info += f"\n • {client.phone or 'غير متوفر'}" البريد الإلكتروني: • {client.email or 'غير متوفر'} العنوان: • {client.address or 'غير متوفر'} ملاحظات الاتصال: • {client.notes or 'لا توجد ملاحظات'}""" show_info_message("تفاصيل الاتصال", contact_info) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء عرض تفاصيل الاتصال: {str(e)}") def show_transaction_history(self): selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من القائمة") return # استخراج رقم العميل من النص (إزالة الرموز التعبيرية) client_id_text = self.clients_table.item(selected_row, 0).text() client_id_match = re.search(r'\d+', client_id_text) if not client_id_match: show_error_message("خطأ", "لا يمكن العثور على رقم العميل") return client_id = int(client_id_match.group()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return try: # البحث عن فواتير العميل invoices = self.session.query(Invoice).filter_by(client_id=client.id).order_by(Invoice.date.desc()).all() # إنشاء نص سجل المعاملات history_info = f""" سجل المعاملات - {client.name}: الرصيد الحالي: {client.balance:.2f} ر.س إحصائيات المعاملات: if invoices: total_amount = sum(invoice.total_amount for invoice in invoices) paid_amount = sum(invoice.paid_amount for invoice in invoices) remaining_amount = total_amount - paid_amount history_info += f""" • إجمالي المبالغ: {total_amount:.2f} ر.س • المبلغ المدفوع: {paid_amount:.2f} ر.س • المبلغ المتبقي: {remaining_amount:.2f} ر.س for i, invoice in enumerate(invoices[:5], 1): status = "مدفوعة" if invoice.paid_amount >= invoice.total_amount else "غير مدفوعة" history_info += f""" {i}. فاتورة {invoice.invoice_number} • التاريخ: {invoice.date.strftime('%Y-%m-%d') if invoice.date else 'غير محدد'} • المبلغ: {invoice.total_amount:.2f} ر.س else: history_info += "\n • لا توجد معاملات مسجلة" show_info_message("سجل المعاملات", history_info) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء عرض سجل المعاملات: {str(e)}") def export_to_json(self): """تصدير إلى JSON""" try: file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف JSON", "قائمة_العملاء.json", "ملفات JSON (*.json)") if not file_path: return clients_data = [] for row in range(self.clients_table.rowCount()): client_data = {} for col in range(self.clients_table.columnCount()): header = self.clients_table.horizontalHeaderItem(col).text() item = self.clients_table.item(row, col) client_data[header] = item.text() if item else "" clients_data.append(client_data) with open(file_path, 'w', encoding='utf-8') as jsonfile: json.dump(clients_data, jsonfile, ensure_ascii=False, indent=2) show_info_message("تم", f"تم تصدير البيانات إلى JSON بنجاح:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}") def backup_clients_data(self): try: # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName( self, "حفظ النسخة الاحتياطية", f"نسخة_احتياطية_العملاء_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "ملفات JSON (*.json)" ) if not file_path: return # جمع بيانات العملاء clients = self.session.query(Client).all() backup_data = { 'backup_date': datetime.now().isoformat(), 'total_clients': len(clients), 'clients': [] } for client in clients: # جمع أرقام الهواتف phones = [] for phone in client.phones: phones.append({ 'phone_number': phone.phone_number, 'label': phone.label, 'is_primary': phone.is_primary }) client_data = { 'id': client.id, 'name': client.name, 'phone': client.phone, 'email': client.email, 'address': client.address, 'balance': client.balance, 'notes': client.notes, 'created_at': client.created_at.isoformat() if client.created_at else None, 'phones': phones } backup_data['clients'].append(client_data) # حفظ البيانات with open(file_path, 'w', encoding='utf-8') as f: json.dump(backup_data, f, ensure_ascii=False, indent=2) show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{file_path}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}") def restore_clients_data(self): """استعادة بيانات العملاء من نسخة احتياطية""" try: # عرض مربع حوار فتح الملف file_path, _ = QFileDialog.getOpenFileName( self, "اختيار النسخة الاحتياطية", "", "ملفات JSON (*.json)" ) if not file_path: return # تأكيد الاستعادة if not show_confirmation_message( "تأكيد الاستعادة", "هل أنت متأكد من استعادة البيانات؟\nسيتم استبدال البيانات الحالية." ): return # قراءة البيانات with open(file_path, 'r', encoding='utf-8') as f: backup_data = json.load(f) restored_count = 0 for client_data in backup_data.get('clients', []): # التحقق من وجود العميل existing_client = self.session.query(Client).filter_by(name=client_data['name']).first() if existing_client: # تحديث البيانات الموجودة existing_client.phone = client_data.get('phone') existing_client.email = client_data.get('email') existing_client.address = client_data.get('address') existing_client.balance = client_data.get('balance', 0.0) existing_client.notes = client_data.get('notes') else: # إنشاء عميل جديد new_client = Client( name=client_data['name'], phone=client_data.get('phone'), email=client_data.get('email'), address=client_data.get('address'), balance=client_data.get('balance', 0.0), notes=client_data.get('notes') ) self.session.add(new_client) self.session.flush() # للحصول على معرف العميل # إضافة أرقام الهواتف for phone_data in client_data.get('phones', []): phone = ClientPhone( client_id=new_client.id, phone_number=phone_data['phone_number'], label=phone_data.get('label'), is_primary=phone_data.get('is_primary', False) ) self.session.add(phone) restored_count += 1 self.session.commit() self.refresh_data() show_info_message("تم", f"تم استعادة {restored_count} عميل بنجاح") except Exception as e: self.session.rollback() show_error_message("خطأ", f"حدث خطأ أثناء استعادة البيانات: {str(e)}") def export_data(self): try: # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName(self, "حفظ كملف Excel", "قائمة_العملاء.xlsx", "ملفات Excel (*.xlsx)") if not file_path: return try: # استخدام مكتبة pandas لتصدير البيانات # جمع البيانات من الجدول data = [] headers = [] # الحصول على عناوين الأعمدة for col in range(self.clients_table.columnCount()): headers.append(self.clients_table.horizontalHeaderItem(col).text()) # جمع البيانات من الجدول for row in range(self.clients_table.rowCount()): row_data = [] for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) row_data.append(item.text() if item else "") data.append(row_data) # إنشاء DataFrame df = pd.DataFrame(data, columns=headers) # حفظ البيانات إلى ملف Excel df.to_excel(file_path, index=False) show_info_message("تم", f"تم تصدير البيانات بنجاح إلى:\n{file_path}") except ImportError: # في حالة عدم وجود مكتبة pandas show_error_message("خطأ", "يرجى تثبيت مكتبة pandas لتصدير البيانات:\npip install pandas openpyxl") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}") def generate_clients_report(self): """إنشاء تقرير مفصل للعملاء""" try: # عرض مربع حوار حفظ الملف file_path, _ = QFileDialog.getSaveFileName( self, "حفظ تقرير العملاء", f"تقرير_العملاء_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html", "ملفات HTML (*.html)" ) if not file_path: return # الحصول على الإحصائيات stats = self.get_client_statistics() if not stats: show_error_message("خطأ", "حدث خطأ أثناء حساب الإحصائيات") return # الحصول على جميع العملاء clients = self.session.query(Client).all() # إنشاء محتوى HTML <!DOCTYPE html> <html dir="rtl" lang="ar"> <head> <meta charset="UTF-8"> <meta name="viewport" content="width=device-width, initial-scale=1.0"> <title>تقرير العملاء</title> <style> body {{ font-family: 'Arial', sans-serif; margin: 20px; background-color: #f5f5f5; direction: rtl; }} .header {{ text-align: center; background-color: #3498db; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }} .stats-container {{ display: flex; justify-content: space-around; margin-bottom: 30px; }} .stat-card {{ background-color: white; padding: 20px; border-radius: 10px; text-align: center; min-width: 200px; }} .stat-number {{ font-size: 2em; font-weight: bold; color: #3498db; }} .stat-label {{ color: #666; margin-top: 10px; }} table {{ width: 100%; border-collapse: collapse; background-color: white; border-radius: 10px; overflow: hidden; }} th, td {{ padding: 12px; text-align: center; border-bottom: 1px solid #ddd; }} th {{ background-color: #3498db; color: white; font-weight: bold; }} tr:nth-child(even) {{ background-color: #f9f9f9; }} .positive-balance {{ color: #27ae60; font-weight: bold; }} .negative-balance {{ color: #e74c3c; font-weight: bold; }} .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }} </style> </head> <body> <div class="header"> <h1>تقرير العملاء</h1> <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p> </div> <div class="stats-container"> <div class="stat-card"> <div class="stat-number">{stats['total_clients']}</div> <div class="stat-label">إجمالي العملاء</div> </div> <div class="stat-card"> <div class="stat-number">{stats['clients_with_positive_balance']}</div> <div class="stat-label">عملاء لهم مبلغ</div> </div> <div class="stat-card"> <div class="stat-number">{stats['clients_with_negative_balance']}</div> <div class="stat-label">عملاء عليهم مبلغ</div> </div> <div class="stat-card"> <div class="stat-number">{stats['total_positive_balance']:.2f}</div> <div class="stat-label">إجمالي المبالغ لهم (ر.س)</div> </div> <div class="stat-card"> <div class="stat-number">{stats['total_negative_balance']:.2f}</div> <div class="stat-label">إجمالي المبالغ عليهم (ر.س)</div> </div> </div> <table> <thead> <tr> <th>الرقم</th> <th>الاسم</th> <th>رقم الهاتف</th> <th>البريد الإلكتروني</th> <th>العنوان</th> <th>الرصيد (ر.س)</th> <th>ملاحظات</th> </tr> </thead> <tbody> """ # إضافة بيانات العملاء for client in clients: balance_class = "" if client.balance > 0: balance_class = "positive-balance" elif client.balance < 0: balance_class = "negative-balance" <tr> <td>{client.id}</td> <td>{client.name}</td> <td>{client.phone or 'غير متوفر'}</td> <td>{client.email or 'غير متوفر'}</td> <td>{client.address or 'غير متوفر'}</td> <td class="{balance_class}">{client.balance:.2f}</td> <td>{client.notes or 'لا توجد ملاحظات'}</td> </tr> """ </tbody> </table> <div class="footer"> <p>تم إنشاء هذا التقرير بواسطة برنامج المحاسبة الإداري</p> </div> </body> </html> """ # حفظ التقرير with open(file_path, 'w', encoding='utf-8') as f: f.write(html_content) show_info_message("تم", f"تم إنشاء التقرير بنجاح:\n{file_path}") # سؤال المستخدم إذا كان يريد فتح التقرير if show_confirmation_message("فتح التقرير", "هل تريد فتح التقرير الآن؟"): webbrowser.open(file_path) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}") # ==================== المميزات المتقدمة الجديدة - التحليل الذكي ==================== def analyze_client_trends(self): try: clients = self.session.query(Client).all() print(" بدء تحليل اتجاهات العملاء...") # تحليل نمو العملاء growth_trend = self.calculate_growth_trend(clients) # تحليل اتجاه الأرصدة balance_trend = self.calculate_balance_trend(clients) # تحليل النشاط activity_trend = self.calculate_activity_trend(clients) self.analytics_engine['trends'] = { 'growth': growth_trend, 'balance': balance_trend, 'activity': activity_trend } print(" تم تحليل الاتجاهات بنجاح") except Exception as e: print(f" خطأ في تحليل الاتجاهات: {str(e)}") def calculate_growth_trend(self, clients): """حساب اتجاه النمو مع تحليل متقدم""" try: # تجميع العملاء حسب تاريخ الإنشاء monthly_counts = {} for client in clients: if hasattr(client, 'created_at') and client.created_at: month_key = client.created_at.strftime('%Y-%m') monthly_counts[month_key] = monthly_counts.get(month_key, 0) + 1 # حساب معدل النمو months = sorted(monthly_counts.keys()) if len(months) >= 2: recent_month = monthly_counts[months[-1]] previous_month = monthly_counts[months[-2]] growth_rate = ((recent_month - previous_month) / previous_month) * 100 if previous_month > 0 else 0 return { 'rate': growth_rate, 'trend': 'صاعد ' if growth_rate > 0 else 'هابط ' if growth_rate < 0 else 'ثابت ', 'monthly_data': monthly_counts, 'prediction': 'إيجابي' if growth_rate > 5 else 'محايد' if growth_rate > -5 else 'سلبي' } return {'rate': 0, 'trend': 'غير محدد ', 'monthly_data': monthly_counts, 'prediction': 'غير متاح'} except Exception as e: print(f" خطأ في حساب اتجاه النمو: {str(e)}") return {'rate': 0, 'trend': 'خطأ ', 'monthly_data': {}, 'prediction': 'خطأ'} def calculate_balance_trend(self, clients): try: positive_balances = [c.balance for c in clients if c.balance > 0] negative_balances = [c.balance for c in clients if c.balance < 0] zero_balances = [c.balance for c in clients if c.balance == 0] return { 'positive_count': len(positive_balances), 'negative_count': len(negative_balances), 'zero_count': len(zero_balances), 'avg_positive': sum(positive_balances) / len(positive_balances) if positive_balances else 0, 'avg_negative': sum(negative_balances) / len(negative_balances) if negative_balances else 0, 'total_positive': sum(positive_balances), 'total_negative': sum(negative_balances), 'health_score': self.calculate_financial_health_score(positive_balances, negative_balances), 'risk_level': self.calculate_risk_level(negative_balances) } except Exception as e: print(f" خطأ في حساب اتجاه الأرصدة: {str(e)}") return {} def calculate_financial_health_score(self, positive_balances, negative_balances): """حساب نقاط الصحة المالية""" try: total_positive = sum(positive_balances) if positive_balances else 0 total_negative = abs(sum(negative_balances)) if negative_balances else 0 if total_positive + total_negative == 0: return 50 # نقاط محايدة score = (total_positive / (total_positive + total_negative)) * 100 if score >= 80: return f"{score:.1f}% - ممتاز 🟢" elif score >= 60: return f"{score:.1f}% - جيد 🟡" elif score >= 40: return f"{score:.1f}% - متوسط 🟠" else: return f"{score:.1f}% - ضعيف " except Exception as e: return "خطأ في الحساب " def calculate_risk_level(self, negative_balances): try: if not negative_balances: return "منخفض 🟢" avg_negative = abs(sum(negative_balances) / len(negative_balances)) if avg_negative > 10000: return "عالي جداً " elif avg_negative > 5000: return "عالي 🟠" elif avg_negative > 1000: return "متوسط 🟡" else: return "منخفض 🟢" except Exception as e: return "خطأ " def calculate_activity_trend(self, clients): """حساب اتجاه النشاط مع تحليل متقدم""" try: # تحليل النشاط بناءً على عدد الفواتير activity_levels = { 'vip': 0, # أكثر من 50 فاتورة - عملاء VIP 'high': 0, # 20-50 فاتورة - نشاط عالي 'medium': 0, # 5-19 فاتورة - نشاط متوسط 'low': 0, # 1-4 فواتير - نشاط منخفض 'inactive': 0, # لا توجد فواتير - غير نشط 'at_risk': 0 # لديهم رصيد سلبي ولا فواتير حديثة } for client in clients: invoice_count = len(client.invoices) if hasattr(client, 'invoices') and client.invoices else 0 if invoice_count > 50: activity_levels['vip'] += 1 elif invoice_count >= 20: activity_levels['high'] += 1 elif invoice_count >= 5: activity_levels['medium'] += 1 elif invoice_count >= 1: activity_levels['low'] += 1 else: if client.balance < 0: activity_levels['at_risk'] += 1 else: activity_levels['inactive'] += 1 # إضافة تحليل متقدم total_clients = sum(activity_levels.values()) activity_levels['engagement_score'] = self.calculate_engagement_score(activity_levels, total_clients) activity_levels['recommendations'] = self.generate_activity_recommendations(activity_levels, total_clients) return activity_levels except Exception as e: print(f" خطأ في حساب اتجاه النشاط: {str(e)}") return {} def calculate_engagement_score(self, activity_levels, total_clients): try: if total_clients == 0: return "غير متاح" # حساب النقاط بناءً على توزيع النشاط vip_weight = activity_levels['vip'] * 5 high_weight = activity_levels['high'] * 4 medium_weight = activity_levels['medium'] * 3 low_weight = activity_levels['low'] * 2 inactive_weight = activity_levels['inactive'] * 1 at_risk_weight = activity_levels['at_risk'] * 0 total_score = vip_weight + high_weight + medium_weight + low_weight + inactive_weight + at_risk_weight max_possible_score = total_clients * 5 engagement_percentage = (total_score / max_possible_score) * 100 if max_possible_score > 0 else 0 if engagement_percentage >= 80: return f"{engagement_percentage:.1f}% - ممتاز " elif engagement_percentage >= 60: return f"{engagement_percentage:.1f}% - جيد " elif engagement_percentage >= 40: return f"{engagement_percentage:.1f}% - متوسط " else: return f"{engagement_percentage:.1f}% - يحتاج تحسين " except Exception as e: return "خطأ في الحساب " def generate_activity_recommendations(self, activity_levels, total_clients): """توليد توصيات النشاط""" try: recommendations = [] # توصيات بناءً على العملاء غير النشطين inactive_percentage = (activity_levels['inactive'] / total_clients) * 100 if total_clients > 0 else 0 if inactive_percentage > 30: recommendations.append(" تفعيل حملة لإعادة إشراك العملاء غير النشطين") # توصيات بناءً على العملاء المعرضين للخطر at_risk_percentage = (activity_levels['at_risk'] / total_clients) * 100 if total_clients > 0 else 0 if at_risk_percentage > 10: recommendations.append(" متابعة عاجلة للعملاء المعرضين للخطر") # توصيات بناءً على عملاء VIP vip_percentage = (activity_levels['vip'] / total_clients) * 100 if total_clients > 0 else 0 if vip_percentage > 5: recommendations.append(" إنشاء برنامج ولاء خاص لعملاء VIP") if not recommendations: recommendations.append(" الوضع مستقر - استمر في المراقبة") return recommendations except Exception as e: return [" خطأ في توليد التوصيات"] # ==================== المميزات المتقدمة - التنبيهات الذكية ==================== def monitor_notifications(self): try: print(" بدء مراقبة التنبيهات الذكية...") # مراقبة العملاء المتأخرين self.monitor_overdue_clients() # مراقبة العملاء عالي القيمة self.monitor_high_value_clients() # مراقبة العملاء غير النشطين self.monitor_inactive_clients() # مراقبة تذكيرات الدفع self.monitor_payment_reminders() print(" تم إعداد مراقبة التنبيهات بنجاح") except Exception as e: print(f" خطأ في مراقبة التنبيهات: {str(e)}") def monitor_payment_reminders(self): """مراقبة تذكيرات الدفع""" try: # العملاء الذين يحتاجون تذكير بالدفع clients_needing_reminder = self.session.query(Client).filter( Client.balance < 0, Client.balance > -5000 # ليس مبلغ كبير جداً ).all() payment_reminders = [] for client in clients_needing_reminder: reminder_urgency = self.calculate_reminder_urgency(client) payment_reminders.append({ 'client': client, 'amount_due': abs(client.balance), 'urgency': reminder_urgency, 'suggested_action': self.suggest_payment_action(client), 'contact_method': self.suggest_contact_method(client) }) self.notification_system['payment_reminders'] = payment_reminders if payment_reminders: print(f" تم إنشاء {len(payment_reminders)} تذكير دفع") except Exception as e: print(f" خطأ في مراقبة تذكيرات الدفع: {str(e)}") def suggest_contact_method(self, client): try: methods = [] if client.phone: methods.append(" هاتف") if client.email: methods.append(" بريد إلكتروني") if client.phone: methods.append(" واتساب") if not methods: methods.append(" بريد عادي") return " + ".join(methods) except Exception as e: return "تواصل مباشر" def calculate_reminder_urgency(self, client): """حساب مستوى إلحاح التذكير""" try: balance = abs(client.balance) if balance > 3000: return "عالي" elif balance > 1000: return "متوسط" else: return "منخفض" except Exception as e: return "عادي" def suggest_payment_action(self, client): try: balance = abs(client.balance) if balance > 3000: return "اتصال فوري" elif balance > 1000: return "إرسال تذكير" else: return "متابعة عادية" except Exception as e: return "متابعة" # ==================== دوال المميزات المتقدمة المفقودة ==================== def setup_smart_analytics(self): """إعداد التحليل الذكي للبيانات""" print("🧠 إعداد التحليل الذكي...") self.analytics_engine = { 'trends': [], 'patterns': {}, 'predictions': {}, 'insights': [] } # تحليل الاتجاهات self.analyze_client_trends() # تحليل الأنماط self.analyze_client_patterns() # توليد الرؤى self.generate_insights() def setup_smart_notifications(self): print(" إعداد التنبيهات الذكية...") self.notification_system = { 'overdue_clients': [], 'high_value_clients': [], 'inactive_clients': [], 'payment_reminders': [] } # تفعيل مراقبة التنبيهات self.monitor_notifications() def setup_advanced_filtering(self): """إعداد التصفية المتقدمة""" print(" إعداد التصفية المتقدمة...") self.advanced_filters = { 'date_range': None, 'balance_range': None, 'location_filter': None, 'activity_filter': None, 'custom_filters': [] } # إنشاء واجهة التصفية المتقدمة self.create_advanced_filter_ui() def setup_smart_grouping(self): print(" إعداد التجميع الذكي...") self.grouping_options = { 'by_balance': True, 'by_location': True, 'by_activity': True, 'by_registration_date': True, 'custom_groups': [] } # تطبيق التجميع الذكي self.apply_smart_grouping() def setup_data_visualization(self): """إعداد التصور المرئي للبيانات""" print(" إعداد التصور المرئي...") self.visualization_tools = { 'charts': [], 'graphs': [], 'heatmaps': [], 'dashboards': [] } # إنشاء المخططات التفاعلية self.create_interactive_charts() def setup_predictive_analytics(self): print(" إعداد التنبؤ الذكي...") self.prediction_models = { 'payment_prediction': None, 'churn_prediction': None, 'value_prediction': None, 'behavior_prediction': None } # تدريب نماذج التنبؤ self.train_prediction_models() def setup_external_integrations(self): """إعداد التكامل مع الخدمات الخارجية""" print(" إعداد التكاملات الخارجية...") self.integrations = { 'google_maps': False, 'social_media': False, 'payment_gateways': False, 'email_services': False, 'sms_services': False } # تفعيل التكاملات المتاحة self.enable_integrations() def setup_advanced_security(self): print(" إعداد الأمان المتقدم...") self.security_features = { 'data_encryption': True, 'access_control': True, 'audit_trail': True, 'backup_encryption': True } # تطبيق إعدادات الأمان self.apply_security_settings() def setup_advanced_customization(self): """إعداد التخصيص المتقدم""" print(" إعداد التخصيص المتقدم...") self.customization_options = { 'themes': ['light', 'dark', 'auto'], 'layouts': ['compact', 'comfortable', 'spacious'], 'colors': ['blue', 'green', 'purple', 'orange'], 'fonts': ['small', 'medium', 'large'] } # تطبيق التخصيصات self.apply_customizations() def setup_ai_features(self): print("🤖 إعداد الذكاء الاصطناعي...") self.ai_features = { 'auto_categorization': True, 'smart_recommendations': True, 'anomaly_detection': True, 'natural_language_search': True } # تفعيل ميزات الذكاء الاصطناعي self.enable_ai_features() # ==================== تنفيذ الدوال المساعدة ==================== def analyze_client_patterns(self): """تحليل أنماط العملاء""" try: clients = self.session.query(Client).all() # تحليل أنماط الدفع payment_patterns = self.analyze_payment_patterns(clients) # تحليل أنماط الموقع location_patterns = self.analyze_location_patterns(clients) # تحليل أنماط السلوك behavior_patterns = self.analyze_behavior_patterns(clients) self.analytics_engine['patterns'] = { 'payment': payment_patterns, 'location': location_patterns, 'behavior': behavior_patterns } except Exception as e: print(f" خطأ في تحليل الأنماط: {str(e)}") def analyze_payment_patterns(self, clients): try: patterns = { 'early_payers': 0, # يدفعون مبكراً 'on_time_payers': 0, # يدفعون في الوقت 'late_payers': 0, # يدفعون متأخراً 'non_payers': 0 # لا يدفعون } for client in clients: if client.balance > 0: patterns['early_payers'] += 1 elif client.balance == 0: patterns['on_time_payers'] += 1 elif client.balance < 0: if abs(client.balance) > 1000: patterns['non_payers'] += 1 else: patterns['late_payers'] += 1 return patterns except Exception as e: print(f" خطأ في تحليل أنماط الدفع: {str(e)}") return {} def analyze_location_patterns(self, clients): """تحليل أنماط الموقع""" try: locations = {} for client in clients: if client.address: # استخراج المدينة من العنوان (تبسيط) city = client.address.split(',')[-1].strip() if ',' in client.address else client.address locations[city] = locations.get(city, 0) + 1 # ترتيب المواقع حسب عدد العملاء sorted_locations = sorted(locations.items(), key=lambda x: x[1], reverse=True) return { 'top_locations': sorted_locations[:10], 'total_locations': len(locations), 'distribution': locations } except Exception as e: print(f" خطأ في تحليل أنماط الموقع: {str(e)}") return {} def analyze_behavior_patterns(self, clients): try: patterns = { 'frequent_buyers': 0, # مشترون متكررون 'occasional_buyers': 0, # مشترون أحياناً 'one_time_buyers': 0, # مشترون لمرة واحدة 'potential_churners': 0 # محتملو الانقطاع } for client in clients: invoice_count = len(client.invoices) if hasattr(client, 'invoices') and client.invoices else 0 if invoice_count > 20: patterns['frequent_buyers'] += 1 elif invoice_count > 5: patterns['occasional_buyers'] += 1 elif invoice_count == 1: patterns['one_time_buyers'] += 1 elif invoice_count == 0 and client.balance < 0: patterns['potential_churners'] += 1 return patterns except Exception as e: print(f" خطأ في تحليل أنماط السلوك: {str(e)}") return {} def generate_insights(self): """توليد الرؤى الذكية""" try: insights = [] # رؤى حول الأرصدة balance_insights = self.generate_balance_insights() insights.extend(balance_insights) # رؤى حول النشاط activity_insights = self.generate_activity_insights() insights.extend(activity_insights) # رؤى حول المخاطر risk_insights = self.generate_risk_insights() insights.extend(risk_insights) # رؤى حول الفرص opportunity_insights = self.generate_opportunity_insights() insights.extend(opportunity_insights) self.analytics_engine['insights'] = insights except Exception as e: print(f" خطأ في توليد الرؤى: {str(e)}") def generate_balance_insights(self): try: insights = [] clients = self.session.query(Client).all() # حساب الإحصائيات total_positive = sum(c.balance for c in clients if c.balance > 0) total_negative = sum(c.balance for c in clients if c.balance < 0) if total_positive > abs(total_negative): insights.append({ 'type': 'positive', 'title': ' وضع مالي إيجابي', 'description': f'إجمالي الأرصدة الإيجابية ({total_positive:.2f}) أكبر من السلبية ({abs(total_negative):.2f})', 'priority': 'low' }) else: insights.append({ 'type': 'warning', 'title': ' تحذير مالي', 'description': f'إجمالي الأرصدة السلبية ({abs(total_negative):.2f}) أكبر من الإيجابية ({total_positive:.2f})', 'priority': 'high' }) return insights except Exception as e: print(f" خطأ في توليد رؤى الأرصدة: {str(e)}") return [] def generate_activity_insights(self): """توليد رؤى حول النشاط""" try: insights = [] clients = self.session.query(Client).all() # حساب العملاء غير النشطين inactive_clients = [c for c in clients if not hasattr(c, 'invoices') or not c.invoices] if len(inactive_clients) > len(clients) * 0.3: # أكثر من 30% insights.append({ 'type': 'warning', 'title': ' نشاط منخفض', 'description': f'{len(inactive_clients)} عميل ({len(inactive_clients)/len(clients)*100:.1f}%) غير نشط', 'priority': 'medium' }) return insights except Exception as e: print(f" خطأ في توليد رؤى النشاط: {str(e)}") return [] def generate_risk_insights(self): try: insights = [] clients = self.session.query(Client).all() # العملاء عالي المخاطر (رصيد سلبي كبير) high_risk_clients = [c for c in clients if c.balance < -5000] if high_risk_clients: insights.append({ 'type': 'danger', 'title': ' عملاء عالي المخاطر', 'description': f'{len(high_risk_clients)} عميل برصيد سلبي أكبر من 5000', 'priority': 'critical' }) return insights except Exception as e: print(f" خطأ في توليد رؤى المخاطر: {str(e)}") return [] def generate_opportunity_insights(self): """توليد رؤى حول الفرص""" try: insights = [] clients = self.session.query(Client).all() # العملاء عالي القيمة (رصيد إيجابي كبير) high_value_clients = [c for c in clients if c.balance > 10000] if high_value_clients: insights.append({ 'type': 'success', 'title': ' فرص ذهبية', 'description': f'{len(high_value_clients)} عميل عالي القيمة برصيد أكبر من 10000', 'priority': 'high' }) return insights except Exception as e: print(f" خطأ في توليد رؤى الفرص: {str(e)}") return [] # ==================== دوال المميزات المتقدمة الإضافية ==================== def create_advanced_filter_ui(self): print(" إنشاء واجهة التصفية المتقدمة...") # سيتم تطويرها لاحقاً def apply_smart_grouping(self): """تطبيق التجميع الذكي""" print(" تطبيق التجميع الذكي...") # سيتم تطويرها لاحقاً def create_interactive_charts(self): print(" إنشاء المخططات التفاعلية...") # سيتم تطويرها لاحقاً def monitor_overdue_clients(self): """مراقبة العملاء المتأخرين""" try: overdue_clients = self.session.query(Client).filter(Client.balance < -1000).all() self.notification_system['overdue_clients'] = overdue_clients if overdue_clients: print(f" تم العثور على {len(overdue_clients)} عميل متأخر") except Exception as e: print(f" خطأ في مراقبة العملاء المتأخرين: {str(e)}") def monitor_high_value_clients(self): try: high_value_clients = self.session.query(Client).filter(Client.balance > 5000).all() self.notification_system['high_value_clients'] = high_value_clients if high_value_clients: print(f" تم العثور على {len(high_value_clients)} عميل عالي القيمة") except Exception as e: print(f" خطأ في مراقبة العملاء عالي القيمة: {str(e)}") def monitor_inactive_clients(self): """مراقبة العملاء غير النشطين""" try: inactive_clients = self.session.query(Client).filter(Client.balance == 0).all() self.notification_system['inactive_clients'] = inactive_clients if inactive_clients: print(f" تم العثور على {len(inactive_clients)} عميل غير نشط") except Exception as e: print(f" خطأ في مراقبة العملاء غير النشطين: {str(e)}") def train_prediction_models(self): try: print("🤖 تدريب نماذج التنبؤ...") # تنفيذ مبسط self.prediction_models['payment_prediction'] = "trained" self.prediction_models['churn_prediction'] = "trained" except Exception as e: print(f" خطأ في تدريب نماذج التنبؤ: {str(e)}") def enable_integrations(self): """تفعيل التكاملات""" try: print(" تفعيل التكاملات...") # تنفيذ مبسط self.integrations['email_services'] = True except Exception as e: print(f" خطأ في تفعيل التكاملات: {str(e)}") def apply_security_settings(self): try: print(" تطبيق إعدادات الأمان...") # تنفيذ مبسط pass except Exception as e: print(f" خطأ في تطبيق إعدادات الأمان: {str(e)}") def apply_customizations(self): """تطبيق التخصيصات""" try: print(" تطبيق التخصيصات...") # تنفيذ مبسط pass except Exception as e: print(f" خطأ في تطبيق التخصيصات: {str(e)}") def enable_ai_features(self): try: print("🤖 تفعيل ميزات الذكاء الاصطناعي...") # تنفيذ مبسط pass except Exception as e: print(f" خطأ في تفعيل ميزات الذكاء الاصطناعي: {str(e)}") def verify_advanced_features(self): """التحقق من أن المميزات المتقدمة تعمل وتظهر""" try: print(" التحقق من المميزات المتقدمة...") # التحقق من الجدول if hasattr(self, 'clients_table'): print(f" الجدول موجود - الأعمدة: {self.clients_table.columnCount()}") print(f" عرض عمود الهاتف: {self.clients_table.columnWidth(2)}px") print(f" عرض عمود الرصيد: {self.clients_table.columnWidth(5)}px") else: print(" الجدول غير موجود!") # التحقق من القائمة السياقية if hasattr(self, 'show_advanced_context_menu'): print(" القائمة السياقية المتقدمة متاحة") else: print(" القائمة السياقية المتقدمة غير متاحة!") # التحقق من التحليل الذكي if hasattr(self, 'analytics_engine'): print(f" محرك التحليل الذكي متاح - الرؤى: {len(self.analytics_engine.get('insights', []))}") else: print(" محرك التحليل الذكي غير متاح!") # التحقق من التنبيهات if hasattr(self, 'notification_system'): overdue = len(self.notification_system.get('overdue_clients', [])) high_value = len(self.notification_system.get('high_value_clients', [])) inactive = len(self.notification_system.get('inactive_clients', [])) reminders = len(self.notification_system.get('payment_reminders', [])) print(f" نظام التنبيهات متاح - متأخرين: {overdue}, عالي القيمة: {high_value}, غير نشطين: {inactive}, تذكيرات: {reminders}") else: print(" نظام التنبيهات غير متاح!") # التحقق من التحديث التلقائي if hasattr(self, 'auto_refresh_timer'): print(f" التحديث التلقائي متاح - نشط: {self.auto_refresh_timer.isActive()}") else: print(" التحديث التلقائي غير متاح!") # التحقق من البحث السريع if hasattr(self, 'search_input'): print(" البحث السريع متاح") else: print(" البحث السريع غير متاح!") # التحقق من الإحصائيات المباشرة if hasattr(self, 'live_stats'): print(f" الإحصائيات المباشرة متاحة - العملاء: {self.live_stats.get('total_clients', 0)}") else: print(" الإحصائيات المباشرة غير متاحة!") # فرض تحديث الجدول لإظهار التطورات self.force_table_refresh() print(" تم التحقق من جميع المميزات المتقدمة!") # عرض توضيحي للمميزات self.show_features_demo() except Exception as e: print(f" خطأ في التحقق من المميزات: {str(e)}") def force_table_refresh(self): try: print(" فرض تحديث الجدول - معطل لتجنب التحديث التلقائي...") # تم تعطيل جميع عمليات التحديث التلقائي # self.setup_table_styling() # self.apply_column_widths() # self.refresh_clients_data() # self.update_live_statistics() # self.clients_table.setMouseTracking(True) # self.add_visual_enhancements() # self.highlight_important_data() # self.add_status_indicators() print(" تم تعطيل فرض تحديث الجدول لتجنب التحديث التلقائي!") except Exception as e: print(f" خطأ في فرض تحديث الجدول: {str(e)}") def show_features_demo(self): """عرض توضيحي للمميزات المتقدمة""" try: print(" بدء العرض التوضيحي للمميزات المتقدمة...") # عرض التحليل الذكي if hasattr(self, 'analytics_engine') and self.analytics_engine.get('insights'): print(" الرؤى الذكية المتاحة:") for insight in self.analytics_engine['insights'][:3]: # أول 3 رؤى print(f" • {insight.get('title', 'رؤية')}: {insight.get('description', 'وصف')}") # عرض التنبيهات if hasattr(self, 'notification_system'): print(" التنبيهات النشطة:") overdue = self.notification_system.get('overdue_clients', []) if overdue: print(f" {len(overdue)} عميل متأخر في الدفع") high_value = self.notification_system.get('high_value_clients', []) if high_value: print(f" {len(high_value)} عميل عالي القيمة") reminders = self.notification_system.get('payment_reminders', []) if reminders: print(f" {len(reminders)} تذكير دفع") # عرض الإحصائيات if hasattr(self, 'live_stats'): print(" الإحصائيات المباشرة:") print(f" إجمالي العملاء: {self.live_stats.get('total_clients', 0)}") print(f" العملاء النشطين: {self.live_stats.get('active_clients', 0)}") print(f" العملاء المدينين: {self.live_stats.get('debtor_clients', 0)}") print(f" إجمالي الرصيد: {self.live_stats.get('total_balance', 0):.2f}") print(" انتهى العرض التوضيحي!") except Exception as e: print(f" خطأ في العرض التوضيحي: {str(e)}") def add_visual_enhancements(self): try: print(" إضافة التحسينات المرئية...") # تحسين ألوان الجدول مع ضمان الوضوح enhanced_style = """ QTableWidget { background-color: #ffffff; alternate-background-color: #f8f9fa; selection-background-color: #007bff; selection-color: white; gridline-color: #dee2e6; border: 3px solid #007bff; border-radius: 10px; font-family: 'Segoe UI', Arial, sans-serif; font-size: 11px; } QTableWidget::item { padding: 10px; border-bottom: 1px solid #dee2e6; border-right: 1px solid #dee2e6; } QTableWidget::item:selected { background-color: #007bff !important; color: white !important; font-weight: bold; } QTableWidget::item:hover { background-color: #e3f2fd !important; border: 2px solid #2196f3; } QHeaderView::section { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4a5568, stop:1 #2d3748); color: white; padding: 12px; border: none; font-weight: bold; font-size: 13px; text-align: center; } QHeaderView::section:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5a6578, stop:1 #3d4758); } # تم تعطيل التنسيق الأزرق للحفاظ على الإطار الأسود # self.clients_table.setStyleSheet(enhanced_style) # تعطيل الصفوف المتناوبة لتجنب التضارب مع تلوين الرصيد self.clients_table.setAlternatingRowColors(False) # تحسين التحديد self.clients_table.setSelectionBehavior(QTableWidget.SelectRows) # إضافة حدود واضحة self.clients_table.setShowGrid(True) # تحسين الخط font = self.clients_table.font() font.setPointSize(11) font.setFamily("Segoe UI") font.setBold(False) self.clients_table.setFont(font) # تحسين ارتفاع الصفوف self.clients_table.verticalHeader().setDefaultSectionSize(40) # تحسين عرض الأعمدة header = self.clients_table.horizontalHeader() header.setStretchLastSection(True) # إخفاء الرؤوس الجانبية self.clients_table.verticalHeader().setVisible(False) print(" تم إضافة التحسينات المرئية بنجاح!") except Exception as e: print(f" خطأ في إضافة التحسينات المرئية: {str(e)}") traceback.print_exc() def highlight_important_data(self): """تمييز البيانات المهمة بألوان مختلفة""" try: print(" تمييز البيانات المهمة...") for row in range(self.clients_table.rowCount()): # الحصول على الرصيد balance_item = self.clients_table.item(row, 5) # عمود الرصيد if balance_item and balance_item.text(): try: balance_text = balance_item.text().replace(',', '').replace(' ', '') balance = float(balance_text) # تحديد اللون حسب الرصيد row_color = None text_color = QColor(0, 0, 0) # أسود افتراضي if balance > 10000: # عميل عالي القيمة - أخضر فاتح row_color = QColor(220, 255, 220) text_color = QColor(0, 100, 0) elif balance > 1000: # عميل جيد - أخضر خفيف row_color = QColor(240, 255, 240) text_color = QColor(0, 120, 0) elif balance < -5000: # عميل عالي المخاطر - أحمر فاتح row_color = QColor(255, 220, 220) text_color = QColor(150, 0, 0) elif balance < -1000: # عميل متوسط المخاطر - برتقالي فاتح row_color = QColor(255, 240, 220) text_color = QColor(180, 100, 0) elif balance < 0: # عميل مدين - أصفر فاتح row_color = QColor(255, 255, 220) text_color = QColor(150, 150, 0) # تم تعطيل تطبيق اللون على الصف لتجنب التضارب مع تلوين الرصيد # if row_color: # for col in range(self.clients_table.columnCount()): # item = self.clients_table.item(row, col) # if item: # item.setBackground(row_color) # item.setForeground(text_color) # # # إضافة تنسيق خاص للرصيد # if col == 5: # عمود الرصيد # font = item.font() # font.setBold(True) # item.setFont(font) except ValueError: print(f" خطأ في تحويل الرصيد للصف {row}: {balance_item.text()}") continue print(" تم تمييز البيانات المهمة بنجاح!") except Exception as e: print(f" خطأ في تمييز البيانات: {str(e)}") traceback.print_exc() def add_status_indicators(self): try: print(" إضافة مؤشرات الحالة...") for row in range(self.clients_table.rowCount()): # إضافة مؤشر الحالة في العمود الأول status_item = self.clients_table.item(row, 0) balance_item = self.clients_table.item(row, 5) if status_item and balance_item and balance_item.text(): try: # استخراج الرقم من النص (إزالة الرموز التعبيرية إذا وجدت) client_id_text = status_item.text() client_id_match = re.search(r'\d+', client_id_text) if not client_id_match: continue client_id = int(client_id_match.group()) # الحصول على الرصيد balance_text = balance_item.text().replace(',', '').replace(' ', '') balance = float(balance_text) # تحديد المؤشر والتنسيق if balance > 10000: status_item.setText(f" {client_id}") # VIP status_item.setToolTip("عميل VIP - قيمة عالية") elif balance > 1000: status_item.setText(f" {client_id}") # ممتاز status_item.setToolTip("عميل ممتاز") elif balance < -5000: status_item.setText(f" {client_id}") # خطر عالي status_item.setToolTip("عميل عالي المخاطر") elif balance < -1000: status_item.setText(f" {client_id}") # تحذير status_item.setToolTip("عميل متوسط المخاطر") elif balance < 0: status_item.setText(f"🟡 {client_id}") # مدين status_item.setToolTip("عميل مدين") else: status_item.setText(f" {client_id}") # عادي status_item.setToolTip("عميل عادي") # تنسيق خاص للمؤشر font = status_item.font() font.setBold(True) font.setPointSize(12) status_item.setFont(font) status_item.setTextAlignment(Qt.AlignCenter) except (ValueError, AttributeError) as e: print(f" خطأ في معالجة الصف {row}: {str(e)}") continue print(" تم إضافة مؤشرات الحالة بنجاح!") except Exception as e: print(f" خطأ في إضافة مؤشرات الحالة: {str(e)}") traceback.print_exc() def refresh_clients_data(self): """تحديث بيانات العملاء - معطل لتجنب التحديث التلقائي""" try: print(" تحديث بيانات العملاء - معطل لتجنب التحديث التلقائي...") # تم تعطيل التحديث التلقائي # self.clients_table.setRowCount(0) # clients = self.session.query(Client).all() # for client in clients: # self.add_client_to_table(client) print(" تم تعطيل تحديث بيانات العملاء لتجنب التحديث التلقائي") except Exception as e: print(f" خطأ في تحديث بيانات العملاء: {str(e)}") def apply_visual_enhancements_after_populate(self): try: # التأكد من وجود بيانات في الجدول if self.clients_table.rowCount() == 0: print(" لا توجد بيانات في الجدول لتطبيق التحسينات") self.enhancements_applied = False return # تجنب التطبيق المتكرر if self.enhancements_applied: print("ℹ التحسينات مطبقة بالفعل، تخطي...") return print(f" تطبيق التحسينات على {self.clients_table.rowCount()} صف...") # تطبيق التحسينات المرئية self.add_visual_enhancements() # تمييز البيانات المهمة self.highlight_important_data() # إضافة مؤشرات الحالة self.add_status_indicators() # تطبيق التلميحات المتقدمة self.apply_advanced_tooltips() # تعيين العلامة self.enhancements_applied = True print(" تم تطبيق جميع التحسينات المرئية بنجاح!") except Exception as e: print(f" خطأ في تطبيق التحسينات بعد ملء الجدول: {str(e)}") self.enhancements_applied = False def apply_advanced_tooltips(self): """تطبيق التلميحات المتقدمة على جميع خلايا الجدول""" try: print(" تطبيق التلميحات المتقدمة...") for row in range(self.clients_table.rowCount()): for col in range(self.clients_table.columnCount()): self.show_advanced_tooltip(row, col) print(" تم تطبيق التلميحات المتقدمة بنجاح!") except Exception as e: print(f" خطأ في تطبيق التلميحات المتقدمة: {str(e)}") def populate_table_simple(self, clients): try: print(f" ملء الجدول بـ {len(clients)} عميل...") # مسح الجدول self.clients_table.setRowCount(0) for row, client in enumerate(clients): self.clients_table.insertRow(row) # إضافة البيانات الأساسية فقط self.clients_table.setItem(row, 0, QTableWidgetItem(str(client.id))) self.clients_table.setItem(row, 1, QTableWidgetItem(client.name or "غير متوفر")) self.clients_table.setItem(row, 2, QTableWidgetItem(client.phone or "غير متوفر")) self.clients_table.setItem(row, 3, QTableWidgetItem(client.email or "غير متوفر")) self.clients_table.setItem(row, 4, QTableWidgetItem(client.address or "غير متوفر")) # إضافة الرصيد مع التلوين المباشر والقوي balance_item = QTableWidgetItem(f"{client.balance:,.0f}") balance_item.setTextAlignment(Qt.AlignCenter) # تطبيق خط موحد ثابت للرصيد font = QFont("Segoe UI", 11, QFont.Bold) # حجم ثابت 11 balance_item.setFont(font) # تلوين الرصيد بألوان بسيطة - أحمر وأخضر فقط if client.balance > 0: # أخضر للأرصدة الموجبة balance_item.setBackground(QColor(200, 255, 200)) # أخضر فاتح balance_item.setForeground(QColor(0, 150, 0)) # أخضر داكن elif client.balance < 0: # أحمر للأرصدة السالبة balance_item.setBackground(QColor(255, 200, 200)) # أحمر فاتح balance_item.setForeground(QColor(200, 0, 0)) # أحمر داكن else: # رمادي رصاصي للرصيد صفر balance_item.setBackground(QColor(220, 220, 220)) # رمادي فاتح balance_item.setForeground(QColor(80, 80, 80)) # رمادي رصاصي داكن self.clients_table.setItem(row, 5, balance_item) print(f" تم تلوين الرصيد للصف {row}: {client.balance}") self.clients_table.setItem(row, 6, QTableWidgetItem(client.notes or "غير متوفر")) # إضافة الحالة مع التحسينات الجديدة if client.balance > 0: status_text = "🟢 نشط" status_color = QColor("#38a169") status_bg = QColor("#c6f6d5") elif client.balance == 0: status_text = " عادي" status_color = QColor("#3182ce") status_bg = QColor("#bee3f8") else: status_text = " مدين" status_color = QColor("#e53e3e") status_bg = QColor("#fed7d7") # إنشاء عنصر الحالة مع التنسيق الموحد status_item = QTableWidgetItem(status_text) status_item.setTextAlignment(Qt.AlignCenter) status_item.setFont(QFont("Segoe UI", 12, QFont.Bold)) # خط موحد status_item.setForeground(status_color) status_item.setBackground(status_bg) self.clients_table.setItem(row, 7, status_item) # إضافة تاريخ الإنشاء if hasattr(client, 'created_at') and client.created_at: self.clients_table.setItem(row, 8, QTableWidgetItem(client.created_at.strftime('%Y-%m-%d'))) else: self.clients_table.setItem(row, 8, QTableWidgetItem("غير متوفر")) # توسيط النصوص for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) if item: item.setTextAlignment(Qt.AlignCenter) print(f" تم ملء الجدول بـ {len(clients)} عميل") # تطبيق ألوان الرصيد بعد ملء الجدول self.apply_balance_colors() except Exception as e: print(f" خطأ في ملء الجدول: {str(e)}") def create_simple_test_data(self): """إنشاء بيانات تجريبية بسيطة""" try: print("🧪 إنشاء بيانات تجريبية بسيطة...") # بيانات تجريبية متنوعة test_clients = [ {"id": " 1", "name": "أحمد محمد", "phone": "0501234567", "email": "<EMAIL>", "address": "الرياض", "balance": "15000", "notes": "عميل VIP"}, {"id": " 2", "name": "فاطمة علي", "phone": "0509876543", "email": "<EMAIL>", "address": "جدة", "balance": "-8000", "notes": "عميل مدين"}, {"id": " 3", "name": "محمد سالم", "phone": "0551122334", "email": "<EMAIL>", "address": "الدمام", "balance": "2500", "notes": "عميل عادي"}, {"id": " 4", "name": "نورا خالد", "phone": "0556677889", "email": "<EMAIL>", "address": "مكة", "balance": "-12000", "notes": "عميل عالي المخاطر"}, {"id": " 5", "name": "عبدالله أحمد", "phone": "0544455667", "email": "<EMAIL>", "address": "المدينة", "balance": "25000", "notes": "عميل ذهبي"} ] # إضافة البيانات للجدول for row, client_data in enumerate(test_clients): self.clients_table.insertRow(row) # إضافة البيانات self.clients_table.setItem(row, 0, QTableWidgetItem(client_data["id"])) self.clients_table.setItem(row, 1, QTableWidgetItem(client_data["name"])) self.clients_table.setItem(row, 2, QTableWidgetItem(client_data["phone"])) self.clients_table.setItem(row, 3, QTableWidgetItem(client_data["email"])) self.clients_table.setItem(row, 4, QTableWidgetItem(client_data["address"])) # إضافة الرصيد مع التلوين المباشر والقوي balance = float(client_data["balance"].replace(',', '')) balance_item = QTableWidgetItem(f"{balance:,.0f}") balance_item.setTextAlignment(Qt.AlignCenter) # تطبيق خط موحد ثابت للرصيد font = QFont("Segoe UI", 11, QFont.Bold) # حجم ثابت 11 balance_item.setFont(font) # تطبيق التلوين البسيط - أحمر وأخضر فقط if balance > 0: # أخضر للأرصدة الموجبة balance_item.setBackground(QColor(200, 255, 200)) # أخضر فاتح balance_item.setForeground(QColor(0, 150, 0)) # أخضر داكن elif balance < 0: # أحمر للأرصدة السالبة balance_item.setBackground(QColor(255, 200, 200)) # أحمر فاتح balance_item.setForeground(QColor(200, 0, 0)) # أحمر داكن else: # رمادي رصاصي للرصيد صفر balance_item.setBackground(QColor(220, 220, 220)) # رمادي فاتح balance_item.setForeground(QColor(80, 80, 80)) # رمادي رصاصي داكن self.clients_table.setItem(row, 5, balance_item) print(f" تم تلوين البيانات التجريبية للصف {row}: {balance}") self.clients_table.setItem(row, 6, QTableWidgetItem(client_data["notes"])) # إضافة الحالة مع التحسينات الجديدة balance = float(client_data["balance"].replace(',', '')) if balance > 0: status_text = "🟢 نشط" status_color = QColor("#38a169") status_bg = QColor("#c6f6d5") elif balance == 0: status_text = " عادى" status_color = QColor("#000000") status_bg = QColor("#f8f9fa") else: status_text = " مدين" status_color = QColor("#e53e3e") status_bg = QColor("#fed7d7") # إنشاء عنصر الحالة مع التنسيق الموحد status_item = QTableWidgetItem(status_text) status_item.setTextAlignment(Qt.AlignCenter) status_item.setFont(QFont("Segoe UI", 11, QFont.Bold)) # خط موحد status_item.setForeground(status_color) status_item.setBackground(status_bg) self.clients_table.setItem(row, 7, status_item) # إضافة التاريخ self.clients_table.setItem(row, 8, QTableWidgetItem("2024-01-01")) # توسيط النصوص وتطبيق الخط الأكبر for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) if item: item.setTextAlignment(Qt.AlignCenter) # تطبيق خط موحد للبيانات if col in [0, 1, 5, 7]: # الرقم، الاسم، الرصيد، الحالة item.setFont(QFont("Segoe UI", 11, QFont.Bold)) else: item.setFont(QFont("Segoe UI", 11, QFont.Bold)) print(f" تم إنشاء {len(test_clients)} عميل تجريبي") # تطبيق التحسينات فوراً على البيانات التجريبية print(" تطبيق التحسينات على البيانات التجريبية...") # self.apply_simple_enhancements() # محذوف - استخدام apply_visual_enhancements_after_populate بدلاً منه self.apply_visual_enhancements_after_populate() # تطبيق ألوان الرصيد في النهاية self.apply_balance_colors() except Exception as e: print(f" خطأ في إنشاء البيانات التجريبية: {str(e)}") def force_header_font_update(self): try: print(" فرض تحديث خط العناوين...") header = self.clients_table.horizontalHeader() # إعادة تطبيق الخط المميز header_font = QFont("Segoe UI", 15, QFont.Bold) # خط متوسط ومميز header_font.setLetterSpacing(QFont.AbsoluteSpacing, 1.0) # تباعد الأحرف header.setFont(header_font) # إعادة تطبيق الارتفاع الجديد header.setFixedHeight(55) # إعادة تطبيق التصميم المميز مع الفاصل header.setStyleSheet(""" QHeaderView::section { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #667eea, stop:0.5 #764ba2, stop:1 #f093fb); color: white; border: 3px solid #5a67d8; border-bottom: 5px solid #4c51bf; border-radius: 12px; padding: 12px 8px; margin: 2px; font-family: "Segoe UI"; font-size: 15px; font-weight: bold; text-align: center; min-height: 35px; max-height: 55px; letter-spacing: 1.0px; } QHeaderView::section:hover { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #5a67d8, stop:0.5 #667eea, stop:1 #e0c3fc); border: 3px solid #4c51bf; border-bottom: 5px solid #3c366b; } QHeaderView::section:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4c51bf, stop:0.5 #553c9a, stop:1 #c084fc); border: 3px solid #3c366b; border-bottom: 5px solid #2d3748; } # فرض التحديث header.update() header.repaint() print(" تم فرض تحديث خط العناوين مع الفاصل الواضح") except Exception as e: print(f" خطأ في فرض تحديث خط العناوين: {str(e)}") def force_column_widths(self): """فرض تطبيق أعراض الأعمدة الجديدة""" try: print(" فرض تطبيق أعراض الأعمدة الجديدة...") # الأعراض الجديدة المطلوبة new_widths = { 0: 120, # الرقم - عرض أكبر 2: 220, # الهاتف - عرض كبير 5: 200, # الرصيد - عرض كبير 7: 120, # الحالة 8: 70 # التاريخ - عرض أصغر } # تطبيق الأعراض بقوة for col, width in new_widths.items(): self.clients_table.setColumnWidth(col, width) print(f" تم تطبيق عرض العمود {col}: {width}px") # فرض إعادة تطبيق إعدادات التمدد header = self.clients_table.horizontalHeader() # الأعمدة الثابتة fixed_columns = [0, 2, 5, 7, 8] for col in fixed_columns: header.setSectionResizeMode(col, header.Fixed) # الأعمدة المتمددة stretch_columns = [1, 3, 4, 6] # الاسم، الإيميل، العنوان، الملاحظات for col in stretch_columns: header.setSectionResizeMode(col, header.Stretch) # فرض التحديث header.update() self.clients_table.update() print(" تم فرض تطبيق أعراض الأعمدة الجديدة بنجاح!") # طباعة الأعراض الفعلية للتأكد self.print_actual_column_widths() except Exception as e: print(f" خطأ في فرض تطبيق أعراض الأعمدة: {str(e)}") def print_actual_column_widths(self): try: print(" الأعراض الفعلية للأعمدة:") for col in range(self.clients_table.columnCount()): actual_width = self.clients_table.columnWidth(col) header_text = self.clients_table.horizontalHeaderItem(col).text() if self.clients_table.horizontalHeaderItem(col) else f"عمود {col}" print(f" {header_text}: {actual_width}px") except Exception as e: print(f" خطأ في طباعة أعراض الأعمدة: {str(e)}") def extract_client_id_from_text(self, text): """استخراج رقم العميل من النص (إزالة الرموز التعبيرية)""" try: client_id_match = re.search(r'\d+', text) if client_id_match: return int(client_id_match.group()) return None except Exception as e: print(f" خطأ في استخراج رقم العميل: {str(e)}") return None def get_selected_client_id(self): try: selected_row = self.clients_table.currentRow() if selected_row < 0: return None client_id_text = self.clients_table.item(selected_row, 0).text() return self.extract_client_id_from_text(client_id_text) except Exception as e: print(f" خطأ في الحصول على رقم العميل المحدد: {str(e)}") return None def apply_simple_column_widths(self): """تطبيق أعراض الأعمدة بطريقة مبسطة""" try: print(" تطبيق أعراض الأعمدة المبسطة...") # الأعراض المطلوبة widths = { 0: 120, # الرقم - أكبر 1: 150, # الاسم 2: 220, # الهاتف 3: 180, # الإيميل 4: 150, # العنوان 5: 200, # الرصيد 6: 120, # الملاحظات 7: 120, # الحالة 8: 70 # التاريخ - أصغر } # تطبيق الأعراض for col, width in widths.items(): if col < self.clients_table.columnCount(): self.clients_table.setColumnWidth(col, width) print(f" عمود {col}: {width}px") print(" تم تطبيق أعراض الأعمدة المبسطة") except Exception as e: print(f" خطأ في تطبيق أعراض الأعمدة المبسطة: {str(e)}") def apply_correct_widths(self): try: print(" تطبيق الأعراض الصحيحة...") # تطبيق الأعراض مباشرة self.clients_table.setColumnWidth(0, 120) # الرقم - أكبر self.clients_table.setColumnWidth(1, 150) # الاسم self.clients_table.setColumnWidth(2, 220) # الهاتف self.clients_table.setColumnWidth(3, 180) # الإيميل self.clients_table.setColumnWidth(4, 150) # العنوان self.clients_table.setColumnWidth(5, 200) # الرصيد self.clients_table.setColumnWidth(6, 120) # الملاحظات self.clients_table.setColumnWidth(7, 120) # الحالة self.clients_table.setColumnWidth(8, 70) # التاريخ - أصغر # فرض التحديث self.clients_table.update() self.clients_table.repaint() # تطبيق ارتفاع الصفوف لتناسب النص الأكبر for row in range(self.clients_table.rowCount()): self.clients_table.setRowHeight(row, 50) # ارتفاع أكبر للصفوف print(" تم تطبيق الأعراض الصحيحة بنجاح!") print(f" عرض الرقم: {self.clients_table.columnWidth(0)}px") print(f" عرض التاريخ: {self.clients_table.columnWidth(8)}px") print(f" ارتفاع الصفوف: 50px") except Exception as e: print(f" خطأ في تطبيق الأعراض الصحيحة: {str(e)}") def extract_client_id_from_text(self, text): """استخراج رقم العميل من النص (إزالة الرموز التعبيرية)""" try: client_id_match = re.search(r'\d+', text) if client_id_match: return int(client_id_match.group()) return None except Exception as e: print(f" خطأ في استخراج رقم العميل: {str(e)}") return None def get_selected_client_id(self): try: selected_row = self.clients_table.currentRow() if selected_row < 0: return None client_id_text = self.clients_table.item(selected_row, 0).text() return self.extract_client_id_from_text(client_id_text) except Exception as e: print(f" خطأ في الحصول على رقم العميل المحدد: {str(e)}") return None def apply_visual_enhancements_to_data(self): """تطبيق التحسينات المرئية مباشرة على البيانات""" try: print(" تطبيق التحسينات المرئية على البيانات...") for row in range(self.clients_table.rowCount()): # تطبيق الخط الأكبر على جميع الخلايا for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) if item: # تطبيق خط أكبر if col in [0, 1, 5, 7]: # الرقم، الاسم، الرصيد، الحالة item.setFont(QFont("Segoe UI", 12, QFont.Bold)) else: item.setFont(QFont("Segoe UI", 12)) # تطبيق التوسيط item.setTextAlignment(Qt.AlignCenter) # تطبيق ارتفاع الصف self.clients_table.setRowHeight(row, 50) # تطبيق تحسينات خاصة بالرصيد balance_item = self.clients_table.item(row, 5) if balance_item: try: # استخراج القيمة الرقمية من النص balance_text = balance_item.text() balance_value = float(balance_text.replace('ر.س', '').replace(',', '').strip()) # تم تعطيل تلوين الرصيد هنا لتجنب التضارب مع دالة apply_balance_colors # التلوين الآن يتم في دالة apply_balance_colors المخصصة pass except: pass # تطبيق تحسينات على الحالة مع إضافة حالة "عادي" status_item = self.clients_table.item(row, 7) if status_item and status_item.text(): # تطبيق خط موحد للحالة status_item.setFont(QFont("Segoe UI", 12, QFont.Bold)) status_item.setTextAlignment(Qt.AlignCenter) if "نشط" in status_item.text(): status_item.setForeground(QColor("#38a169")) status_item.setBackground(QColor("#c6f6d5")) elif "عادي" in status_item.text(): status_item.setForeground(QColor("#3182ce")) status_item.setBackground(QColor("#bee3f8")) elif "مدين" in status_item.text(): status_item.setForeground(QColor("#e53e3e")) status_item.setBackground(QColor("#fed7d7")) # تطبيق تحسينات على الهاتف phone_item = self.clients_table.item(row, 2) if phone_item and phone_item.text() and phone_item.text() != "غير محدد": phone_item.setForeground(QColor("#38a169")) phone_item.setBackground(QColor("#f0fff4")) # تطبيق تحسينات على الإيميل - لون أسود موحد email_item = self.clients_table.item(row, 3) if email_item and email_item.text() and email_item.text() != "غير محدد" and email_item.text() != "غير متوفر": email_item.setForeground(QColor("#000000")) # أسود موحد email_item.setBackground(QColor("#f8f9fa")) # خلفية رمادية فاتحة # تطبيق ألوان الرصيد في النهاية لضمان عدم الكتابة عليها self.apply_balance_colors() print(" تم تطبيق التحسينات المرئية على البيانات بنجاح!") except Exception as e: print(f" خطأ في تطبيق التحسينات المرئية على البيانات: {str(e)}") def style_advanced_button(self, button, button_type, has_menu=False): try: # تحديد الألوان المتنوعة والمميزة حسب نوع الزر colors = { 'primary': { 'bg_start': '#1e3a8a', 'bg_mid': '#3b82f6', 'bg_end': '#1d4ed8', 'bg_bottom': '#1e40af', 'hover_start': '#3b82f6', 'hover_mid': '#60a5fa', 'hover_end': '#2563eb', 'hover_bottom': '#1d4ed8', 'hover_border': '#2563eb', 'pressed_start': '#1e40af', 'pressed_mid': '#1d4ed8', 'pressed_end': '#1e3a8a', 'pressed_bottom': '#172554', 'pressed_border': '#1e3a8a', 'border': '#1d4ed8', 'text': '#ffffff' }, 'success': { 'bg_start': '#065f46', 'bg_mid': '#10b981', 'bg_end': '#047857', 'bg_bottom': '#064e3b', 'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#059669', 'hover_bottom': '#047857', 'hover_border': '#059669', 'pressed_start': '#064e3b', 'pressed_mid': '#047857', 'pressed_end': '#065f46', 'pressed_bottom': '#022c22', 'pressed_border': '#065f46', 'border': '#047857', 'text': '#ffffff' }, 'danger': { 'bg_start': '#991b1b', 'bg_mid': '#ef4444', 'bg_end': '#dc2626', 'bg_bottom': '#7f1d1d', 'hover_start': '#ef4444', 'hover_mid': '#f87171', 'hover_end': '#dc2626', 'hover_bottom': '#991b1b', 'hover_border': '#dc2626', 'pressed_start': '#7f1d1d', 'pressed_mid': '#991b1b', 'pressed_end': '#450a0a', 'pressed_bottom': '#450a0a', 'pressed_border': '#991b1b', 'border': '#dc2626', 'text': '#ffffff' }, 'warning': { 'bg_start': '#92400e', 'bg_mid': '#f59e0b', 'bg_end': '#d97706', 'bg_bottom': '#78350f', 'hover_start': '#f59e0b', 'hover_mid': '#fbbf24', 'hover_end': '#d97706', 'hover_bottom': '#92400e', 'hover_border': '#d97706', 'pressed_start': '#78350f', 'pressed_mid': '#92400e', 'pressed_end': '#451a03', 'pressed_bottom': '#451a03', 'pressed_border': '#92400e', 'border': '#d97706', 'text': '#ffffff' }, 'purple': { 'bg_start': '#581c87', 'bg_mid': '#a855f7', 'bg_end': '#9333ea', 'bg_bottom': '#4c1d95', 'hover_start': '#a855f7', 'hover_mid': '#c084fc', 'hover_end': '#9333ea', 'hover_bottom': '#581c87', 'hover_border': '#9333ea', 'pressed_start': '#4c1d95', 'pressed_mid': '#581c87', 'pressed_end': '#312e81', 'pressed_bottom': '#312e81', 'pressed_border': '#581c87', 'border': '#9333ea', 'text': '#ffffff' }, 'indigo': { 'bg_start': '#3730a3', 'bg_mid': '#6366f1', 'bg_end': '#4f46e5', 'bg_bottom': '#312e81', 'hover_start': '#6366f1', 'hover_mid': '#818cf8', 'hover_end': '#4f46e5', 'hover_bottom': '#3730a3', 'hover_border': '#4f46e5', 'pressed_start': '#312e81', 'pressed_mid': '#3730a3', 'pressed_end': '#1e1b4b', 'pressed_bottom': '#1e1b4b', 'pressed_border': '#3730a3', 'border': '#4f46e5', 'text': '#ffffff' }, 'modern_teal': { 'bg_start': '#0891b2', 'bg_mid': '#0ea5e9', 'bg_end': '#0284c7', 'bg_bottom': '#075985', 'hover_start': '#0ea5e9', 'hover_mid': '#38bdf8', 'hover_end': '#0891b2', 'hover_bottom': '#0284c7', 'hover_border': '#0891b2', 'pressed_start': '#0284c7', 'pressed_mid': '#0891b2', 'pressed_end': '#075985', 'pressed_bottom': '#0c4a6e', 'pressed_border': '#075985', 'border': '#0891b2', 'text': '#ffffff' }, 'teal': { 'bg_start': '#134e4a', 'bg_mid': '#14b8a6', 'bg_end': '#0f766e', 'bg_bottom': '#115e59', 'hover_start': '#14b8a6', 'hover_mid': '#5eead4', 'hover_end': '#0f766e', 'hover_bottom': '#134e4a', 'hover_border': '#0f766e', 'pressed_start': '#115e59', 'pressed_mid': '#134e4a', 'pressed_end': '#042f2e', 'pressed_bottom': '#042f2e', 'pressed_border': '#134e4a', 'border': '#0f766e', 'text': '#ffffff' }, 'rose': { 'bg_start': '#9f1239', 'bg_mid': '#f43f5e', 'bg_end': '#e11d48', 'bg_bottom': '#881337', 'hover_start': '#f43f5e', 'hover_mid': '#fb7185', 'hover_end': '#e11d48', 'hover_bottom': '#9f1239', 'hover_border': '#e11d48', 'pressed_start': '#881337', 'pressed_mid': '#9f1239', 'pressed_end': '#4c0519', 'pressed_bottom': '#4c0519', 'pressed_border': '#9f1239', 'border': '#e11d48', 'text': '#ffffff' }, 'emerald': { 'bg_start': '#064e3b', 'bg_mid': '#10b981', 'bg_end': '#059669', 'bg_bottom': '#022c22', 'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#059669', 'hover_bottom': '#064e3b', 'hover_border': '#059669', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b', 'pressed_end': '#014737', 'pressed_bottom': '#014737', 'pressed_border': '#064e3b', 'border': '#059669', 'text': '#ffffff' }, 'cyan': { 'bg_start': '#155e75', 'bg_mid': '#06b6d4', 'bg_end': '#0891b2', 'bg_bottom': '#164e63', 'hover_start': '#06b6d4', 'hover_mid': '#67e8f9', 'hover_end': '#0891b2', 'hover_bottom': '#155e75', 'hover_border': '#0891b2', 'pressed_start': '#164e63', 'pressed_mid': '#155e75', 'pressed_end': '#083344', 'pressed_bottom': '#083344', 'pressed_border': '#155e75', 'border': '#0891b2', 'text': '#ffffff' }, 'orange': { 'bg_start': '#9a3412', 'bg_mid': '#ea580c', 'bg_end': '#dc2626', 'bg_bottom': '#7c2d12', 'hover_start': '#ea580c', 'hover_mid': '#fb923c', 'hover_end': '#dc2626', 'hover_bottom': '#9a3412', 'hover_border': '#dc2626', 'pressed_start': '#7c2d12', 'pressed_mid': '#9a3412', 'pressed_end': '#431407', 'pressed_bottom': '#431407', 'pressed_border': '#9a3412', 'border': '#dc2626', 'text': '#ffffff' }, 'slate': { 'bg_start': '#334155', 'bg_mid': '#64748b', 'bg_end': '#475569', 'bg_bottom': '#1e293b', 'hover_start': '#64748b', 'hover_mid': '#94a3b8', 'hover_end': '#475569', 'hover_bottom': '#334155', 'hover_border': '#475569', 'pressed_start': '#1e293b', 'pressed_mid': '#334155', 'pressed_end': '#0f172a', 'pressed_bottom': '#0f172a', 'pressed_border': '#334155', 'border': '#475569', 'text': '#ffffff' }, 'info': { 'bg_start': '#4fd1c7', 'bg_end': '#38b2ac', 'hover_start': '#81e6d9', 'hover_end': '#4fd1c7', 'pressed_start': '#38b2ac', 'pressed_end': '#319795', 'border': '#38b2ac', 'text': 'white' }, 'secondary': { 'bg_start': '#a0aec0', 'bg_end': '#718096', 'hover_start': '#cbd5e0', 'hover_end': '#a0aec0', 'pressed_start': '#718096', 'pressed_end': '#4a5568', 'border': '#718096', 'text': 'white' }, 'black': { 'bg_start': '#000000', 'bg_mid': '#1a1a1a', 'bg_end': '#0d0d0d', 'bg_bottom': '#000000', 'hover_start': '#1a1a1a', 'hover_mid': '#333333', 'hover_end': '#262626', 'hover_bottom': '#1a1a1a', 'hover_border': '#333333', 'pressed_start': '#000000', 'pressed_mid': '#0d0d0d', 'pressed_end': '#000000', 'pressed_bottom': '#000000', 'pressed_border': '#000000', 'border': '#1a1a1a', 'text': '#ffffff' }, 'lime': { 'bg_start': '#365314', 'bg_mid': '#84cc16', 'bg_end': '#65a30d', 'bg_bottom': '#1a2e05', 'hover_start': '#84cc16', 'hover_mid': '#a3e635', 'hover_end': '#65a30d', 'hover_bottom': '#365314', 'hover_border': '#65a30d', 'pressed_start': '#1a2e05', 'pressed_mid': '#365314', 'pressed_end': '#14532d', 'pressed_bottom': '#14532d', 'pressed_border': '#365314', 'border': '#65a30d', 'text': '#ffffff' } } color_scheme = colors.get(button_type, colors['primary']) # تطبيق التصميم الراقي المتوافق مع Qt button.setStyleSheet(f""" QPushButton {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['bg_start']}, stop:0.2 {color_scheme.get('bg_mid', color_scheme['bg_start'])}, stop:0.5 {color_scheme['bg_end']}, stop:0.8 {color_scheme.get('bg_bottom', color_scheme['bg_end'])}, stop:1 {color_scheme['bg_start']}); color: {color_scheme['text']}; border: 3px solid {color_scheme['border']}; border-radius: 12px; padding: 10px 12px; font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif; font-size: 13px; font-weight: bold; min-height: 32px; max-height: 36px; margin: 0px; }} QPushButton:hover {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['hover_start']}, stop:0.2 {color_scheme.get('hover_mid', color_scheme['hover_start'])}, stop:0.5 {color_scheme['hover_end']}, stop:0.8 {color_scheme.get('hover_bottom', color_scheme['hover_end'])}, stop:1 {color_scheme['hover_start']}); border: 3px solid {color_scheme.get('hover_border', color_scheme['hover_end'])}; border-radius: 14px; }} QPushButton:pressed {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 {color_scheme['pressed_start']}, stop:0.2 {color_scheme.get('pressed_mid', color_scheme['pressed_start'])}, stop:0.5 {color_scheme['pressed_end']}, stop:0.8 {color_scheme.get('pressed_bottom', color_scheme['pressed_end'])}, stop:1 {color_scheme['pressed_start']}); border: 3px solid {color_scheme.get('pressed_border', color_scheme['pressed_end'])}; border-radius: 10px; }} QPushButton:disabled {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f7fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e0, stop:1 #a0aec0); color: #718096; border: 3px solid #cbd5e0; border-radius: 12px; }} QPushButton::menu-indicator {{ {f"image: none; width: 0px;" if not has_menu else "width: 12px; height: 12px; margin-right: 4px;"} }} # تطبيق خط واضح وسهل القراءة font = QFont("Segoe UI", 13, QFont.Bold) # خط واضح ومقروء font.setLetterSpacing(QFont.AbsoluteSpacing, 0.2) # تباعد أحرف أقل font.setStyleHint(QFont.SansSerif) # نوع الخط font.setHintingPreference(QFont.PreferFullHinting) # تحسين وضوح الخط button.setFont(font) except Exception as e: print(f" خطأ في تطبيق تصميم الزر المتطور: {str(e)}") def style_advanced_menu(self, menu): """تطبيق تصميم خيالي فائق التطور على القوائم المنسدلة مع تأثيرات بصرية مذهلة وألوان سحرية""" try: QMenu { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:0.02 #fefefe, stop:0.05 #fdfdfd, stop:0.08 #f8fafc, stop:0.12 #f1f5f9, stop:0.18 #e2e8f0, stop:0.25 #cbd5e1, stop:0.35 #94a3b8, stop:0.5 #64748b, stop:0.65 #475569, stop:0.8 #334155, stop:0.9 #1e293b, stop:0.95 #0f172a, stop:1 #020617); border: 6px solid qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ff0080, stop:0.08 #ff4080, stop:0.16 #ff8000, stop:0.24 #ffbf00, stop:0.32 #ffff00, stop:0.4 #80ff00, stop:0.48 #00ff00, stop:0.56 #00ff80, stop:0.64 #00ffff, stop:0.72 #0080ff, stop:0.8 #0000ff, stop:0.88 #8000ff, stop:0.96 #ff00ff, stop:1 #ff0080); border-radius: 28px; padding: 18px; font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica', sans-serif; font-size: 18px; font-weight: 900; min-width: 350px; max-width: 450px; /* box-shadow غير مدعوم في PyQt5 */ } QMenu::item { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(255, 255, 255, 0.99), stop:0.05 rgba(248, 250, 252, 0.97), stop:0.1 rgba(241, 245, 249, 0.95), stop:0.2 rgba(226, 232, 240, 0.93), stop:0.3 rgba(203, 213, 225, 0.91), stop:0.45 rgba(148, 163, 184, 0.89), stop:0.6 rgba(100, 116, 139, 0.87), stop:0.75 rgba(71, 85, 105, 0.85), stop:0.85 rgba(51, 65, 85, 0.83), stop:0.92 rgba(30, 41, 59, 0.81), stop:0.97 rgba(15, 23, 42, 0.79), stop:1 rgba(2, 6, 23, 0.77)); padding: 20px 36px; border-radius: 20px; margin: 8px 6px; color: #0f172a; font-size: 18px; font-weight: 900; border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 rgba(99, 102, 241, 0.6), stop:0.25 rgba(139, 92, 246, 0.7), stop:0.5 rgba(168, 85, 247, 0.8), stop:0.75 rgba(217, 70, 239, 0.7), stop:1 rgba(236, 72, 153, 0.6)); text-align: center; min-height: 32px; letter-spacing: 1px; /* text-shadow غير مدعوم في PyQt5 */ /* box-shadow غير مدعوم في PyQt5 */ } QMenu::item:selected { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ff6b6b, stop:0.1 #ff8e53, stop:0.2 #ff6348, stop:0.3 #ff9ff3, stop:0.4 #54a0ff, stop:0.5 #5f27cd, stop:0.6 #00d2d3, stop:0.7 #ff9ff3, stop:0.8 #54a0ff, stop:0.9 #5f27cd, stop:1 #ff6b6b); color: #ffffff; border: 5px solid qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #ff0080, stop:0.2 #8000ff, stop:0.4 #0080ff, stop:0.6 #00ff80, stop:0.8 #ff8000, stop:1 #ff0080); font-weight: 900; font-size: 20px; /* خصائص مدعومة في PyQt5 فقط */ letter-spacing: 1.5px; } QMenu::item:pressed { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1a1a2e, stop:0.15 #16213e, stop:0.3 #0f3460, stop:0.45 #533483, stop:0.6 #e94560, stop:0.75 #f39c12, stop:0.9 #27ae60, stop:1 #8e44ad); color: #ffffff; border: 5px solid qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #000000, stop:0.2 #2c3e50, stop:0.4 #34495e, stop:0.6 #7f8c8d, stop:0.8 #95a5a6, stop:1 #000000); font-weight: 900; font-size: 20px; /* خصائص مدعومة في PyQt5 فقط */ letter-spacing: 1.5px; } QMenu::item:disabled { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(156, 163, 175, 0.6), stop:0.3 rgba(107, 114, 128, 0.5), stop:0.7 rgba(75, 85, 99, 0.4), stop:1 rgba(55, 65, 81, 0.3)); color: #9ca3af; border: 4px solid rgba(156, 163, 175, 0.5); font-weight: 500; /* خصائص مدعومة في PyQt5 فقط */ letter-spacing: 0.8px; opacity: 0.6; } QMenu::separator { height: 6px; background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 rgba(255, 0, 128, 0.8), stop:0.1 rgba(255, 64, 128, 0.85), stop:0.2 rgba(255, 128, 0, 0.9), stop:0.3 rgba(255, 191, 0, 0.85), stop:0.4 rgba(255, 255, 0, 0.8), stop:0.5 rgba(128, 255, 0, 0.85), stop:0.6 rgba(0, 255, 0, 0.9), stop:0.7 rgba(0, 255, 128, 0.85), stop:0.8 rgba(0, 255, 255, 0.8), stop:0.9 rgba(0, 128, 255, 0.85), stop:1 rgba(128, 0, 255, 0.9)); margin: 18px 32px; border-radius: 4px; /* خصائص مدعومة في PyQt5 فقط */ } QMenu::icon { padding-left: 24px; padding-right: 20px; width: 32px; height: 32px; } /* @keyframes غير مدعوم في PyQt5 */ """) except Exception as e: print(f" خطأ في تطبيق تصميم القائمة المتطورة: {str(e)}") def style_premium_menu(self, menu, menu_type='default'): try: # تحديد الألوان السحرية المتطورة حسب نوع القائمة color_schemes = { 'default': { 'bg_gradient': 'stop:0 #ffffff, stop:0.02 #fefefe, stop:0.05 #fdfdfd, stop:0.08 #f8fafc, stop:0.12 #f1f5f9, stop:0.18 #e2e8f0, stop:0.25 #cbd5e1, stop:0.35 #94a3b8, stop:0.5 #64748b, stop:0.65 #475569, stop:0.8 #334155, stop:0.9 #1e293b, stop:0.95 #0f172a, stop:1 #020617', 'border_gradient': 'stop:0 #ff0080, stop:0.08 #ff4080, stop:0.16 #ff8000, stop:0.24 #ffbf00, stop:0.32 #ffff00, stop:0.4 #80ff00, stop:0.48 #00ff00, stop:0.56 #00ff80, stop:0.64 #00ffff, stop:0.72 #0080ff, stop:0.8 #0000ff, stop:0.88 #8000ff, stop:0.96 #ff00ff, stop:1 #ff0080', 'item_selected': 'stop:0 #ff6b6b, stop:0.1 #ff8e53, stop:0.2 #ff6348, stop:0.3 #ff9ff3, stop:0.4 #54a0ff, stop:0.5 #5f27cd, stop:0.6 #00d2d3, stop:0.7 #ff9ff3, stop:0.8 #54a0ff, stop:0.9 #5f27cd, stop:1 #ff6b6b', 'item_pressed': 'stop:0 #1a1a2e, stop:0.15 #16213e, stop:0.3 #0f3460, stop:0.45 #533483, stop:0.6 #e94560, stop:0.75 #f39c12, stop:0.9 #27ae60, stop:1 #8e44ad', 'separator': 'stop:0 rgba(255, 0, 128, 0.9), stop:0.1 rgba(255, 64, 128, 0.95), stop:0.2 rgba(255, 128, 0, 1.0), stop:0.3 rgba(255, 191, 0, 0.95), stop:0.4 rgba(255, 255, 0, 0.9), stop:0.5 rgba(128, 255, 0, 0.95), stop:0.6 rgba(0, 255, 0, 1.0), stop:0.7 rgba(0, 255, 128, 0.95), stop:0.8 rgba(0, 255, 255, 0.9), stop:0.9 rgba(0, 128, 255, 0.95), stop:1 rgba(128, 0, 255, 1.0)' }, 'success': { 'bg_gradient': 'stop:0 #f0fff4, stop:0.03 #e6fffa, stop:0.06 #ccfbf1, stop:0.1 #99f6e4, stop:0.15 #5eead4, stop:0.25 #2dd4bf, stop:0.4 #14b8a6, stop:0.6 #0d9488, stop:0.8 #0f766e, stop:0.9 #115e59, stop:0.95 #134e4a, stop:1 #042f2e', 'border_gradient': 'stop:0 #10b981, stop:0.1 #059669, stop:0.2 #047857, stop:0.3 #065f46, stop:0.4 #064e3b, stop:0.5 #022c22, stop:0.6 #14532d, stop:0.7 #166534, stop:0.8 #15803d, stop:0.9 #16a34a, stop:1 #22c55e', 'item_selected': 'stop:0 #dcfce7, stop:0.1 #bbf7d0, stop:0.2 #86efac, stop:0.3 #4ade80, stop:0.4 #22c55e, stop:0.5 #16a34a, stop:0.6 #15803d, stop:0.7 #166534, stop:0.8 #14532d, stop:0.9 #052e16, stop:1 #022c22', 'item_pressed': 'stop:0 #042f2e, stop:0.2 #134e4a, stop:0.4 #115e59, stop:0.6 #0f766e, stop:0.8 #0d9488, stop:1 #14b8a6', 'separator': 'stop:0 rgba(16, 185, 129, 0.9), stop:0.2 rgba(5, 150, 105, 0.95), stop:0.4 rgba(4, 120, 87, 1.0), stop:0.6 rgba(6, 95, 70, 0.95), stop:0.8 rgba(6, 78, 59, 0.9), stop:1 rgba(2, 44, 34, 0.85)' }, 'warning': { 'bg_gradient': 'stop:0 #fffbeb, stop:0.03 #fef3c7, stop:0.06 #fde68a, stop:0.1 #fcd34d, stop:0.15 #fbbf24, stop:0.25 #f59e0b, stop:0.4 #d97706, stop:0.6 #b45309, stop:0.8 #92400e, stop:0.9 #78350f, stop:0.95 #451a03, stop:1 #1c0701', 'border_gradient': 'stop:0 #f59e0b, stop:0.1 #d97706, stop:0.2 #b45309, stop:0.3 #92400e, stop:0.4 #78350f, stop:0.5 #451a03, stop:0.6 #7c2d12, stop:0.7 #9a3412, stop:0.8 #c2410c, stop:0.9 #ea580c, stop:1 #fb923c', 'item_selected': 'stop:0 #fef3c7, stop:0.1 #fde68a, stop:0.2 #fcd34d, stop:0.3 #fbbf24, stop:0.4 #f59e0b, stop:0.5 #d97706, stop:0.6 #b45309, stop:0.7 #92400e, stop:0.8 #78350f, stop:0.9 #451a03, stop:1 #1c0701', 'item_pressed': 'stop:0 #1c0701, stop:0.2 #451a03, stop:0.4 #78350f, stop:0.6 #92400e, stop:0.8 #b45309, stop:1 #d97706', 'separator': 'stop:0 rgba(245, 158, 11, 0.9), stop:0.2 rgba(217, 119, 6, 0.95), stop:0.4 rgba(180, 83, 9, 1.0), stop:0.6 rgba(146, 64, 14, 0.95), stop:0.8 rgba(120, 53, 15, 0.9), stop:1 rgba(69, 26, 3, 0.85)' }, 'danger': { 'bg_gradient': 'stop:0 #fef2f2, stop:0.03 #fecaca, stop:0.06 #fca5a5, stop:0.1 #f87171, stop:0.15 #ef4444, stop:0.25 #dc2626, stop:0.4 #b91c1c, stop:0.6 #991b1b, stop:0.8 #7f1d1d, stop:0.9 #450a0a, stop:0.95 #1f0404, stop:1 #0c0202', 'border_gradient': 'stop:0 #ef4444, stop:0.1 #dc2626, stop:0.2 #b91c1c, stop:0.3 #991b1b, stop:0.4 #7f1d1d, stop:0.5 #450a0a, stop:0.6 #7f1d1d, stop:0.7 #991b1b, stop:0.8 #b91c1c, stop:0.9 #dc2626, stop:1 #f87171', 'item_selected': 'stop:0 #fecaca, stop:0.1 #fca5a5, stop:0.2 #f87171, stop:0.3 #ef4444, stop:0.4 #dc2626, stop:0.5 #b91c1c, stop:0.6 #991b1b, stop:0.7 #7f1d1d, stop:0.8 #450a0a, stop:0.9 #1f0404, stop:1 #0c0202', 'item_pressed': 'stop:0 #0c0202, stop:0.2 #1f0404, stop:0.4 #450a0a, stop:0.6 #7f1d1d, stop:0.8 #991b1b, stop:1 #b91c1c', 'separator': 'stop:0 rgba(239, 68, 68, 0.9), stop:0.2 rgba(220, 38, 38, 0.95), stop:0.4 rgba(185, 28, 28, 1.0), stop:0.6 rgba(153, 27, 27, 0.95), stop:0.8 rgba(127, 29, 29, 0.9), stop:1 rgba(69, 10, 10, 0.85)' } } scheme = color_schemes.get(menu_type, color_schemes['default']) menu.setStyleSheet(f""" QMenu {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, {scheme['bg_gradient']}); border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1, {scheme['border_gradient']}); border-radius: 18px; padding: 12px; font-family: 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica', sans-serif; font-size: 15px; font-weight: 700; min-width: 240px; max-width: 300px; /* خصائص مدعومة في PyQt5 فقط */ }} QMenu::item {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(255, 255, 255, 0.98), stop:0.05 rgba(248, 250, 252, 0.96), stop:0.15 rgba(241, 245, 249, 0.94), stop:0.3 rgba(226, 232, 240, 0.92), stop:0.5 rgba(203, 213, 225, 0.9), stop:0.7 rgba(148, 163, 184, 0.88), stop:0.85 rgba(100, 116, 139, 0.86), stop:1 rgba(71, 85, 105, 0.84)); padding: 14px 24px; border-radius: 12px; margin: 4px 3px; color: #0f172a; font-size: 15px; font-weight: 700; border: 3px solid qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 rgba(99, 102, 241, 0.6), stop:0.25 rgba(139, 92, 246, 0.7), stop:0.5 rgba(168, 85, 247, 0.8), stop:0.75 rgba(217, 70, 239, 0.7), stop:1 rgba(236, 72, 153, 0.6)); text-align: center; min-height: 20px; letter-spacing: 0.8px; /* خصائص مدعومة في PyQt5 فقط */ }} QMenu::item:selected {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, {scheme['item_selected']}); color: #ffffff; border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #ff0080, stop:0.2 #8000ff, stop:0.4 #0080ff, stop:0.6 #00ff80, stop:0.8 #ff8000, stop:1 #ff0080); font-weight: 900; font-size: 16px; /* خصائص مدعومة في PyQt5 فقط */ letter-spacing: 1.2px; }} QMenu::item:pressed {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, {scheme['item_pressed']}); color: #ffffff; border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #000000, stop:0.25 #2c3e50, stop:0.5 #34495e, stop:0.75 #7f8c8d, stop:1 #000000); font-weight: 900; font-size: 16px; /* خصائص مدعومة في PyQt5 فقط */ letter-spacing: 1.2px; }} QMenu::item:disabled {{ background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(156, 163, 175, 0.5), stop:0.5 rgba(107, 114, 128, 0.4), stop:1 rgba(75, 85, 99, 0.3)); color: #9ca3af; border: 3px solid rgba(156, 163, 175, 0.4); font-weight: 500; /* خصائص مدعومة في PyQt5 فقط */ letter-spacing: 0.6px; opacity: 0.6; }} QMenu::separator {{ height: 4px; background: qlineargradient(x1:0, y1:0, x2:1, y2:0, {scheme['separator']}); margin: 12px 20px; border-radius: 3px; /* خصائص مدعومة في PyQt5 فقط */ }} QMenu::icon {{ padding-left: 16px; padding-right: 12px; width: 20px; height: 20px; }} /* @keyframes غير مدعوم في PyQt5 */ except Exception as e: print(f" خطأ في تطبيق تصميم القائمة البريميوم: {str(e)}") def create_elegant_separator(self): """إنشاء فاصل أسود يملأ ارتفاع الإطار من الأعلى للأسفل""" try: separator = QFrame() separator.setFixedWidth(2) # عرض رفيع جداً separator.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding) # يتمدد عمودياً QFrame { background-color: #000000; border: none; border-radius: 1px; margin: 0px 4px; padding: 0px; } """) return separator except Exception as e: print(f" خطأ في إنشاء الفاصل الأسود: {str(e)}") return QFrame() # ==================== دوال الأزرار المطورة والمحسنة ==================== def manage_attachments(self): try: selected_items = self.clients_table.selectedItems() if not selected_items: show_error_message("تحذير", "يرجى تحديد عميل أولاً") return # الحصول على معرف العميل من الصف المحدد row = selected_items[0].row() client_id_item = self.clients_table.item(row, 0) if not client_id_item: show_error_message("خطأ", "لا يمكن تحديد العميل المحدد") return client_id = int(client_id_item.text()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "العميل غير موجود") return # فتح نافذة إدارة المرفقات (مؤقت) show_info_message("إدارة المرفقات", f"سيتم فتح نافذة إدارة المرفقات للعميل: {client.name}\n(هذه الميزة قيد التطوير)") # TODO: إضافة نافذة إدارة المرفقات لاحقاً except Exception as e: show_error_message("خطأ", f"حدث خطأ في إدارة المرفقات: {str(e)}") def backup_clients_data(self): """إنشاء نسخة احتياطية من بيانات العملاء""" try: # اختيار مكان حفظ النسخة الاحتياطية file_path, _ = QFileDialog.getSaveFileName( self, "حفظ النسخة الاحتياطية", f"clients_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "JSON Files (*.json)" ) if not file_path: return # جمع بيانات العملاء clients = self.session.query(Client).all() backup_data = { 'backup_date': datetime.now().isoformat(), 'clients_count': len(clients), 'clients': [] } for client in clients: client_data = { 'id': client.id, 'name': client.name, 'phone': client.phone, 'email': client.email, 'address': client.address, 'balance': client.balance, 'notes': client.notes, 'created_at': client.created_at.isoformat() if client.created_at else None } backup_data['clients'].append(client_data) # حفظ النسخة الاحتياطية with open(file_path, 'w', encoding='utf-8') as f: json.dump(backup_data, f, ensure_ascii=False, indent=2) show_info_message("تم", f"تم إنشاء النسخة الاحتياطية بنجاح\nتم حفظ {len(clients)} عميل") except Exception as e: show_error_message("خطأ", f"حدث خطأ في إنشاء النسخة الاحتياطية: {str(e)}") def restore_clients_data(self): try: # اختيار ملف النسخة الاحتياطية file_path, _ = QFileDialog.getOpenFileName( self, "اختيار النسخة الاحتياطية", "", "JSON Files (*.json)" ) if not file_path: return # تأكيد الاستعادة if not show_confirmation_message("تأكيد الاستعادة", "هل أنت متأكد من استعادة البيانات؟\nسيتم استبدال البيانات الحالية"): return # قراءة النسخة الاحتياطية with open(file_path, 'r', encoding='utf-8') as f: backup_data = json.load(f) restored_count = 0 for client_data in backup_data.get('clients', []): # التحقق من وجود العميل existing_client = self.session.query(Client).filter_by(name=client_data['name']).first() if not existing_client: # إنشاء عميل جديد client = Client( name=client_data['name'], phone=client_data.get('phone'), email=client_data.get('email'), address=client_data.get('address'), balance=client_data.get('balance', 0.0), notes=client_data.get('notes') ) self.session.add(client) restored_count += 1 self.session.commit() self.refresh_data() show_info_message("تم", f"تم استعادة {restored_count} عميل بنجاح") except Exception as e: show_error_message("خطأ", f"حدث خطأ في استعادة البيانات: {str(e)}") self.session.rollback() def generate_clients_report(self): """إنشاء تقرير شامل للعملاء""" try: # تقرير مؤقت بسيط clients = self.session.query(Client).all() total_clients = len(clients) تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')} إجمالي العملاء: {total_clients} تفاصيل العملاء: """ for i, client in enumerate(clients[:10], 1): # أول 10 عملاء report_text += f"{i}. {client.name} - الرصيد: {client.balance:,.0f} جنية\n" if total_clients > 10: report_text += f"... و {total_clients - 10} عميل آخر" show_info_message("تقرير العملاء", report_text) except Exception as e: show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}") def show_statistics(self): try: clients = self.session.query(Client).all() total_clients = len(clients) positive_balance = len([c for c in clients if c.balance > 0]) negative_balance = len([c for c in clients if c.balance < 0]) zero_balance = len([c for c in clients if c.balance == 0]) total_positive_amount = sum([c.balance for c in clients if c.balance > 0]) total_negative_amount = sum([c.balance for c in clients if c.balance < 0]) stats_message = f""" إحصائيات العملاء: إجمالي العملاء: {total_clients} عملاء لهم مبلغ: {positive_balance} ({total_positive_amount:,.0f} جنية) عملاء عليهم مبلغ: {negative_balance} ({abs(total_negative_amount):,.0f} جنية) عملاء بدون رصيد: {zero_balance} show_info_message("إحصائيات العملاء", stats_message) except Exception as e: show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات: {str(e)}") def show_detailed_statistics(self): """عرض إحصائيات مفصلة للعملاء""" try: clients = self.session.query(Client).all() # حساب إحصائيات مفصلة balance_ranges = { 'أكثر من 10000': len([c for c in clients if c.balance > 10000]), '5000-10000': len([c for c in clients if 5000 <= c.balance <= 10000]), '1000-5000': len([c for c in clients if 1000 <= c.balance < 5000]), '0-1000': len([c for c in clients if 0 <= c.balance < 1000]), 'أقل من 0': len([c for c in clients if c.balance < 0]) } stats_text = " إحصائيات مفصلة للعملاء:\n\n" stats_text += " توزيع الأرصدة:\n" for range_name, count in balance_ranges.items(): stats_text += f"• {range_name}: {count} عميل\n" show_info_message("إحصائيات مفصلة", stats_text) except Exception as e: show_error_message("خطأ", f"حدث خطأ في عرض الإحصائيات المفصلة: {str(e)}") def show_balance_analysis(self): try: clients = self.session.query(Client).all() # تحليل الأرصدة positive_clients = [c for c in clients if c.balance > 0] negative_clients = [c for c in clients if c.balance < 0] analysis_text = " تحليل الأرصدة:\n\n" if positive_clients: max_positive = max(positive_clients, key=lambda x: x.balance) analysis_text += f" أعلى رصيد: {max_positive.name} ({max_positive.balance:,.0f} جنية)\n" if negative_clients: max_negative = min(negative_clients, key=lambda x: x.balance) analysis_text += f" أقل رصيد: {max_negative.name} ({max_negative.balance:,.0f} جنية)\n" avg_balance = sum(c.balance for c in clients) / len(clients) if clients else 0 analysis_text += f" متوسط الرصيد: {avg_balance:,.0f} جنية" show_info_message("تحليل الأرصدة", analysis_text) except Exception as e: show_error_message("خطأ", f"حدث خطأ في تحليل الأرصدة: {str(e)}") def show_monthly_report(self): """عرض التقرير الشهري""" try: # تقرير شهري بسيط current_month = datetime.now().month current_year = datetime.now().year clients = self.session.query(Client).all() إجمالي العملاء: {len(clients)} إجمالي الأرصدة الموجبة: {sum(c.balance for c in clients if c.balance > 0):,.0f} جنية إجمالي الأرصدة السالبة: {abs(sum(c.balance for c in clients if c.balance < 0)):,.0f} جنية نمو العملاء: سيتم إضافة هذه الميزة لاحقاً""" show_info_message("التقرير الشهري", report_text) except Exception as e: show_error_message("خطأ", f"حدث خطأ في التقرير الشهري: {str(e)}") # ==================== دوال إدارة الأعمدة ==================== def show_columns_dialog(self): try: QLabel, QFrame, QHBoxLayout, QPushButton, QScrollArea, QWidget, QGraphicsDropShadowEffect) # محاولة استيراد QPropertyAnimation مع معالجة الخطأ try: animation_available = True except ImportError: animation_available = False print(" QPropertyAnimation غير متوفر، سيتم استخدام تأثيرات بديلة") dialog = QDialog(self) dialog.setWindowTitle(" إدارة الأعمدة المتطورة والذكية") dialog.setFixedSize(500, 650) dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint) # تطبيق النمط الموحد المتطور للحوار مع تأثيرات متقدمة dialog.setStyleSheet(""" QDialog { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #667eea, stop:0.2 #764ba2, stop:0.4 #f093fb, stop:0.6 #4facfe, stop:0.8 #00f2fe, stop:1 #43e97b); border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #3b82f6, stop:0.5 #8b5cf6, stop:1 #06b6d4); border-radius: 0px; font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif; } # إضافة تأثير الظل للنافذة shadow_effect = QGraphicsDropShadowEffect() shadow_effect.setBlurRadius(30) shadow_effect.setColor(QColor(0, 0, 0, 100)) shadow_effect.setOffset(0, 10) dialog.setGraphicsEffect(shadow_effect) layout = QVBoxLayout() layout.setSpacing(20) layout.setContentsMargins(25, 25, 25, 25) # شريط العنوان المتطور مثل الشريط الرئيسي title_bar = QFrame() title_bar.setFixedHeight(60) title_bar.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e3a8a, stop:0.3 #3b82f6, stop:0.7 #8b5cf6, stop:1 #ec4899); border: none; border-radius: 0px; margin: 0px; padding: 0px; } title_bar_layout = QHBoxLayout(title_bar) title_bar_layout.setContentsMargins(15, 0, 15, 0) title_bar_layout.setSpacing(15) # الجانب الأيسر - أزرار التحكم left_section = QFrame() left_section.setStyleSheet("QFrame { background: transparent; }") left_layout = QHBoxLayout(left_section) left_layout.setContentsMargins(0, 0, 0, 0) left_layout.setSpacing(8) # زر تصغير minimize_btn = QPushButton("") minimize_btn.setFixedSize(30, 30) minimize_btn.setStyleSheet(""" QPushButton { background: rgba(255, 255, 255, 0.2); color: white; border: none; border-radius: 0px; font-size: 12px; font-weight: bold; } QPushButton:hover { background: rgba(255, 255, 255, 0.3); transform: scale(1.1); } QPushButton:pressed { background: rgba(255, 255, 255, 0.4); transform: scale(0.95); } minimize_btn.clicked.connect(dialog.showMinimized) # زر إغلاق close_btn = QPushButton("") close_btn.setFixedSize(30, 30) close_btn.setStyleSheet(""" QPushButton { background: rgba(239, 68, 68, 0.8); color: white; border: none; border-radius: 0px; font-size: 14px; font-weight: bold; } QPushButton:hover { background: rgba(220, 38, 38, 0.9); transform: scale(1.1); } QPushButton:pressed { background: rgba(185, 28, 28, 1.0); transform: scale(0.95); } close_btn.clicked.connect(dialog.reject) left_layout.addWidget(close_btn) left_layout.addWidget(minimize_btn) left_layout.addStretch() # الوسط - عنوان النافذة center_section = QFrame() center_section.setStyleSheet("QFrame { background: transparent; }") center_layout = QHBoxLayout(center_section) center_layout.setContentsMargins(0, 0, 0, 0) # أيقونة ونص العنوان title_icon = QLabel("") title_icon.setStyleSheet(""" QLabel { font-size: 24px; color: white; background: rgba(255, 255, 255, 0.2); border-radius: 12px; padding: 8px; min-width: 40px; max-width: 40px; text-align: center; } title_icon.setAlignment(Qt.AlignCenter) title_text = QLabel("إدارة الأعمدة الذكية") title_text.setStyleSheet(""" QLabel { color: white; font-weight: bold; font-size: 18px; background: transparent; margin-left: 10px; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3); } center_layout.addWidget(title_icon) center_layout.addWidget(title_text) # الجانب الأيمن - فارغ right_section = QFrame() right_section.setStyleSheet("QFrame { background: transparent; }") right_layout = QHBoxLayout(right_section) right_layout.setContentsMargins(0, 0, 0, 0) right_layout.addStretch() # إضافة الأقسام إلى شريط العنوان (معكوس) title_bar_layout.addWidget(left_section, 1) title_bar_layout.addWidget(center_section, 0, Qt.AlignCenter) title_bar_layout.addWidget(right_section, 1) layout.addWidget(title_bar) # إضافة وظيفة السحب المتطورة للنافذة def mousePressEvent(event): if event.button() == Qt.LeftButton: dialog.drag_start_position = event.globalPos() - dialog.frameGeometry().topLeft() dialog.dragging = True # تغيير شكل المؤشر عند بدء السحب dialog.setCursor(Qt.ClosedHandCursor) event.accept() def mouseMoveEvent(event): if event.buttons() == Qt.LeftButton and hasattr(dialog, 'drag_start_position') and dialog.dragging: # حساب الموضع الجديد new_position = event.globalPos() - dialog.drag_start_position # التأكد من أن النافذة لا تخرج من حدود الشاشة screen = QApplication.desktop().screenGeometry() dialog_size = dialog.frameGeometry() # تحديد الحد الأدنى والأقصى للموضع min_x = 0 min_y = 0 max_x = screen.width() - dialog_size.width() max_y = screen.height() - dialog_size.height() # تطبيق القيود x = max(min_x, min(new_position.x(), max_x)) y = max(min_y, min(new_position.y(), max_y)) dialog.move(x, y) event.accept() def mouseReleaseEvent(event): if event.button() == Qt.LeftButton: dialog.dragging = False # إعادة شكل المؤشر الطبيعي dialog.setCursor(Qt.ArrowCursor) event.accept() # تطبيق الأحداث على شريط العنوان والنافذة title_bar.mousePressEvent = mousePressEvent title_bar.mouseMoveEvent = mouseMoveEvent title_bar.mouseReleaseEvent = mouseReleaseEvent # تطبيق الأحداث على النافذة نفسها أيضاً dialog.mousePressEvent = mousePressEvent dialog.mouseMoveEvent = mouseMoveEvent dialog.mouseReleaseEvent = mouseReleaseEvent # إضافة متغير لتتبع حالة السحب dialog.dragging = False # شريط الإحصائيات stats_frame = QFrame() stats_frame.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(59, 130, 246, 0.1), stop:1 rgba(139, 92, 246, 0.1)); border: 2px solid rgba(59, 130, 246, 0.3); border-radius: 15px; padding: 10px; margin-bottom: 15px; } stats_layout = QHBoxLayout(stats_frame) total_columns = len([ " رقم العميل", " اسم العميل", " رقم الهاتف", " البريد الإلكتروني", " العنوان", " الرصيد المالي", " الملاحظات", " حالة العميل", " تاريخ التسجيل" ]) stats_label = QLabel(f" إجمالي الأعمدة: {total_columns} | مرئية: {total_columns} | مخفية: 0") stats_label.setStyleSheet(""" QLabel { color: #374151; font-size: 14px; font-weight: 600; background: transparent; } stats_label.setAlignment(Qt.AlignCenter) stats_layout.addWidget(stats_label) layout.addWidget(stats_frame) # وصف تفصيلي desc_label = QLabel(" اختر الأعمدة التي تريد عرضها في الجدول") desc_label.setStyleSheet(""" QLabel { color: #475569; font-size: 14px; font-weight: 500; padding: 10px; background: rgba(255, 255, 255, 0.7); border-radius: 10px; border: 2px solid rgba(59, 130, 246, 0.2); } desc_label.setAlignment(Qt.AlignCenter) layout.addWidget(desc_label) # منطقة التمرير للأعمدة scroll_area = QScrollArea() scroll_area.setWidgetResizable(True) scroll_area.setStyleSheet(""" QScrollArea { border: 2px solid #e2e8f0; border-radius: 12px; background: white; } QScrollBar:vertical { background: #f1f5f9; width: 12px; border-radius: 6px; } QScrollBar::handle:vertical { background: #3b82f6; border-radius: 6px; min-height: 20px; } scroll_widget = QWidget() scroll_layout = QVBoxLayout(scroll_widget) scroll_layout.setSpacing(8) scroll_layout.setContentsMargins(15, 15, 15, 15) # قائمة الأعمدة مع checkboxes متطورة self.column_checkboxes = {} column_names = [ ("", "رقم العميل", "#6366f1"), ("", "اسم العميل", "#10b981"), ("", "رقم الهاتف", "#f59e0b"), ("", "البريد الإلكتروني", "#ef4444"), ("", "العنوان", "#8b5cf6"), ("", "الرصيد المالي", "#06b6d4"), ("", "الملاحظات", "#84cc16"), ("", "حالة العميل", "#f97316"), ("", "تاريخ التسجيل", "#ec4899") ] for i, (icon, name, color) in enumerate(column_names): checkbox_frame = QFrame() checkbox_frame.setStyleSheet(f""" QFrame {{ background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 rgba(255, 255, 255, 0.9), stop:1 {color}20); border: 2px solid {color}40; border-radius: 12px; padding: 8px; margin: 2px; }} QFrame:hover {{ background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 rgba(255, 255, 255, 0.95), stop:1 {color}30); border: 2px solid {color}60; transform: scale(1.02); }} checkbox_layout = QHBoxLayout(checkbox_frame) checkbox_layout.setContentsMargins(10, 5, 10, 5) checkbox = QCheckBox() is_column_checked = not self.clients_table.isColumnHidden(i) checkbox.setChecked(is_column_checked) # إضافة علامة الصح في النص if is_column_checked: checkbox.setText(" ") else: checkbox.setText("") checkbox.setStyleSheet(f""" QCheckBox {{ font-size: 16px; font-weight: bold; color: #10b981; spacing: 8px; }} QCheckBox::indicator {{ width: 24px; height: 24px; border: 2px solid {color}; border-radius: 6px; background: white; }} QCheckBox::indicator:checked {{ background: {color}; border: 2px solid {color}; }} QCheckBox::indicator:hover {{ border: 3px solid {color}; background: rgba(16, 185, 129, 0.1); }} # إضافة وظيفة تحديث علامة الصح def update_check_mark(state, cb=checkbox): if state == 2: # محدد cb.setText(" ") else: # غير محدد cb.setText("") checkbox.stateChanged.connect(update_check_mark) icon_label = QLabel(icon) icon_label.setStyleSheet(f""" QLabel {{ font-size: 18px; color: {color}; font-weight: bold; min-width: 25px; text-align: center; }} # إضافة علامة صح للأعمدة المحددة is_checked = not self.clients_table.isColumnHidden(i) check_mark = " " if is_checked else "" name_label = QLabel(f"{name}{check_mark}") name_label.setStyleSheet(""" QLabel { font-size: 14px; font-weight: 600; color: #374151; } # إضافة وظيفة تحديث النص عند تغيير الحالة def update_label_text(state, label=name_label, original_name=name): if state == 2: # محدد label.setText(f"{original_name} ") else: # غير محدد label.setText(original_name) checkbox.stateChanged.connect(update_label_text) checkbox_layout.addWidget(checkbox) checkbox_layout.addWidget(icon_label) checkbox_layout.addWidget(name_label) checkbox_layout.addStretch() self.column_checkboxes[i] = checkbox scroll_layout.addWidget(checkbox_frame) scroll_area.setWidget(scroll_widget) layout.addWidget(scroll_area) # أزرار الحوار المتطورة buttons_frame = QFrame() buttons_frame.setStyleSheet(""" QFrame { background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 10px; } buttons_layout = QHBoxLayout(buttons_frame) buttons_layout.setSpacing(6) buttons_layout.setContentsMargins(8, 8, 8, 8) # زر تحديد الكل - عرض متوازن select_all_btn = QPushButton(" تحديد الكل") select_all_btn.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #10b981, stop:1 #059669); color: white; border: none; border-radius: 10px; padding: 12px 18px; font-size: 14px; font-weight: 600; text-align: center; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #059669, stop:1 #047857); transform: translateY(-2px); } QPushButton:pressed { transform: translateY(0px); } select_all_btn.clicked.connect(lambda: self.toggle_all_columns(True)) buttons_layout.addWidget(select_all_btn, 1) # نسبة 1 # زر إلغاء تحديد الكل - عرض متوازن deselect_all_btn = QPushButton(" إلغاء الكل") deselect_all_btn.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ef4444, stop:1 #dc2626); color: white; border: none; border-radius: 10px; padding: 12px 18px; font-size: 14px; font-weight: 600; text-align: center; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #dc2626, stop:1 #b91c1c); transform: translateY(-2px); } QPushButton:pressed { transform: translateY(0px); } deselect_all_btn.clicked.connect(lambda: self.toggle_all_columns(False)) buttons_layout.addWidget(deselect_all_btn, 1) # نسبة 1 # زر تطبيق - عرض مصغر بدون أيقونة apply_btn = QPushButton("تطبيق التغييرات") apply_btn.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #3b82f6, stop:1 #1d4ed8); color: white; border: none; border-radius: 10px; padding: 12px 18px; font-size: 14px; font-weight: 600; text-align: center; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1d4ed8, stop:1 #1e40af); transform: translateY(-2px); } QPushButton:pressed { transform: translateY(0px); } apply_btn.clicked.connect(lambda: self.apply_column_visibility(dialog)) buttons_layout.addWidget(apply_btn, 1) # نسبة 1 (نفس الباقي) # زر إلغاء - عرض متوازن cancel_btn = QPushButton(" إلغاء") cancel_btn.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #6b7280, stop:1 #4b5563); color: white; border: none; border-radius: 10px; padding: 12px 18px; font-size: 14px; font-weight: 600; text-align: center; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4b5563, stop:1 #374151); transform: translateY(-2px); } QPushButton:pressed { transform: translateY(0px); } cancel_btn.clicked.connect(dialog.reject) buttons_layout.addWidget(cancel_btn, 1) # نسبة 1 layout.addWidget(buttons_frame) dialog.setLayout(layout) # إضافة تأثيرات التحريك البديلة def add_smooth_animation(): """إضافة تأثيرات تحريك سلسة بدون QPropertyAnimation""" try: # تأثير الظهور التدريجي dialog.setWindowOpacity(0.0) dialog.show() # استخدام QTimer لتحريك الشفافية opacity_timer = QTimer() opacity_step = 0.0 def increase_opacity(): nonlocal opacity_step opacity_step += 0.1 if opacity_step <= 1.0: dialog.setWindowOpacity(opacity_step) else: opacity_timer.stop() opacity_timer.timeout.connect(increase_opacity) opacity_timer.start(30) # كل 30 مللي ثانية print(" تم تطبيق تأثيرات التحريك البديلة بنجاح") except Exception as e: print(f" خطأ في تأثيرات التحريك: {e}") dialog.setWindowOpacity(1.0) dialog.show() # تطبيق التحريك add_smooth_animation() dialog.exec_() except Exception as e: show_error_message("خطأ", f"حدث خطأ في إدارة الأعمدة: {str(e)}") def toggle_all_columns(self, checked): try: for checkbox in self.column_checkboxes.values(): checkbox.setChecked(checked) except Exception as e: print(f" خطأ في تحديد الأعمدة: {str(e)}") def apply_column_visibility(self, dialog): """تطبيق إعدادات إظهار/إخفاء الأعمدة""" try: for column_index, checkbox in self.column_checkboxes.items(): if checkbox.isChecked(): self.clients_table.showColumn(column_index) else: self.clients_table.hideColumn(column_index) dialog.accept() show_info_message("تم", "تم تطبيق إعدادات الأعمدة بنجاح") except Exception as e: show_error_message("خطأ", f"حدث خطأ في تطبيق إعدادات الأعمدة: {str(e)}") # ==================== دوال تلوين الصفوف ==================== def apply_balance_colors(self): try: print(" تطبيق ألوان الرصيد...") # تعطيل الصفوف المتناوبة أولاً self.clients_table.setAlternatingRowColors(False) for row in range(self.clients_table.rowCount()): # الحصول على عنصر الرصيد balance_item = self.clients_table.item(row, 5) # عمود الرصيد if balance_item and balance_item.text(): try: # استخراج القيمة الرقمية (إزالة جميع النصوص غير الرقمية) balance_text = balance_item.text().replace(',', '').replace(' ر.س', '').replace('ر.س', '').strip() balance = float(balance_text) # تطبيق الألوان البسيطة - أحمر وأخضر فقط if balance > 0: # أخضر للأرصدة الموجبة balance_item.setBackground(QColor(200, 255, 200)) # أخضر فاتح balance_item.setForeground(QColor(0, 150, 0)) # أخضر داكن print(f"🟢 أخضر للصف {row} (رصيد: {balance})") elif balance < 0: # أحمر للأرصدة السالبة balance_item.setBackground(QColor(255, 200, 200)) # أحمر فاتح balance_item.setForeground(QColor(200, 0, 0)) # أحمر داكن print(f" أحمر للصف {row} (رصيد: {balance})") else: # رمادي رصاصي للرصيد صفر balance_item.setBackground(QColor(220, 220, 220)) # رمادي فاتح balance_item.setForeground(QColor(80, 80, 80)) # رمادي رصاصي داكن print(f" رمادي رصاصي للصف {row} (رصيد: {balance})") # تطبيق خط موحد ثابت للرصيد font = QFont("Segoe UI", 11, QFont.Bold) # حجم ثابت 11 balance_item.setFont(font) # فرض التحديث للخلية balance_item.setData(Qt.UserRole, f"balance_{balance}") except ValueError: print(f" خطأ في تحويل الرصيد للصف {row}: {balance_item.text()}") continue # فرض التحديث النهائي self.clients_table.update() self.clients_table.repaint() print(" تم تطبيق ألوان الرصيد بنجاح!") except Exception as e: print(f" خطأ في تطبيق ألوان الرصيد: {str(e)}") traceback.print_exc() def force_balance_colors_final(self): """فرض تطبيق ألوان الرصيد بطريقة نهائية ومقاومة للتضارب""" try: print(" فرض تطبيق ألوان الرصيد النهائية...") # تعطيل جميع الميزات التي قد تتضارب self.clients_table.setAlternatingRowColors(False) # تطبيق الألوان مباشرة على كل خلية رصيد for row in range(self.clients_table.rowCount()): balance_item = self.clients_table.item(row, 5) # عمود الرصيد if balance_item and balance_item.text(): try: # استخراج الرصيد balance_text = balance_item.text().replace(',', '').replace(' ر.س', '').replace('ر.س', '').strip() balance = float(balance_text) # تطبيق الألوان البسيطة - أحمر وأخضر فقط if balance > 0: # أخضر للأرصدة الموجبة balance_item.setBackground(QColor(200, 255, 200)) # أخضر فاتح balance_item.setForeground(QColor(0, 150, 0)) # أخضر داكن print(f"🟢 أخضر للصف {row} (رصيد: {balance})") elif balance < 0: # أحمر للأرصدة السالبة balance_item.setBackground(QColor(255, 200, 200)) # أحمر فاتح balance_item.setForeground(QColor(200, 0, 0)) # أحمر داكن print(f" أحمر للصف {row} (رصيد: {balance})") else: # رمادي رصاصي للرصيد صفر balance_item.setBackground(QColor(220, 220, 220)) # رمادي فاتح balance_item.setForeground(QColor(80, 80, 80)) # رمادي رصاصي داكن print(f" رمادي رصاصي للصف {row} (رصيد: {balance})") # تطبيق خط موحد ثابت للرصيد font = QFont("Segoe UI", 11, QFont.Bold) # حجم ثابت 11 balance_item.setFont(font) except ValueError: print(f" خطأ في تحويل الرصيد للصف {row}: {balance_item.text()}") continue # فرض التحديث self.clients_table.update() self.clients_table.repaint() print(" تم فرض تطبيق ألوان الرصيد النهائية بنجاح!") except Exception as e: print(f" خطأ في فرض تطبيق ألوان الرصيد: {str(e)}") traceback.print_exc() def apply_row_colors(self): try: for row in range(self.clients_table.rowCount()): # الحصول على الرصيد balance_item = self.clients_table.item(row, 5) # عمود الرصيد if balance_item and balance_item.text(): try: balance_text = balance_item.text().replace(',', '').replace(' ر.س', '').strip() balance = float(balance_text) # تحديد لون الصف حسب الرصيد if balance > 10000: row_color = QColor(220, 255, 220) # أخضر فاتح للعملاء عالي القيمة elif balance > 0: row_color = QColor(240, 255, 240) # أخضر خفيف للعملاء الجيدين elif balance < -5000: row_color = QColor(255, 220, 220) # أحمر فاتح للعملاء عالي المخاطر elif balance < 0: row_color = QColor(255, 240, 220) # برتقالي فاتح للعملاء المدينين else: row_color = QColor(245, 245, 245) # رمادي فاتح للرصيد الصفر # تطبيق اللون على جميع خلايا الصف for col in range(self.clients_table.columnCount()): item = self.clients_table.item(row, col) if item: item.setBackground(row_color) except ValueError: continue except Exception as e: print(f" خطأ في تطبيق ألوان الصفوف: {str(e)}") def get_selected_clients_count(self): """الحصول على عدد العملاء المحددين""" try: selected_rows = set() for item in self.clients_table.selectedItems(): selected_rows.add(item.row()) return len(selected_rows) except: return 0 def get_selected_clients_ids(self): try: selected_rows = set() for item in self.clients_table.selectedItems(): selected_rows.add(item.row()) client_ids = [] for row in selected_rows: id_item = self.clients_table.item(row, 0) # عمود المعرف if id_item and id_item.text().isdigit(): client_ids.append(int(id_item.text())) return client_ids except Exception as e: print(f" خطأ في الحصول على معرفات العملاء: {str(e)}") return [] # ==================== دوال العمليات المجمعة ==================== def show_bulk_operations_dialog(self): """عرض نافذة العمليات المجمعة المتطورة والذكية""" try: selected_count = self.get_selected_clients_count() if selected_count == 0: show_error_message("تحذير", "يرجى تحديد عميل واحد أو أكثر أولاً\n\nاستخدم Ctrl+Click أو Shift+Click لتحديد عدة عملاء") return QHBoxLayout, QFrame, QScrollArea, QWidget, QGraphicsDropShadowEffect) dialog = QDialog(self) dialog.setWindowTitle(f" العمليات المجمعة الذكية - {selected_count} عميل") dialog.setFixedSize(600, 750) dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint) # تطبيق النمط الموحد المتطور للحوار مع تأثيرات متقدمة QDialog { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #10b981, stop:0.2 #059669, stop:0.4 #047857, stop:0.6 #065f46, stop:0.8 #064e3b, stop:1 #022c22); border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #10b981, stop:0.5 #059669, stop:1 #047857); border-radius: 0px; font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif; } """) # إضافة تأثير الظل للنافذة shadow_effect = QGraphicsDropShadowEffect() shadow_effect.setBlurRadius(30) shadow_effect.setColor(QColor(0, 0, 0, 100)) shadow_effect.setOffset(0, 10) dialog.setGraphicsEffect(shadow_effect) layout = QVBoxLayout() layout.setSpacing(20) layout.setContentsMargins(25, 25, 25, 25) # شريط العنوان المتطور مثل الشريط الرئيسي title_bar = QFrame() title_bar.setFixedHeight(60) QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #065f46, stop:0.3 #10b981, stop:0.7 #059669, stop:1 #047857); border: none; border-radius: 0px; margin: 0px; padding: 0px; } """) title_bar_layout = QHBoxLayout(title_bar) title_bar_layout.setContentsMargins(15, 0, 15, 0) title_bar_layout.setSpacing(15) # الجانب الأيسر - أزرار التحكم left_section = QFrame() left_section.setStyleSheet("QFrame { background: transparent; }") left_layout = QHBoxLayout(left_section) left_layout.setContentsMargins(0, 0, 0, 0) left_layout.setSpacing(8) # زر تصغير minimize_btn = QPushButton("") minimize_btn.setFixedSize(30, 30) QPushButton { background: rgba(255, 255, 255, 0.2); color: white; border: none; border-radius: 0px; font-size: 12px; font-weight: bold; } QPushButton:hover { background: rgba(255, 255, 255, 0.3); transform: scale(1.1); } QPushButton:pressed { background: rgba(255, 255, 255, 0.4); transform: scale(0.95); } """) minimize_btn.clicked.connect(dialog.showMinimized) # زر إغلاق close_btn = QPushButton("") close_btn.setFixedSize(30, 30) QPushButton { background: rgba(239, 68, 68, 0.8); color: white; border: none; border-radius: 0px; font-size: 14px; font-weight: bold; } QPushButton:hover { background: rgba(220, 38, 38, 0.9); transform: scale(1.1); } QPushButton:pressed { background: rgba(185, 28, 28, 1.0); transform: scale(0.95); } """) close_btn.clicked.connect(dialog.reject) left_layout.addWidget(close_btn) left_layout.addWidget(minimize_btn) left_layout.addStretch() # الوسط - عنوان النافذة center_section = QFrame() center_section.setStyleSheet("QFrame { background: transparent; }") center_layout = QHBoxLayout(center_section) center_layout.setContentsMargins(0, 0, 0, 0) # أيقونة ونص العنوان title_icon = QLabel("") QLabel { font-size: 24px; color: white; background: rgba(255, 255, 255, 0.2); border-radius: 12px; padding: 8px; min-width: 40px; max-width: 40px; text-align: center; } """) title_icon.setAlignment(Qt.AlignCenter) title_text = QLabel(f"العمليات المجمعة - {selected_count} عميل") QLabel { color: white; font-weight: bold; font-size: 18px; background: transparent; margin-left: 10px; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3); } """) center_layout.addWidget(title_icon) center_layout.addWidget(title_text) # الجانب الأيمن - فارغ right_section = QFrame() right_section.setStyleSheet("QFrame { background: transparent; }") right_layout = QHBoxLayout(right_section) right_layout.setContentsMargins(0, 0, 0, 0) right_layout.addStretch() # إضافة الأقسام إلى شريط العنوان (معكوس) title_bar_layout.addWidget(left_section, 1) title_bar_layout.addWidget(center_section, 0, Qt.AlignCenter) title_bar_layout.addWidget(right_section, 1) layout.addWidget(title_bar) # إضافة وظيفة السحب المتطورة للنافذة def mousePressEvent(event): if event.button() == Qt.LeftButton: dialog.drag_start_position = event.globalPos() - dialog.frameGeometry().topLeft() dialog.dragging = True # تغيير شكل المؤشر عند بدء السحب dialog.setCursor(Qt.ClosedHandCursor) event.accept() def mouseMoveEvent(event): if event.buttons() == Qt.LeftButton and hasattr(dialog, 'drag_start_position') and dialog.dragging: # حساب الموضع الجديد new_position = event.globalPos() - dialog.drag_start_position # التأكد من أن النافذة لا تخرج من حدود الشاشة screen = QApplication.desktop().screenGeometry() dialog_size = dialog.frameGeometry() # تحديد الحد الأدنى والأقصى للموضع min_x = 0 min_y = 0 max_x = screen.width() - dialog_size.width() max_y = screen.height() - dialog_size.height() # تطبيق القيود x = max(min_x, min(new_position.x(), max_x)) y = max(min_y, min(new_position.y(), max_y)) dialog.move(x, y) event.accept() def mouseReleaseEvent(event): if event.button() == Qt.LeftButton: dialog.dragging = False # إعادة شكل المؤشر الطبيعي dialog.setCursor(Qt.ArrowCursor) event.accept() # تطبيق الأحداث على شريط العنوان والنافذة title_bar.mousePressEvent = mousePressEvent title_bar.mouseMoveEvent = mouseMoveEvent title_bar.mouseReleaseEvent = mouseReleaseEvent # تطبيق الأحداث على النافذة نفسها أيضاً dialog.mousePressEvent = mousePressEvent dialog.mouseMoveEvent = mouseMoveEvent dialog.mouseReleaseEvent = mouseReleaseEvent # إضافة متغير لتتبع حالة السحب dialog.dragging = False # وصف تفصيلي desc_label = QLabel(" اختر العملية التي تريد تطبيقها على العملاء المحددين") QLabel { color: #475569; font-size: 14px; font-weight: 500; padding: 10px; background: rgba(255, 255, 255, 0.7); border-radius: 10px; border: 2px solid rgba(16, 185, 129, 0.2); } """) desc_label.setAlignment(Qt.AlignCenter) layout.addWidget(desc_label) # منطقة التمرير للعمليات scroll_area = QScrollArea() scroll_area.setWidgetResizable(True) QScrollArea { border: 2px solid #e2e8f0; border-radius: 12px; background: white; } QScrollBar:vertical { background: #f1f5f9; width: 12px; border-radius: 6px; } QScrollBar::handle:vertical { background: #10b981; border-radius: 6px; min-height: 20px; } """) scroll_widget = QWidget() scroll_layout = QVBoxLayout(scroll_widget) scroll_layout.setSpacing(12) scroll_layout.setContentsMargins(15, 15, 15, 15) # أزرار العمليات المجمعة المتطورة operations = [ ("", "حذف المحددين", "حذف العملاء المحددين نهائياً", self.bulk_delete_clients, "#ef4444"), ("", "تصدير المحددين", "تصدير بيانات العملاء إلى ملف", self.bulk_export_clients, "#3b82f6"), ("", "تعديل الأرصدة", "تعديل أرصدة العملاء المحددين", self.bulk_edit_balances, "#f59e0b"), ("", "إيميل جماعي", "إرسال رسالة إيميل لجميع العملاء", self.bulk_send_email, "#10b981"), ("", "رسائل SMS", "إرسال رسائل نصية جماعية", self.bulk_send_sms, "#8b5cf6"), ("", "تعديل التصنيف", "تغيير تصنيف العملاء المحددين", self.bulk_edit_category, "#06b6d4"), ("", "تقرير مفصل", "إنشاء تقرير شامل للعملاء", self.bulk_generate_report, "#f97316"), ("", "تحديث الحالة", "تحديث حالة العملاء المحددين", self.bulk_update_status, "#84cc16") ] for icon, title, desc, callback, color in operations: operation_frame = QFrame() QFrame {{ background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 rgba(255, 255, 255, 0.9), stop:1 {color}15); border: 2px solid {color}40; border-radius: 12px; padding: 12px; margin: 3px; }} QFrame:hover {{ background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 rgba(255, 255, 255, 0.95), stop:1 {color}25); border: 2px solid {color}60; transform: scale(1.02); }} """) operation_layout = QHBoxLayout(operation_frame) operation_layout.setContentsMargins(10, 8, 10, 8) # أيقونة العملية icon_label = QLabel(icon) QLabel {{ font-size: 24px; color: {color}; font-weight: bold; min-width: 35px; max-width: 35px; text-align: center; background: {color}20; border-radius: 8px; padding: 8px; }} """) icon_label.setAlignment(Qt.AlignCenter) operation_layout.addWidget(icon_label) # معلومات العملية info_layout = QVBoxLayout() info_layout.setSpacing(2) title_label = QLabel(title) QLabel {{ font-size: 16px; font-weight: bold; color: {color}; margin: 0px; }} """) desc_label = QLabel(desc) QLabel { font-size: 12px; color: #6b7280; margin: 0px; } """) info_layout.addWidget(title_label) info_layout.addWidget(desc_label) operation_layout.addLayout(info_layout) operation_layout.addStretch() # زر تنفيذ العملية execute_btn = QPushButton("تنفيذ") QPushButton {{ background: {color}; color: white; border: none; border-radius: 8px; padding: 8px 16px; font-size: 12px; font-weight: 600; min-width: 60px; }} QPushButton:hover {{ background: {self.lighten_color(color)}; transform: translateY(-1px); }} QPushButton:pressed {{ background: {self.darken_color(color)}; transform: translateY(0px); }} """) execute_btn.clicked.connect(lambda checked, cb=callback: self.execute_bulk_operation(cb, dialog)) operation_layout.addWidget(execute_btn) scroll_layout.addWidget(operation_frame) scroll_area.setWidget(scroll_widget) layout.addWidget(scroll_area) # أزرار الحوار المتطورة buttons_frame = QFrame() QFrame { background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 10px; } """) buttons_layout = QHBoxLayout(buttons_frame) buttons_layout.setSpacing(10) # معلومات العملاء المحددين info_label = QLabel(f" {selected_count} عميل محدد للمعالجة") QLabel { color: #374151; font-size: 14px; font-weight: 600; padding: 8px 12px; background: rgba(16, 185, 129, 0.1); border-radius: 8px; border: 1px solid rgba(16, 185, 129, 0.3); } """) buttons_layout.addWidget(info_label) buttons_layout.addStretch() # زر إغلاق close_btn = QPushButton(" إغلاق") QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #6b7280, stop:1 #4b5563); color: white; border: none; border-radius: 10px; padding: 12px 20px; font-size: 14px; font-weight: 600; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4b5563, stop:1 #374151); transform: translateY(-2px); } QPushButton:pressed { transform: translateY(0px); } """) close_btn.clicked.connect(dialog.reject) buttons_layout.addWidget(close_btn) layout.addWidget(buttons_frame) dialog.setLayout(layout) # إضافة تأثيرات التحريك البديلة def add_smooth_animation(): try: # تأثير الظهور التدريجي dialog.setWindowOpacity(0.0) dialog.show() # استخدام QTimer لتحريك الشفافية opacity_timer = QTimer() opacity_step = 0.0 def increase_opacity(): nonlocal opacity_step opacity_step += 0.1 if opacity_step <= 1.0: dialog.setWindowOpacity(opacity_step) else: opacity_timer.stop() opacity_timer.timeout.connect(increase_opacity) opacity_timer.start(30) # كل 30 مللي ثانية print(" تم تطبيق تأثيرات التحريك البديلة للعمليات المجمعة") except Exception as e: print(f" خطأ في تأثيرات التحريك: {e}") dialog.setWindowOpacity(1.0) dialog.show() # تطبيق التحريك add_smooth_animation() dialog.exec_() except Exception as e: show_error_message("خطأ", f"حدث خطأ في العمليات المجمعة: {str(e)}") def bulk_generate_report(self): """إنشاء تقرير مفصل للعملاء المحددين""" try: selected_ids = self.get_selected_clients_ids() if not selected_ids: return show_info_message("تقرير مفصل", f"سيتم إنشاء تقرير مفصل لـ {len(selected_ids)} عميل\n(هذه الميزة قيد التطوير)") except Exception as e: show_error_message("خطأ", f"حدث خطأ في إنشاء التقرير: {str(e)}") def bulk_update_status(self): try: selected_ids = self.get_selected_clients_ids() if not selected_ids: return show_info_message("تحديث الحالة", f"سيتم تحديث حالة {len(selected_ids)} عميل\n(هذه الميزة قيد التطوير)") except Exception as e: show_error_message("خطأ", f"حدث خطأ في تحديث الحالة: {str(e)}") def execute_bulk_operation(self, callback, dialog): """تنفيذ عملية مجمعة""" try: dialog.accept() callback() except Exception as e: show_error_message("خطأ", f"حدث خطأ في تنفيذ العملية: {str(e)}") def bulk_delete_clients(self): try: selected_ids = self.get_selected_clients_ids() if not selected_ids: return if not show_confirmation_message("تأكيد الحذف", f"هل أنت متأكد من حذف {len(selected_ids)} عميل؟\nهذا الإجراء لا يمكن التراجع عنه"): return # حذف العملاء من قاعدة البيانات deleted_count = 0 for client_id in selected_ids: client = self.session.query(Client).get(client_id) if client: self.session.delete(client) deleted_count += 1 self.session.commit() self.refresh_data() show_info_message("تم", f"تم حذف {deleted_count} عميل بنجاح") except Exception as e: show_error_message("خطأ", f"حدث خطأ في حذف العملاء: {str(e)}") self.session.rollback() def bulk_export_clients(self): """تصدير العملاء المحددين""" try: selected_ids = self.get_selected_clients_ids() if not selected_ids: return show_info_message("تصدير", f"سيتم تصدير {len(selected_ids)} عميل\n(هذه الميزة قيد التطوير)") except Exception as e: show_error_message("خطأ", f"حدث خطأ في التصدير: {str(e)}") def bulk_edit_balances(self): try: selected_ids = self.get_selected_clients_ids() if not selected_ids: return show_info_message("تعديل الأرصدة", f"سيتم تعديل أرصدة {len(selected_ids)} عميل\n(هذه الميزة قيد التطوير)") except Exception as e: show_error_message("خطأ", f"حدث خطأ في تعديل الأرصدة: {str(e)}") def bulk_send_email(self): """إرسال إيميل جماعي""" try: selected_ids = self.get_selected_clients_ids() if not selected_ids: return show_info_message("إيميل جماعي", f"سيتم إرسال إيميل لـ {len(selected_ids)} عميل\n(هذه الميزة قيد التطوير)") except Exception as e: show_error_message("خطأ", f"حدث خطأ في الإيميل الجماعي: {str(e)}") def bulk_send_sms(self): try: selected_ids = self.get_selected_clients_ids() if not selected_ids: return show_info_message("رسائل SMS", f"سيتم إرسال رسائل لـ {len(selected_ids)} عميل\n(هذه الميزة قيد التطوير)") except Exception as e: show_error_message("خطأ", f"حدث خطأ في الرسائل الجماعية: {str(e)}") def bulk_edit_category(self): """تعديل تصنيف العملاء المحددين""" try: selected_ids = self.get_selected_clients_ids() if not selected_ids: return show_info_message("تعديل التصنيف", f"سيتم تعديل تصنيف {len(selected_ids)} عميل\n(هذه الميزة قيد التطوير)") except Exception as e: show_error_message("خطأ", f"حدث خطأ في تعديل التصنيف: {str(e)}") def lighten_color(self, color): # دالة بسيطة لتفتيح اللون return color.replace('#', '#').replace('ef4444', 'f87171').replace('3b82f6', '60a5fa').replace('f59e0b', 'fbbf24').replace('10b981', '34d399').replace('8b5cf6', 'a78bfa').replace('06b6d4', '22d3ee') def darken_color(self, color): """تغميق اللون""" # دالة بسيطة لتغميق اللون return color.replace('#', '#').replace('ef4444', 'dc2626').replace('3b82f6', '2563eb').replace('f59e0b', 'd97706').replace('10b981', '059669').replace('8b5cf6', '7c3aed').replace('06b6d4', '0891b2') # ==================== دوال الفلاتر المتقدمة ==================== def show_advanced_filters_dialog(self): try: QLineEdit, QComboBox, QDateEdit, QPushButton, QSpinBox, QGroupBox, QGridLayout, QFrame, QScrollArea, QWidget, QGraphicsDropShadowEffect) dialog = QDialog(self) dialog.setWindowTitle(" الفلاتر المتقدمة الذكية والمتطورة") dialog.setFixedSize(650, 800) dialog.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint) # تطبيق النمط الموحد المتطور للحوار مع تأثيرات متقدمة dialog.setStyleSheet(""" QDialog { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #8b5cf6, stop:0.2 #7c3aed, stop:0.4 #6d28d9, stop:0.6 #5b21b6, stop:0.8 #4c1d95, stop:1 #3730a3); border: 4px solid qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #8b5cf6, stop:0.5 #7c3aed, stop:1 #6d28d9); border-radius: 0px; font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif; } # إضافة تأثير الظل للنافذة shadow_effect = QGraphicsDropShadowEffect() shadow_effect.setBlurRadius(30) shadow_effect.setColor(QColor(0, 0, 0, 100)) shadow_effect.setOffset(0, 10) dialog.setGraphicsEffect(shadow_effect) layout = QVBoxLayout() layout.setSpacing(20) layout.setContentsMargins(25, 25, 25, 25) # شريط العنوان المتطور مثل الشريط الرئيسي title_bar = QFrame() title_bar.setFixedHeight(60) title_bar.setStyleSheet(""" QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4c1d95, stop:0.3 #8b5cf6, stop:0.7 #7c3aed, stop:1 #6d28d9); border: none; border-radius: 0px; margin: 0px; padding: 0px; } title_bar_layout = QHBoxLayout(title_bar) title_bar_layout.setContentsMargins(15, 0, 15, 0) title_bar_layout.setSpacing(15) # الجانب الأيسر - أزرار التحكم left_section = QFrame() left_section.setStyleSheet("QFrame { background: transparent; }") left_layout = QHBoxLayout(left_section) left_layout.setContentsMargins(0, 0, 0, 0) left_layout.setSpacing(8) # زر تصغير minimize_btn = QPushButton("") minimize_btn.setFixedSize(30, 30) minimize_btn.setStyleSheet(""" QPushButton { background: rgba(255, 255, 255, 0.2); color: white; border: none; border-radius: 0px; font-size: 12px; font-weight: bold; } QPushButton:hover { background: rgba(255, 255, 255, 0.3); transform: scale(1.1); } QPushButton:pressed { background: rgba(255, 255, 255, 0.4); transform: scale(0.95); } minimize_btn.clicked.connect(dialog.showMinimized) # زر إغلاق close_btn = QPushButton("") close_btn.setFixedSize(30, 30) close_btn.setStyleSheet(""" QPushButton { background: rgba(239, 68, 68, 0.8); color: white; border: none; border-radius: 0px; font-size: 14px; font-weight: bold; } QPushButton:hover { background: rgba(220, 38, 38, 0.9); transform: scale(1.1); } QPushButton:pressed { background: rgba(185, 28, 28, 1.0); transform: scale(0.95); } close_btn.clicked.connect(dialog.reject) left_layout.addWidget(close_btn) left_layout.addWidget(minimize_btn) left_layout.addStretch() # الوسط - عنوان النافذة center_section = QFrame() center_section.setStyleSheet("QFrame { background: transparent; }") center_layout = QHBoxLayout(center_section) center_layout.setContentsMargins(0, 0, 0, 0) # أيقونة ونص العنوان title_icon = QLabel("") title_icon.setStyleSheet(""" QLabel { font-size: 24px; color: white; background: rgba(255, 255, 255, 0.2); border-radius: 12px; padding: 8px; min-width: 40px; max-width: 40px; text-align: center; } title_icon.setAlignment(Qt.AlignCenter) title_text = QLabel("الفلاتر المتقدمة الذكية") title_text.setStyleSheet(""" QLabel { color: white; font-weight: bold; font-size: 18px; background: transparent; margin-left: 10px; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3); } center_layout.addWidget(title_icon) center_layout.addWidget(title_text) # الجانب الأيمن - فارغ right_section = QFrame() right_section.setStyleSheet("QFrame { background: transparent; }") right_layout = QHBoxLayout(right_section) right_layout.setContentsMargins(0, 0, 0, 0) right_layout.addStretch() # إضافة الأقسام إلى شريط العنوان (معكوس) title_bar_layout.addWidget(left_section, 1) title_bar_layout.addWidget(center_section, 0, Qt.AlignCenter) title_bar_layout.addWidget(right_section, 1) layout.addWidget(title_bar) # إضافة وظيفة السحب المتطورة للنافذة def mousePressEvent(event): if event.button() == Qt.LeftButton: dialog.drag_start_position = event.globalPos() - dialog.frameGeometry().topLeft() dialog.dragging = True # تغيير شكل المؤشر عند بدء السحب dialog.setCursor(Qt.ClosedHandCursor) event.accept() def mouseMoveEvent(event): if event.buttons() == Qt.LeftButton and hasattr(dialog, 'drag_start_position') and dialog.dragging: # حساب الموضع الجديد new_position = event.globalPos() - dialog.drag_start_position # التأكد من أن النافذة لا تخرج من حدود الشاشة screen = QApplication.desktop().screenGeometry() dialog_size = dialog.frameGeometry() # تحديد الحد الأدنى والأقصى للموضع min_x = 0 min_y = 0 max_x = screen.width() - dialog_size.width() max_y = screen.height() - dialog_size.height() # تطبيق القيود x = max(min_x, min(new_position.x(), max_x)) y = max(min_y, min(new_position.y(), max_y)) dialog.move(x, y) event.accept() def mouseReleaseEvent(event): if event.button() == Qt.LeftButton: dialog.dragging = False # إعادة شكل المؤشر الطبيعي dialog.setCursor(Qt.ArrowCursor) event.accept() # تطبيق الأحداث على شريط العنوان والنافذة title_bar.mousePressEvent = mousePressEvent title_bar.mouseMoveEvent = mouseMoveEvent title_bar.mouseReleaseEvent = mouseReleaseEvent # تطبيق الأحداث على النافذة نفسها أيضاً dialog.mousePressEvent = mousePressEvent dialog.mouseMoveEvent = mouseMoveEvent dialog.mouseReleaseEvent = mouseReleaseEvent # إضافة متغير لتتبع حالة السحب dialog.dragging = False # وصف تفصيلي desc_label = QLabel(" استخدم الفلاتر أدناه للبحث المتقدم في قاعدة بيانات العملاء") desc_label.setStyleSheet(""" QLabel { color: #475569; font-size: 14px; font-weight: 500; padding: 10px; background: rgba(255, 255, 255, 0.7); border-radius: 10px; border: 2px solid rgba(139, 92, 246, 0.2); } desc_label.setAlignment(Qt.AlignCenter) layout.addWidget(desc_label) # منطقة التمرير للفلاتر scroll_area = QScrollArea() scroll_area.setWidgetResizable(True) scroll_area.setStyleSheet(""" QScrollArea { border: 2px solid #e2e8f0; border-radius: 12px; background: white; } QScrollBar:vertical { background: #f1f5f9; width: 12px; border-radius: 6px; } QScrollBar::handle:vertical { background: #8b5cf6; border-radius: 6px; min-height: 20px; } scroll_widget = QWidget() scroll_layout = QVBoxLayout(scroll_widget) scroll_layout.setSpacing(15) scroll_layout.setContentsMargins(15, 15, 15, 15) # مجموعة فلاتر النص المتطورة text_group = QGroupBox(" فلاتر النص") text_group.setStyleSheet(""" QGroupBox { font-weight: bold; font-size: 16px; color: #374151; border: 2px solid #8b5cf6; border-radius: 12px; margin-top: 10px; padding-top: 15px; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 255, 255, 0.9), stop:1 rgba(139, 92, 246, 0.1)); } QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 8px 0 8px; color: #8b5cf6; background: white; border-radius: 6px; } text_layout = QGridLayout() text_layout.setSpacing(12) # نمط موحد للحقول field_style = """ QLineEdit { border: 2px solid #e2e8f0; border-radius: 8px; padding: 10px 12px; font-size: 14px; background: white; color: #374151; } QLineEdit:focus { border: 2px solid #8b5cf6; background: #faf5ff; } QLineEdit:hover { border: 2px solid #c4b5fd; } label_style = """ QLabel { font-weight: 600; font-size: 14px; color: #4b5563; padding: 5px; } # اسم العميل name_label = QLabel(" اسم العميل:") name_label.setStyleSheet(label_style) text_layout.addWidget(name_label, 0, 0) self.filter_name = QLineEdit() self.filter_name.setPlaceholderText("ابحث في أسماء العملاء...") self.filter_name.setStyleSheet(field_style) text_layout.addWidget(self.filter_name, 0, 1) # رقم الهاتف phone_label = QLabel(" رقم الهاتف:") phone_label.setStyleSheet(label_style) text_layout.addWidget(phone_label, 1, 0) self.filter_phone = QLineEdit() self.filter_phone.setPlaceholderText("ابحث في أرقام الهواتف...") self.filter_phone.setStyleSheet(field_style) text_layout.addWidget(self.filter_phone, 1, 1) # الإيميل email_label = QLabel(" الإيميل:") email_label.setStyleSheet(label_style) text_layout.addWidget(email_label, 2, 0) self.filter_email = QLineEdit() self.filter_email.setPlaceholderText("ابحث في الإيميلات...") self.filter_email.setStyleSheet(field_style) text_layout.addWidget(self.filter_email, 2, 1) # العنوان address_label = QLabel(" العنوان:") address_label.setStyleSheet(label_style) text_layout.addWidget(address_label, 3, 0) self.filter_address = QLineEdit() self.filter_address.setPlaceholderText("ابحث في العناوين...") self.filter_address.setStyleSheet(field_style) text_layout.addWidget(self.filter_address, 3, 1) text_group.setLayout(text_layout) scroll_layout.addWidget(text_group) # مجموعة فلاتر الرصيد المتطورة balance_group = QGroupBox(" فلاتر الرصيد") balance_group.setStyleSheet(""" QGroupBox { font-weight: bold; font-size: 16px; color: #374151; border: 2px solid #f59e0b; border-radius: 12px; margin-top: 10px; padding-top: 15px; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 255, 255, 0.9), stop:1 rgba(245, 158, 11, 0.1)); } QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 8px 0 8px; color: #f59e0b; background: white; border-radius: 6px; } balance_layout = QGridLayout() balance_layout.setSpacing(12) # نوع الرصيد balance_type_label = QLabel(" نوع الرصيد:") balance_type_label.setStyleSheet(label_style) balance_layout.addWidget(balance_type_label, 0, 0) self.filter_balance_type = QComboBox() self.filter_balance_type.addItems(["الكل", "موجب", "سالب", "صفر", "نطاق مخصص"]) self.filter_balance_type.setStyleSheet(""" QComboBox { border: 2px solid #e2e8f0; border-radius: 8px; padding: 10px 12px; font-size: 14px; background: white; color: #374151; min-width: 150px; } QComboBox:focus { border: 2px solid #f59e0b; background: #fffbeb; } QComboBox:hover { border: 2px solid #fbbf24; } QComboBox::drop-down { border: none; width: 30px; } QComboBox::down-arrow { image: none; border: 2px solid #f59e0b; width: 8px; height: 8px; border-top: none; border-left: none; transform: rotate(45deg); margin-right: 8px; } QComboBox QAbstractItemView { border: 2px solid #f59e0b; border-radius: 8px; background: white; selection-background-color: #fbbf24; selection-color: white; font-size: 14px; padding: 5px; } balance_layout.addWidget(self.filter_balance_type, 0, 1) # الحد الأدنى للرصيد min_label = QLabel(" الحد الأدنى:") min_label.setStyleSheet(label_style) balance_layout.addWidget(min_label, 1, 0) self.filter_balance_min = QSpinBox() self.filter_balance_min.setRange(-999999, 999999) self.filter_balance_min.setValue(0) self.filter_balance_min.setStyleSheet(""" QSpinBox { border: 2px solid #e2e8f0; border-radius: 8px; padding: 10px 12px; font-size: 14px; background: white; color: #374151; } QSpinBox:focus { border: 2px solid #f59e0b; background: #fffbeb; } QSpinBox:hover { border: 2px solid #fbbf24; } balance_layout.addWidget(self.filter_balance_min, 1, 1) # الحد الأقصى للرصيد max_label = QLabel(" الحد الأقصى:") max_label.setStyleSheet(label_style) balance_layout.addWidget(max_label, 2, 0) self.filter_balance_max = QSpinBox() self.filter_balance_max.setRange(-999999, 999999) self.filter_balance_max.setValue(999999) self.filter_balance_max.setStyleSheet(""" QSpinBox { border: 2px solid #e2e8f0; border-radius: 8px; padding: 10px 12px; font-size: 14px; background: white; color: #374151; } QSpinBox:focus { border: 2px solid #f59e0b; background: #fffbeb; } QSpinBox:hover { border: 2px solid #fbbf24; } balance_layout.addWidget(self.filter_balance_max, 2, 1) balance_group.setLayout(balance_layout) scroll_layout.addWidget(balance_group) # مجموعة فلاتر التاريخ المتطورة date_group = QGroupBox(" فلاتر التاريخ") date_group.setStyleSheet(""" QGroupBox { font-weight: bold; font-size: 16px; color: #374151; border: 2px solid #06b6d4; border-radius: 12px; margin-top: 10px; padding-top: 15px; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 rgba(255, 255, 255, 0.9), stop:1 rgba(6, 182, 212, 0.1)); } QGroupBox::title { subcontrol-origin: margin; left: 10px; padding: 0 8px 0 8px; color: #06b6d4; background: white; border-radius: 6px; } date_layout = QGridLayout() date_layout.setSpacing(12) # من تاريخ from_label = QLabel(" من تاريخ:") from_label.setStyleSheet(label_style) date_layout.addWidget(from_label, 0, 0) self.filter_date_from = QDateEdit() self.filter_date_from.setDate(QDate.currentDate().addMonths(-1)) self.filter_date_from.setCalendarPopup(True) self.filter_date_from.setStyleSheet(""" QDateEdit { border: 2px solid #e2e8f0; border-radius: 8px; padding: 10px 12px; font-size: 14px; background: white; color: #374151; } QDateEdit:focus { border: 2px solid #06b6d4; background: #ecfeff; } QDateEdit:hover { border: 2px solid #22d3ee; } date_layout.addWidget(self.filter_date_from, 0, 1) # إلى تاريخ to_label = QLabel(" إلى تاريخ:") to_label.setStyleSheet(label_style) date_layout.addWidget(to_label, 1, 0) self.filter_date_to = QDateEdit() self.filter_date_to.setDate(QDate.currentDate()) self.filter_date_to.setCalendarPopup(True) self.filter_date_to.setStyleSheet(""" QDateEdit { border: 2px solid #e2e8f0; border-radius: 8px; padding: 10px 12px; font-size: 14px; background: white; color: #374151; } QDateEdit:focus { border: 2px solid #06b6d4; background: #ecfeff; } QDateEdit:hover { border: 2px solid #22d3ee; } date_layout.addWidget(self.filter_date_to, 1, 1) date_group.setLayout(date_layout) scroll_layout.addWidget(date_group) scroll_area.setWidget(scroll_widget) layout.addWidget(scroll_area) # أزرار الحوار المتطورة buttons_frame = QFrame() buttons_frame.setStyleSheet(""" QFrame { background: rgba(255, 255, 255, 0.8); border-radius: 12px; padding: 10px; } buttons_layout = QHBoxLayout(buttons_frame) buttons_layout.setSpacing(10) # زر تطبيق الفلاتر apply_btn = QPushButton(" تطبيق الفلاتر") apply_btn.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #8b5cf6, stop:1 #7c3aed); color: white; border: none; border-radius: 10px; padding: 12px 25px; font-size: 14px; font-weight: 600; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7c3aed, stop:1 #6d28d9); transform: translateY(-2px); } QPushButton:pressed { transform: translateY(0px); } apply_btn.clicked.connect(lambda: self.apply_advanced_filters(dialog)) buttons_layout.addWidget(apply_btn) # زر مسح الفلاتر clear_btn = QPushButton(" مسح الفلاتر") clear_btn.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ef4444, stop:1 #dc2626); color: white; border: none; border-radius: 10px; padding: 12px 20px; font-size: 14px; font-weight: 600; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #dc2626, stop:1 #b91c1c); transform: translateY(-2px); } QPushButton:pressed { transform: translateY(0px); } clear_btn.clicked.connect(lambda: self.clear_advanced_filters(dialog)) buttons_layout.addWidget(clear_btn) buttons_layout.addStretch() # زر إلغاء cancel_btn = QPushButton(" إلغاء") cancel_btn.setStyleSheet(""" QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #6b7280, stop:1 #4b5563); color: white; border: none; border-radius: 10px; padding: 12px 20px; font-size: 14px; font-weight: 600; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4b5563, stop:1 #374151); transform: translateY(-2px); } QPushButton:pressed { transform: translateY(0px); } cancel_btn.clicked.connect(dialog.reject) buttons_layout.addWidget(cancel_btn) layout.addWidget(buttons_frame) dialog.setLayout(layout) # إضافة تأثيرات التحريك البديلة def add_smooth_animation(): """إضافة تأثيرات تحريك سلسة بدون QPropertyAnimation""" try: # تأثير الظهور التدريجي dialog.setWindowOpacity(0.0) dialog.show() # استخدام QTimer لتحريك الشفافية opacity_timer = QTimer() opacity_step = 0.0 def increase_opacity(): nonlocal opacity_step opacity_step += 0.1 if opacity_step <= 1.0: dialog.setWindowOpacity(opacity_step) else: opacity_timer.stop() opacity_timer.timeout.connect(increase_opacity) opacity_timer.start(30) # كل 30 مللي ثانية print(" تم تطبيق تأثيرات التحريك البديلة للفلاتر المتقدمة") except Exception as e: print(f" خطأ في تأثيرات التحريك: {e}") dialog.setWindowOpacity(1.0) dialog.show() # تطبيق التحريك add_smooth_animation() dialog.exec_() except Exception as e: show_error_message("خطأ", f"حدث خطأ في الفلاتر المتقدمة: {str(e)}") def apply_advanced_filters(self, dialog): try: # بناء الاستعلام query = self.session.query(Client) # فلتر الاسم if self.filter_name.text().strip(): query = query.filter(Client.name.like(f"%{self.filter_name.text().strip()}%")) # فلتر الهاتف if self.filter_phone.text().strip(): query = query.filter(Client.phone.like(f"%{self.filter_phone.text().strip()}%")) # فلتر الإيميل if self.filter_email.text().strip(): query = query.filter(Client.email.like(f"%{self.filter_email.text().strip()}%")) # فلتر العنوان if self.filter_address.text().strip(): query = query.filter(Client.address.like(f"%{self.filter_address.text().strip()}%")) # فلتر الرصيد balance_type = self.filter_balance_type.currentText() if balance_type == "موجب": query = query.filter(Client.balance > 0) elif balance_type == "سالب": query = query.filter(Client.balance < 0) elif balance_type == "صفر": query = query.filter(Client.balance == 0) elif balance_type == "نطاق مخصص": min_balance = self.filter_balance_min.value() max_balance = self.filter_balance_max.value() query = query.filter(Client.balance >= min_balance, Client.balance <= max_balance) # تنفيذ الاستعلام filtered_clients = query.all() # تحديث الجدول مع الحفاظ على التنسيق والألوان self.smart_update_table_data_with_colors(filtered_clients) dialog.accept() show_info_message("تم", f"تم تطبيق الفلاتر - تم العثور على {len(filtered_clients)} عميل") except Exception as e: show_error_message("خطأ", f"حدث خطأ في تطبيق الفلاتر: {str(e)}") def clear_advanced_filters(self, dialog): """مسح جميع الفلاتر""" try: self.filter_name.clear() self.filter_phone.clear() self.filter_email.clear() self.filter_address.clear() self.filter_balance_type.setCurrentIndex(0) self.filter_balance_min.setValue(0) self.filter_balance_max.setValue(999999) # إعادة تحميل جميع العملاء self.refresh_data() show_info_message("تم", "تم مسح جميع الفلاتر وإعادة تحميل البيانات") except Exception as e: show_error_message("خطأ", f"حدث خطأ في مسح الفلاتر: {str(e)}") # ==================== دوال الاتصال السريع ==================== def make_direct_call(self): try: selected_items = self.clients_table.selectedItems() if not selected_items: show_error_message("تحذير", "يرجى تحديد عميل أولاً") return row = selected_items[0].row() phone_item = self.clients_table.item(row, 2) # عمود الهاتف client_name_item = self.clients_table.item(row, 1) # عمود الاسم if not phone_item or not phone_item.text().strip(): show_error_message("خطأ", "لا يوجد رقم هاتف لهذا العميل") return phone = phone_item.text().strip() client_name = client_name_item.text() if client_name_item else "غير محدد" # محاولة فتح تطبيق الهاتف if platform.system() == "Windows": # في Windows، يمكن استخدام tel: protocol subprocess.run(f'start tel:{phone}', shell=True) else: show_info_message("اتصال مباشر", f"سيتم الاتصال بـ {client_name}\nالرقم: {phone}") except Exception as e: show_error_message("خطأ", f"حدث خطأ في الاتصال المباشر: {str(e)}") def make_whatsapp_call(self): """إجراء اتصال واتساب""" try: selected_items = self.clients_table.selectedItems() if not selected_items: show_error_message("تحذير", "يرجى تحديد عميل أولاً") return row = selected_items[0].row() phone_item = self.clients_table.item(row, 2) client_name_item = self.clients_table.item(row, 1) if not phone_item or not phone_item.text().strip(): show_error_message("خطأ", "لا يوجد رقم هاتف لهذا العميل") return phone = phone_item.text().strip() client_name = client_name_item.text() if client_name_item else "غير محدد" # فتح واتساب whatsapp_url = f"https://wa.me/{phone.replace('+', '').replace(' ', '').replace('-', '')}" webbrowser.open(whatsapp_url) except Exception as e: show_error_message("خطأ", f"حدث خطأ في اتصال واتساب: {str(e)}") def make_skype_call(self): try: selected_items = self.clients_table.selectedItems() if not selected_items: show_error_message("تحذير", "يرجى تحديد عميل أولاً") return row = selected_items[0].row() phone_item = self.clients_table.item(row, 2) client_name_item = self.clients_table.item(row, 1) if not phone_item or not phone_item.text().strip(): show_error_message("خطأ", "لا يوجد رقم هاتف لهذا العميل") return phone = phone_item.text().strip() client_name = client_name_item.text() if client_name_item else "غير محدد" show_info_message("مكالمة فيديو", f"سيتم فتح سكايب للاتصال بـ {client_name}\nالرقم: {phone}") except Exception as e: show_error_message("خطأ", f"حدث خطأ في مكالمة سكايب: {str(e)}") def copy_phone_number(self): """نسخ رقم الهاتف""" try: selected_items = self.clients_table.selectedItems() if not selected_items: show_error_message("تحذير", "يرجى تحديد عميل أولاً") return row = selected_items[0].row() phone_item = self.clients_table.item(row, 2) if not phone_item or not phone_item.text().strip(): show_error_message("خطأ", "لا يوجد رقم هاتف لهذا العميل") return phone = phone_item.text().strip() # نسخ إلى الحافظة clipboard = QApplication.clipboard() clipboard.setText(phone) show_info_message("تم", f"تم نسخ الرقم: {phone}") except Exception as e: show_error_message("خطأ", f"حدث خطأ في نسخ الرقم: {str(e)}") def save_to_contacts(self): try: selected_items = self.clients_table.selectedItems() if not selected_items: show_error_message("تحذير", "يرجى تحديد عميل أولاً") return row = selected_items[0].row() phone_item = self.clients_table.item(row, 2) client_name_item = self.clients_table.item(row, 1) if not phone_item or not phone_item.text().strip(): show_error_message("خطأ", "لا يوجد رقم هاتف لهذا العميل") return phone = phone_item.text().strip() client_name = client_name_item.text() if client_name_item else "غير محدد" show_info_message("حفظ جهة اتصال", f"سيتم حفظ:\nالاسم: {client_name}\nالرقم: {phone}\n(هذه الميزة قيد التطوير)") except Exception as e: show_error_message("خطأ", f"حدث خطأ في حفظ جهة الاتصال: {str(e)}") def show_call_history(self): """عرض سجل المكالمات""" try: show_info_message("سجل المكالمات", "سيتم عرض سجل المكالمات هنا\n(هذه الميزة قيد التطوير)") except Exception as e: show_error_message("خطأ", f"حدث خطأ في عرض سجل المكالمات: {str(e)}") # ==================== دوال الأزرار الجديدة ==================== def manage_attachments(self): try: selected_client_id = self.get_selected_client_id() if not selected_client_id: QMessageBox.warning(self, "تحذير", "يرجى تحديد عميل أولاً") return # الحصول على بيانات العميل client = self.session.query(Client).filter_by(id=selected_client_id).first() if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return # إنشاء نافذة إدارة المرفقات المتطورة dialog = ClientAttachmentsDialog(self, client, self.session) dialog.exec_() except Exception as e: print(f" خطأ في إدارة المرفقات: {str(e)}") show_error_message("خطأ", f"حدث خطأ أثناء فتح نافذة المرفقات: {str(e)}") def show_notifications(self): """عرض الإشعارات المعلقة""" try: dialog = QDialog(self) dialog.setWindowTitle(" الإشعارات المعلقة") dialog.setMinimumSize(500, 350) layout = QVBoxLayout() # عنوان title_label = QLabel(" الإشعارات والتنبيهات") title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2d3748; margin: 10px;") layout.addWidget(title_label) # قائمة الإشعارات notifications_list = QListWidget() notifications_list.addItem(" تذكير: دفعة مستحقة للعميل أحمد محمد") notifications_list.addItem(" تنبيه: عميل جديد يحتاج متابعة") notifications_list.addItem(" تذكير: مراجعة رصيد العميل سارة أحمد") notifications_list.addItem(" عيد ميلاد: العميل محمد علي غداً") layout.addWidget(notifications_list) # أزرار الإدارة buttons_layout = QHBoxLayout() mark_read_btn = QPushButton(" تم القراءة") self.style_advanced_button(mark_read_btn, 'success') delete_notification_btn = QPushButton(" حذف") self.style_advanced_button(delete_notification_btn, 'danger') buttons_layout.addWidget(mark_read_btn) buttons_layout.addWidget(delete_notification_btn) buttons_layout.addStretch() layout.addLayout(buttons_layout) dialog.setLayout(layout) dialog.exec_() except Exception as e: print(f" خطأ في عرض الإشعارات: {str(e)}") def setup_new_alert(self): try: QMessageBox.information(self, "تنبيه", " سيتم فتح نافذة إعداد التنبيهات قريباً") except Exception as e: print(f" خطأ في إعداد التنبيه: {str(e)}") def view_active_alerts(self): """عرض التنبيهات النشطة""" try: QMessageBox.information(self, "تنبيه", " عرض التنبيهات النشطة") except Exception as e: print(f" خطأ في عرض التنبيهات: {str(e)}") def setup_payment_reminder(self): try: QMessageBox.information(self, "تنبيه", " إعداد تذكير دفع للعميل") except Exception as e: print(f" خطأ في إعداد تذكير الدفع: {str(e)}") def setup_birthday_alert(self): """إعداد تنبيه عيد ميلاد""" try: QMessageBox.information(self, "تنبيه", " إعداد تنبيه عيد ميلاد") except Exception as e: print(f" خطأ في إعداد تنبيه عيد الميلاد: {str(e)}") def setup_follow_up_alert(self): try: QMessageBox.information(self, "تنبيه", " إعداد تنبيه متابعة العميل") except Exception as e: print(f" خطأ في إعداد تنبيه المتابعة: {str(e)}") # ==================== نافذة عرض تفاصيل العميل ==================== class ClientDetailsDialog(QDialog): """نافذة عرض تفاصيل العميل المطورة""" def __init__(self, parent=None, client=None): super().__init__(parent) self.client = client self.session = parent.session if parent else None self.init_ui() def init_ui(self): # إزالة شريط العنوان القديم تماماً أولاً self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint) self.setAttribute(Qt.WA_TranslucentBackground, False) # إعداد النافذة الأساسي self.setWindowTitle("") # إزالة العنوان القديم self.setModal(True) # تحسين حجم النافذة - حل مشكلة setGeometry نهائياً # إزالة جميع قيود الحجم لتجنب تضارب setGeometry # لا نعين أي حد أدنى أو أقصى أو حجم ثابت # تعيين خلفية النافذة مع إطار مربع بسيط بألوان شريط العنوان self.setStyleSheet(""" QDialog { background: #f5f5f5; border: 4px solid #4facfe; border-radius: 0px; } # إنشاء التخطيط الرئيسي بدون هوامش main_layout = QVBoxLayout() main_layout.setContentsMargins(0, 0, 0, 0) main_layout.setSpacing(0) # إنشاء شريط العنوان المخصص في أعلى النافذة مباشرة self.create_custom_title_bar(main_layout) # إنشاء المحتوى الرئيسي self.create_main_content(main_layout) # إنشاء أزرار التحكم self.create_control_buttons(main_layout) self.setLayout(main_layout) # متغيرات لسحب النافذة self.dragging = False self.drag_position = None # تعيين حجم مبدئي بسيط بدون قيود self.resize(800, 600) # توسيط النافذة بطريقة آمنة self.center_on_screen_safe() def center_on_screen_safe(self): """توسيط النافذة على الشاشة بطريقة بسيطة وآمنة""" try: # تأخير بسيط للسماح للنافذة بالتهيئة الكاملة QTimer.singleShot(50, self._perform_simple_centering) except Exception as e: print(f"خطأ في توسيط النافذة: {str(e)}") def _perform_simple_centering(self): try: # الحصول على معلومات الشاشة screen = QApplication.desktop().screenGeometry() # استخدام حجم آمن ثابت safe_width = 800 safe_height = 600 # حساب الموضع المركزي x = (screen.width() - safe_width) // 2 y = (screen.height() - safe_height) // 2 # التأكد من أن النافذة داخل حدود الشاشة x = max(0, min(x, screen.width() - safe_width)) y = max(0, min(y, screen.height() - safe_height)) # تحريك النافذة للموضع المحسوب فقط (بدون تغيير الحجم) self.move(x, y) print(f" تم توسيط النافذة بنجاح في الموضع ({x}, {y})") except Exception as e: print(f"خطأ في تنفيذ التوسيط البسيط: {str(e)}") def create_custom_title_bar(self, layout): """إنشاء شريط عنوان مخصص""" title_frame = QFrame() QFrame { background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #1a1a2e, stop:0.1 #16213e, stop:0.15 #0f3460, stop:0.25 #533483, stop:0.35 #7209b7, stop:0.45 #a663cc, stop:0.55 #4facfe, stop:0.65 #00f2fe, stop:0.75 #43e97b, stop:0.85 #38f9d7, stop:0.95 #667eea, stop:1 #764ba2); border-radius: 0px; padding: 15px; margin: 0px; } """) title_frame.setFixedHeight(40) # تقليل الارتفاع title_layout = QHBoxLayout(title_frame) title_layout.setContentsMargins(15, 10, 15, 10) # عنوان النافذة title_label = QLabel(f" تفاصيل العميل: {self.client.name if self.client else 'غير محدد'}") QLabel { color: white; font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; font-weight: bold; background: transparent; border: none; } """) title_layout.addWidget(title_label) title_layout.addStretch() # أزرار التحكم في النافذة (في أقصى اليمين) minimize_btn = QPushButton("") QPushButton { background: rgba(255, 255, 255, 0.2); color: white; border: none; border-radius: 15px; font-size: 14px; font-weight: bold; min-width: 30px; max-width: 30px; min-height: 30px; max-height: 30px; } QPushButton:hover { background: rgba(255, 255, 255, 0.4); } QPushButton:pressed { background: rgba(255, 255, 255, 0.6); } """) minimize_btn.clicked.connect(self.showMinimized) title_layout.addWidget(minimize_btn) close_btn = QPushButton("") QPushButton { background: rgba(255, 255, 255, 0.2); color: white; border: none; border-radius: 15px; font-size: 16px; font-weight: bold; min-width: 30px; max-width: 30px; min-height: 30px; max-height: 30px; } QPushButton:hover { background: rgba(255, 0, 0, 0.7); } QPushButton:pressed { background: rgba(255, 0, 0, 0.9); } """) close_btn.clicked.connect(self.close) title_layout.addWidget(close_btn) layout.addWidget(title_frame) # حفظ مرجع لشريط العنوان لتمكين السحب self.title_frame = title_frame def create_main_content(self, layout): # إنشاء منطقة المحتوى مع خلفية بيضاء بسيطة content_widget = QWidget() content_widget.setStyleSheet(""" QWidget { background: #ffffff; border: none; padding: 20px; } content_layout = QVBoxLayout(content_widget) content_layout.setContentsMargins(25, 25, 25, 25) content_layout.setSpacing(20) # معلومات العميل الأساسية self.create_basic_info_section(content_layout) # معلومات الاتصال self.create_contact_info_section(content_layout) # المعلومات المالية self.create_financial_info_section(content_layout) # الملاحظات self.create_notes_section(content_layout) layout.addWidget(content_widget) def create_basic_info_section(self, layout): """إنشاء قسم المعلومات الأساسية""" # عنوان القسم section_title = QLabel(" المعلومات الأساسية") QLabel { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; font-weight: bold; color: #2d3748; padding: 10px 0px; border-bottom: 2px solid #4facfe; margin-bottom: 15px; } """) layout.addWidget(section_title) # تخطيط المعلومات info_layout = QFormLayout() info_layout.setSpacing(15) info_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter) # نمط التسميات QLabel { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; font-weight: bold; color: #4a5568; min-width: 120px; } """ # نمط القيم QLabel { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; color: #2d3748; background: #f7fafc; padding: 10px 15px; border: 1px solid #e2e8f0; border-radius: 6px; min-height: 20px; } """ # رقم العميل id_label = QLabel(" رقم العميل:") id_label.setStyleSheet(label_style) id_value = QLabel(str(self.client.id) if self.client else "غير متوفر") id_value.setStyleSheet(value_style) info_layout.addRow(id_label, id_value) # اسم العميل name_label = QLabel(" اسم العميل:") name_label.setStyleSheet(label_style) name_value = QLabel(self.client.name if self.client and self.client.name else "غير متوفر") name_value.setStyleSheet(value_style) info_layout.addRow(name_label, name_value) # تاريخ الإنشاء date_label = QLabel(" تاريخ الإنشاء:") date_label.setStyleSheet(label_style) date_value = QLabel( self.client.created_at.strftime('%Y-%m-%d %H:%M') if self.client and hasattr(self.client, 'created_at') and self.client.created_at else "غير متوفر" ) date_value.setStyleSheet(value_style) info_layout.addRow(date_label, date_value) layout.addLayout(info_layout) def create_contact_info_section(self, layout): # عنوان القسم section_title = QLabel(" معلومات الاتصال") section_title.setStyleSheet(""" QLabel { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; font-weight: bold; color: #2d3748; padding: 10px 0px; border-bottom: 2px solid #4facfe; margin-bottom: 15px; } layout.addWidget(section_title) # تخطيط المعلومات contact_layout = QFormLayout() contact_layout.setSpacing(15) contact_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter) # نمط التسميات label_style = """ QLabel { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; font-weight: bold; color: #4a5568; min-width: 120px; } # نمط القيم value_style = """ QLabel { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; color: #2d3748; background: #f7fafc; padding: 10px 15px; border: 1px solid #e2e8f0; border-radius: 6px; min-height: 20px; } # رقم الهاتف الرئيسي phone_label = QLabel(" رقم الهاتف:") phone_label.setStyleSheet(label_style) phone_value = QLabel(self.client.phone if self.client and self.client.phone else "غير متوفر") if phone_value.text() == "غير متوفر": phone_value.setStyleSheet(value_style.replace("#2d3748", "#DC2626")) # أحمر للغير متوفر else: phone_value.setStyleSheet(value_style) contact_layout.addRow(phone_label, phone_value) # البريد الإلكتروني email_label = QLabel(" البريد الإلكتروني:") email_label.setStyleSheet(label_style) email_value = QLabel(self.client.email if self.client and self.client.email else "غير متوفر") if email_value.text() == "غير متوفر": email_value.setStyleSheet(value_style.replace("#2d3748", "#DC2626")) # أحمر للغير متوفر else: email_value.setStyleSheet(value_style) contact_layout.addRow(email_label, email_value) # العنوان address_label = QLabel(" العنوان:") address_label.setStyleSheet(label_style) address_value = QLabel(self.client.address if self.client and self.client.address else "غير متوفر") if address_value.text() == "غير متوفر": address_value.setStyleSheet(value_style.replace("#2d3748", "#DC2626")) # أحمر للغير متوفر else: address_value.setStyleSheet(value_style) contact_layout.addRow(address_label, address_value) layout.addLayout(contact_layout) def create_financial_info_section(self, layout): """إنشاء قسم المعلومات المالية""" # عنوان القسم section_title = QLabel(" المعلومات المالية") QLabel { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; font-weight: bold; color: #2d3748; padding: 10px 0px; border-bottom: 2px solid #4facfe; margin-bottom: 15px; } """) layout.addWidget(section_title) # تخطيط المعلومات financial_layout = QFormLayout() financial_layout.setSpacing(15) financial_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter) # نمط التسميات QLabel { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; font-weight: bold; color: #4a5568; min-width: 120px; } """ # الرصيد الحالي balance_label = QLabel(" الرصيد الحالي:") balance_label.setStyleSheet(label_style) balance_amount = self.client.balance if self.client else 0 balance_text = f"{balance_amount:,.0f}" # تحديد لون الرصيد حسب القيمة if balance_amount > 0: balance_color = "#155724" # أخضر للرصيد الموجب balance_bg = "#d1ecf1" elif balance_amount < 0: balance_color = "#721c24" # أحمر للرصيد السالب balance_bg = "#f8d7da" else: balance_color = "#495057" # رمادي للرصيد صفر balance_bg = "#e9ecef" balance_value = QLabel(balance_text) QLabel {{ font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; font-weight: bold; color: {balance_color}; background: {balance_bg}; padding: 12px 15px; border: 2px solid {balance_color}; border-radius: 8px; min-height: 20px; }} """) financial_layout.addRow(balance_label, balance_value) # حالة العميل status_label = QLabel(" حالة العميل:") status_label.setStyleSheet(label_style) if balance_amount > 0: status_text = "🟢 نشط" status_color = "#155724" elif balance_amount == 0: status_text = " عادي" status_color = "#495057" else: status_text = " مدين" status_color = "#721c24" status_value = QLabel(status_text) QLabel {{ font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; font-weight: bold; color: {status_color}; background: #f7fafc; padding: 10px 15px; border: 1px solid #e2e8f0; border-radius: 6px; min-height: 20px; }} """) financial_layout.addRow(status_label, status_value) layout.addLayout(financial_layout) def create_notes_section(self, layout): # عنوان القسم section_title = QLabel(" الملاحظات") section_title.setStyleSheet(""" QLabel { font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 16px; font-weight: bold; color: #2d3748; padding: 10px 0px; border-bottom: 2px solid #4facfe; margin-bottom: 15px; } layout.addWidget(section_title) # منطقة النص notes_text = self.client.notes if self.client and self.client.notes else "لا توجد ملاحظات" notes_display = QLabel(notes_text) if notes_text == "لا توجد ملاحظات": notes_color = "#DC2626" # أحمر للغير متوفر else: notes_color = "#2d3748" notes_display.setStyleSheet(f""" QLabel {{ font-family: 'Segoe UI', 'Arial', sans-serif; font-size: 14px; color: {notes_color}; background: #f7fafc; padding: 15px; border: 1px solid #e2e8f0; border-radius: 8px; min-height: 60px; }} notes_display.setWordWrap(True) notes_display.setAlignment(Qt.AlignTop | Qt.AlignRight) layout.addWidget(notes_display) def create_control_buttons(self, layout): """إنشاء أزرار التحكم""" # إطار الأزرار buttons_frame = QFrame() QFrame { background: #ffffff; border: none; padding: 15px; } """) buttons_layout = QHBoxLayout(buttons_frame) buttons_layout.setContentsMargins(0, 15, 0, 15) buttons_layout.setSpacing(15) # زر تعديل العميل edit_btn = QPushButton(" تعديل العميل") QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #3b82f6, stop:1 #1d4ed8); color: white; border: none; border-radius: 8px; padding: 12px 25px; font-size: 14px; font-weight: bold; min-width: 150px; min-height: 45px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1d4ed8, stop:1 #1e40af); } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e40af, stop:1 #1d4ed8); } """) edit_btn.clicked.connect(self.edit_client) buttons_layout.addWidget(edit_btn) # زر طباعة التفاصيل print_btn = QPushButton(" طباعة") QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #10b981, stop:1 #059669); color: white; border: none; border-radius: 8px; padding: 12px 25px; font-size: 14px; font-weight: bold; min-width: 150px; min-height: 45px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #059669, stop:1 #047857); } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #047857, stop:1 #059669); } """) print_btn.clicked.connect(self.print_details) buttons_layout.addWidget(print_btn) buttons_layout.addStretch() # زر إغلاق close_btn = QPushButton(" إغلاق") QPushButton { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #6b7280, stop:1 #4b5563); color: white; border: none; border-radius: 8px; padding: 12px 25px; font-size: 14px; font-weight: bold; min-width: 150px; min-height: 45px; } QPushButton:hover { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4b5563, stop:1 #374151); } QPushButton:pressed { background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #374151, stop:1 #4b5563); } """) close_btn.clicked.connect(self.close) buttons_layout.addWidget(close_btn) layout.addWidget(buttons_frame) def edit_client(self): try: if self.client and self.session: # إغلاق نافذة التفاصيل self.close() # فتح نافذة التعديل dialog = ClientDialog(self.session, self.client) if dialog.exec_() == QDialog.Accepted: show_info_message("تم", "تم تحديث بيانات العميل بنجاح") # تحديث البيانات في النافذة الأصلية if self.parent(): self.parent().refresh_data() except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء تعديل العميل: {str(e)}") def print_details(self): """طباعة تفاصيل العميل""" try: show_info_message("طباعة", "سيتم فتح نافذة الطباعة قريباً") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء الطباعة: {str(e)}") def mousePressEvent(self, event): if event.button() == Qt.LeftButton: # التحقق من أن الضغط كان على شريط العنوان if hasattr(self, 'title_frame'): title_rect = self.title_frame.geometry() if title_rect.contains(event.pos()): self.dragging = True self.drag_position = event.globalPos() - self.frameGeometry().topLeft() event.accept() return super().mousePressEvent(event) def mouseMoveEvent(self, event): """التعامل مع حركة الماوس للسحب""" if event.buttons() == Qt.LeftButton and self.dragging and self.drag_position: self.move(event.globalPos() - self.drag_position) event.accept() return super().mouseMoveEvent(event) def mouseReleaseEvent(self, event): if event.button() == Qt.LeftButton: self.dragging = False self.drag_position = None super().mouseReleaseEvent(event) # ==================== دوال الاتصال السريع ==================== def make_direct_call(self): """إجراء اتصال مباشر بالعميل المحدد""" try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من الجدول أولاً") return # الحصول على رقم الهاتف phone_item = self.clients_table.item(selected_row, 2) if not phone_item or not phone_item.text() or phone_item.text() == "غير متوفر": show_error_message("خطأ", "لا يوجد رقم هاتف متاح لهذا العميل") return phone = phone_item.text().strip() client_name_item = self.clients_table.item(selected_row, 1) client_name = client_name_item.text() if client_name_item else "غير محدد" # فتح تطبيق الهاتف (Windows) if platform.system() == "Windows": # محاولة فتح تطبيق الهاتف في Windows try: subprocess.run(f'start tel:{phone}', shell=True) show_info_message("اتصال", f"تم فتح تطبيق الهاتف للاتصال بـ {client_name}\nالرقم: {phone}") except: show_info_message("رقم الهاتف", f"رقم هاتف {client_name}:\n{phone}\n\nيمكنك نسخ الرقم والاتصال يدوياً") else: show_info_message("رقم الهاتف", f"رقم هاتف {client_name}:\n{phone}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء محاولة الاتصال: {str(e)}") def make_whatsapp_call(self): try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من الجدول أولاً") return # الحصول على رقم الهاتف phone_item = self.clients_table.item(selected_row, 2) if not phone_item or not phone_item.text() or phone_item.text() == "غير متوفر": show_error_message("خطأ", "لا يوجد رقم هاتف متاح لهذا العميل") return phone = phone_item.text().strip() client_name_item = self.clients_table.item(selected_row, 1) client_name = client_name_item.text() if client_name_item else "غير محدد" # تنظيف رقم الهاتف لواتساب clean_phone = phone.replace('+', '').replace('-', '').replace(' ', '') if clean_phone.startswith('0'): clean_phone = '966' + clean_phone[1:] # تحويل للرقم السعودي الدولي # فتح واتساب whatsapp_url = f"https://wa.me/{clean_phone}" webbrowser.open(whatsapp_url) show_info_message("واتساب", f"تم فتح واتساب للتواصل مع {client_name}\nالرقم: {phone}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء فتح واتساب: {str(e)}") def copy_phone_number(self): """نسخ رقم الهاتف إلى الحافظة""" try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من الجدول أولاً") return # الحصول على رقم الهاتف phone_item = self.clients_table.item(selected_row, 2) if not phone_item or not phone_item.text() or phone_item.text() == "غير متوفر": show_error_message("خطأ", "لا يوجد رقم هاتف متاح لهذا العميل") return phone = phone_item.text().strip() client_name_item = self.clients_table.item(selected_row, 1) client_name = client_name_item.text() if client_name_item else "غير محدد" # نسخ الرقم إلى الحافظة clipboard = QApplication.clipboard() clipboard.setText(phone) show_info_message("تم النسخ", f"تم نسخ رقم هاتف {client_name} إلى الحافظة:\n{phone}") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء نسخ رقم الهاتف: {str(e)}") def save_to_contacts(self): try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من الجدول أولاً") return # الحصول على بيانات العميل client_name_item = self.clients_table.item(selected_row, 1) phone_item = self.clients_table.item(selected_row, 2) email_item = self.clients_table.item(selected_row, 3) client_name = client_name_item.text() if client_name_item else "غير محدد" phone = phone_item.text() if phone_item and phone_item.text() != "غير متوفر" else "" email = email_item.text() if email_item and email_item.text() != "غير متوفر" else "" # إنشاء ملف vCard vcard_content = f"""BEGIN:VCARD VERSION:3.0 FN:{client_name} TEL:{phone} EMAIL:{email} ORG:Smart Finish # حفظ الملف file_path, _ = QFileDialog.getSaveFileName( self, "حفظ جهة الاتصال", f"{client_name}.vcf", "vCard Files (*.vcf)" ) if file_path: with open(file_path, 'w', encoding='utf-8') as f: f.write(vcard_content) show_info_message("تم الحفظ", f"تم حفظ جهة اتصال {client_name} بنجاح") except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء حفظ جهة الاتصال: {str(e)}") def show_call_history(self): """عرض سجل المكالمات""" try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من الجدول أولاً") return client_name_item = self.clients_table.item(selected_row, 1) client_name = client_name_item.text() if client_name_item else "غير محدد" # عرض رسالة مؤقتة (يمكن تطويرها لاحقاً لحفظ سجل حقيقي) show_info_message( "سجل المكالمات", f"سجل المكالمات مع {client_name}:\n\n" " هذه الميزة قيد التطوير\n" "سيتم إضافة سجل مفصل للمكالمات قريباً" ) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء عرض سجل المكالمات: {str(e)}") # ==================== دوال عرض التفاصيل ==================== def show_client_details(self): try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من الجدول أولاً") return # الحصول على بيانات العميل client_id_item = self.clients_table.item(selected_row, 0) if not client_id_item: show_error_message("خطأ", "لا يمكن الحصول على معرف العميل") return client_id = int(client_id_item.text()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return # إنشاء نص التفاصيل الأساسية في سطر واحد details_text = f" {client.name} | {client.id} | {client.phone or 'غير متوفر'} | {'نشط' if client.balance >= 0 else 'مديون'}" show_info_message("التفاصيل الأساسية", details_text) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء عرض التفاصيل الأساسية: {str(e)}") def show_financial_details(self): """عرض التفاصيل المالية للعميل المحدد""" try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من الجدول أولاً") return # الحصول على بيانات العميل client_id_item = self.clients_table.item(selected_row, 0) if not client_id_item: show_error_message("خطأ", "لا يمكن الحصول على معرف العميل") return client_id = int(client_id_item.text()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return # حساب الإحصائيات المالية try: # البحث عن مبيعات العميل sales = self.session.query(Sale).filter_by(client_id=client.id).all() total_sales = sum(sale.total_amount for sale in sales) if sales else 0 total_paid = sum(sale.paid_amount for sale in sales) if sales else 0 remaining_amount = total_sales - total_paid # البحث عن فواتير العميل invoices = self.session.query(Invoice).filter_by(client_id=client.id).all() total_invoices = len(invoices) # إنشاء نص التفاصيل المالية balance_status = "موجب " if client.balance >= 0 else "سالب " balance_color = "أخضر" if client.balance >= 0 else "أحمر" الرصيد الحالي: {client.balance:.2f} ر.س ({balance_status}) لون الرصيد: {balance_color} إحصائيات المبيعات: • عدد المبيعات: {len(sales)} • إجمالي المبيعات: {total_sales:.2f} ر.س • المبلغ المدفوع: {total_paid:.2f} ر.س • المبلغ المتبقي: {remaining_amount:.2f} ر.س إحصائيات الفواتير: • عدد الفواتير: {total_invoices} تحليل مالي: • نسبة السداد: {(total_paid/total_sales*100):.1f}% إذا كان هناك مبيعات • حالة الحساب: {'ممتاز' if client.balance > 1000 else 'جيد' if client.balance >= 0 else 'يحتاج متابعة'} • مستوى المخاطر: {'منخفض' if client.balance >= 0 else 'متوسط' if client.balance > -1000 else 'عالي'}""" show_info_message("التفاصيل المالية", financial_text) except Exception as calc_error: # في حالة عدم وجود جداول المبيعات، عرض الرصيد فقط balance_status = "موجب " if client.balance >= 0 else "سالب " الرصيد الحالي: {client.balance:.2f} ر.س ({balance_status}) ملاحظة: لا توجد بيانات مبيعات متاحة حالياً""" show_info_message("التفاصيل المالية", financial_text) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء عرض التفاصيل المالية: {str(e)}") def show_contact_details(self): try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من الجدول أولاً") return # الحصول على بيانات العميل client_id_item = self.clients_table.item(selected_row, 0) if not client_id_item: show_error_message("خطأ", "لا يمكن الحصول على معرف العميل") return client_id = int(client_id_item.text()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return # إنشاء نص تفاصيل الاتصال contact_text = f""" تفاصيل الاتصال - {client.name}: رقم الهاتف: • {client.phone or 'غير متوفر'} • حالة الرقم: {'متاح للاتصال' if client.phone and client.phone != 'غير متوفر' else 'غير متاح'} البريد الإلكتروني: • {client.email or 'غير متوفر'} • حالة الإيميل: {'متاح للمراسلة' if client.email and client.email != 'غير متوفر' else 'غير متاح'} العنوان: • {client.address or 'غير متوفر'} # إضافة طرق التواصل المتاحة if client.phone and client.phone != 'غير متوفر': contact_text += f""" اتصال مباشر واتساب else: contact_text += f""" if client.email and client.email != 'غير متوفر': contact_text += f""" else: contact_text += f""" contact_text += f""" ملاحظات الاتصال: • {client.notes or 'لا توجد ملاحظات خاصة بالاتصال'} ⏰ أفضل أوقات الاتصال: • الصباح: 9:00 - 12:00 show_info_message("تفاصيل الاتصال", contact_text) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء عرض تفاصيل الاتصال: {str(e)}") def show_transaction_history(self): """عرض سجل المعاملات للعميل المحدد""" try: selected_row = self.clients_table.currentRow() if selected_row < 0: show_error_message("خطأ", "الرجاء اختيار عميل من الجدول أولاً") return # الحصول على بيانات العميل client_id_item = self.clients_table.item(selected_row, 0) if not client_id_item: show_error_message("خطأ", "لا يمكن الحصول على معرف العميل") return client_id = int(client_id_item.text()) client = self.session.query(Client).get(client_id) if not client: show_error_message("خطأ", "لم يتم العثور على العميل") return # إنشاء نص سجل المعاملات الرصيد الحالي: {client.balance:.2f} ر.س إحصائيات عامة: • تاريخ إنشاء الحساب: {client.created_at.strftime('%Y-%m-%d') if client.created_at else 'غير محدد'} • عدد أيام العضوية: {(datetime.now() - client.created_at).days if client.created_at else 'غير محدد'} سجل المعاملات:""" try: # البحث عن مبيعات العميل sales = self.session.query(Sale).filter_by(client_id=client.id).order_by(Sale.created_at.desc()).all() if sales: المبيعات (آخر 5 معاملات):""" for i, sale in enumerate(sales[:5], 1): sale_date = sale.created_at.strftime('%Y-%m-%d') if sale.created_at else 'غير محدد' {i}. معاملة بيع • التاريخ: {sale_date} • المبلغ الإجمالي: {sale.total_amount:.2f} ر.س • المبلغ المدفوع: {sale.paid_amount:.2f} ر.س • المتبقي: {(sale.total_amount - sale.paid_amount):.2f} ر.س""" else: • لا توجد مبيعات مسجلة""" # البحث عن فواتير العميل invoices = self.session.query(Invoice).filter_by(client_id=client.id).order_by(Invoice.created_at.desc()).all() if invoices: الفواتير (آخر 3 فواتير):""" for i, invoice in enumerate(invoices[:3], 1): invoice_date = invoice.created_at.strftime('%Y-%m-%d') if invoice.created_at else 'غير محدد' {i}. فاتورة رقم: {invoice.invoice_number or 'غير محدد'} • التاريخ: {invoice_date} • المبلغ: {invoice.total_amount:.2f} ر.س • الحالة: {invoice.status or 'غير محدد'}""" else: الفواتير: • لا توجد فواتير مسجلة""" except Exception as db_error: ملاحظة: لا توجد بيانات معاملات متاحة حالياً (قد تكون جداول المبيعات والفواتير غير متاحة)""" تحليل النشاط: • مستوى النشاط: {'نشط' if client.balance != 0 else 'خامل'} • آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}""" show_info_message("سجل المعاملات", history_text) except Exception as e: show_error_message("خطأ", f"حدث خطأ أثناء عرض سجل المعاملات: {str(e)}") class ClientAttachmentsDialog(QDialog): def __init__(self, parent, client, session): super().__init__(parent) self.client = client self.session = session self.attachments = [] self.setWindowTitle(f"إدارة مرفقات العميل: {client.name}") self.setModal(True) self.resize(800, 600) self.init_ui() self.load_attachments() def init_ui(self): """إعداد واجهة المستخدم""" layout = QVBoxLayout() # عنوان النافذة title_label = QLabel(f" إدارة مرفقات العميل: {self.client.name}") QLabel { font-size: 16px; font-weight: bold; color: #2c3e50; padding: 10px; background: #ecf0f1; border-radius: 5px; margin-bottom: 10px; } """) layout.addWidget(title_label) # قائمة المرفقات self.attachments_list = QListWidget() QListWidget { border: 2px solid #bdc3c7; border-radius: 5px; background: white; font-size: 12px; } QListWidget::item { padding: 8px; border-bottom: 1px solid #ecf0f1; } QListWidget::item:selected { background: #3498db; color: white; } """) layout.addWidget(self.attachments_list) # أزرار الإجراءات buttons_layout = QHBoxLayout() add_file_btn = QPushButton(" إضافة ملف") add_file_btn.clicked.connect(self.add_file) QPushButton { background: #27ae60; color: white; border: none; padding: 8px 16px; border-radius: 5px; font-weight: bold; } QPushButton:hover { background: #2ecc71; } """) view_file_btn = QPushButton(" عرض") view_file_btn.clicked.connect(self.view_file) QPushButton { background: #3498db; color: white; border: none; padding: 8px 16px; border-radius: 5px; font-weight: bold; } QPushButton:hover { background: #5dade2; } """) delete_file_btn = QPushButton(" حذف") delete_file_btn.clicked.connect(self.delete_file) QPushButton { background: #e74c3c; color: white; border: none; padding: 8px 16px; border-radius: 5px; font-weight: bold; } QPushButton:hover { background: #ec7063; } """) close_btn = QPushButton("إغلاق") close_btn.clicked.connect(self.accept) QPushButton { background: #95a5a6; color: white; border: none; padding: 8px 16px; border-radius: 5px; font-weight: bold; } QPushButton:hover { background: #bdc3c7; } """) buttons_layout.addWidget(add_file_btn) buttons_layout.addWidget(view_file_btn) buttons_layout.addWidget(delete_file_btn) buttons_layout.addStretch() buttons_layout.addWidget(close_btn) layout.addLayout(buttons_layout) self.setLayout(layout) def load_attachments(self): self.attachments_list.clear() # هنا يمكن إضافة كود لتحميل المرفقات من قاعدة البيانات # في الوقت الحالي سنعرض رسالة توضيحية item = QListWidgetItem("لا توجد مرفقات حالياً - اضغط 'إضافة ملف' لإضافة مرفق جديد") item.setData(Qt.UserRole, None) self.attachments_list.addItem(item) def add_file(self): """إضافة ملف جديد""" file_path, _ = QFileDialog.getOpenFileName( self, "اختر ملف لإضافته", "", "جميع الملفات (*.*)" ) if file_path: file_name = os.path.basename(file_path) # إضافة الملف للقائمة item = QListWidgetItem(f" {file_name}") item.setData(Qt.UserRole, file_path) self.attachments_list.addItem(item) # إزالة الرسالة التوضيحية إذا كانت موجودة if self.attachments_list.count() > 1: first_item = self.attachments_list.item(0) if first_item.data(Qt.UserRole) is None: self.attachments_list.takeItem(0) show_info_message("تم", f"تم إضافة الملف: {file_name}") def view_file(self): current_item = self.attachments_list.currentItem() if not current_item: show_error_message("خطأ", "الرجاء اختيار ملف من القائمة") return file_path = current_item.data(Qt.UserRole) if not file_path: show_error_message("خطأ", "لا يوجد ملف محدد") return try: if platform.system() == 'Windows': os.startfile(file_path) elif platform.system() == 'Darwin': # macOS subprocess.call(['open', file_path]) else: # Linux subprocess.call(['xdg-open', file_path]) except Exception as e: show_error_message("خطأ", f"لا يمكن فتح الملف: {str(e)}") def delete_file(self): """حذف الملف المحدد""" current_item = self.attachments_list.currentItem() if not current_item: show_error_message("خطأ", "الرجاء اختيار ملف من القائمة") return file_path = current_item.data(Qt.UserRole) if not file_path: show_error_message("خطأ", "لا يوجد ملف محدد") return if show_confirmation_message("تأكيد الحذف", "هل أنت متأكد من حذف هذا المرفق؟"): row = self.attachments_list.row(current_item) self.attachments_list.takeItem(row) # إضافة الرسالة التوضيحية إذا لم تعد هناك مرفقات if self.attachments_list.count() == 0: item = QListWidgetItem("لا توجد مرفقات حالياً - اضغط 'إضافة ملف' لإضافة مرفق جديد") item.setData(Qt.UserRole, None) self.attachments_list.addItem(item) show_info_message("تم", "تم حذف المرفق بنجاح")